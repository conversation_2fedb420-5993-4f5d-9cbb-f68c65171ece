---
# https://vitepress.dev/reference/default-theme-home-page
layout: home

hero:
  name: "zyDocs"
  text: "道阻且长，行则将至"
  tagline: "不登高山，不知天之高也；不临深溪，不知地之厚也"
  image:
    src: /zy.png
    alt: zy
  prev: 'get'
  actions:
    - theme: brand
      text: 高级JavaScript指南
      link: /packages/js-doc/index.md
    - theme: alt
      text: 开始阅读
      link: /packages/js-doc/blob
    # - theme: alt
    #   text: API Examples
    #   link: /api-examples

features:
  - title: 高级JavaScript开发指南
    details: 深入探讨JavaScript高级特性、底层原理和最佳实践的完整教程
    link: /packages/js-doc/index.md
    icon: 🚀
    linkText: 开始学习
  - title: js杂谈
    details: javascript相关技术的笔记
    link: /packages/js-doc/blob
    icon: 📚
    linkText: 了解更多
  - title: 前端工程化
    details: 前端工程化相关的笔记
    link: /packages/engineer/npm
    icon: 🛠
    linkText: 了解更多
  - title: Vue
    details: vue相关技术的笔记
    link: /packages/vue/vueShapeFlag
    icon: 🎨
    linkText: 了解更多
  - title: React Native 教程
    details: 从零开始的移动应用开发完整指南
    link: /react-native-tutorial/README
    icon: 📱
    linkText: 开始学习
---

