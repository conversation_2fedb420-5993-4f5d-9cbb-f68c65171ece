# Node.js MCP Server 开发指南

## 概述

MCP（Model Context Protocol，模型上下文协议）是一种开放协议，旨在实现大型语言模型（LLM）应用与外部数据源、工具和服务之间的无缝集成。本指南将详细介绍如何使用Node.js开发MCP Server。

## 核心概念

### MCP架构组件

1. **Host（主机）**：AI应用程序，如IDE、聊天机器人等
2. **Client（客户端）**：主机与服务器之间的桥梁
3. **Server（服务器）**：提供外部数据和工具的组件
4. **Base Protocol（基础协议）**：定义通信规则和消息格式

### MCP Server的职责

- 提供工具（Tools）：可执行的函数
- 提供资源（Resources）：数据源或文件
- 提供提示模板（Prompts）：预定义的提示模板
- 处理订阅（Subscriptions）：实时数据更新

## 开发环境准备

### 安装依赖

```bash
# 创建新项目
mkdir my-mcp-server
cd my-mcp-server
npm init -y

# 安装MCP SDK
npm install @modelcontextprotocol/sdk

# 安装开发依赖
npm install --save-dev typescript @types/node nodemon
```

### 项目结构

```
my-mcp-server/
├── src/
│   ├── index.ts          # 主入口文件
│   ├── tools/            # 工具实现
│   ├── resources/        # 资源处理
│   └── types/            # 类型定义
├── package.json
├── tsconfig.json
└── README.md
```

## 基础MCP Server实现

### 1. 基本服务器结构

```typescript
// src/index.ts
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  Tool,
} from '@modelcontextprotocol/sdk/types.js';

class MyMCPServer {
  private server: Server;

  constructor() {
    this.server = new Server(
      {
        name: 'my-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
          resources: {},
          prompts: {},
        },
      }
    );

    this.setupHandlers();
  }

  private setupHandlers() {
    // 列出可用工具
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: this.getAvailableTools(),
      };
    });

    // 执行工具调用
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      return await this.handleToolCall(request);
    });
  }

  private getAvailableTools(): Tool[] {
    return [
      {
        name: 'get_weather',
        description: '获取指定城市的天气信息',
        inputSchema: {
          type: 'object',
          properties: {
            city: {
              type: 'string',
              description: '城市名称',
            },
            unit: {
              type: 'string',
              enum: ['celsius', 'fahrenheit'],
              description: '温度单位',
              default: 'celsius',
            },
          },
          required: ['city'],
        },
      },
      {
        name: 'calculate',
        description: '执行数学计算',
        inputSchema: {
          type: 'object',
          properties: {
            expression: {
              type: 'string',
              description: '数学表达式，如 "2 + 3 * 4"',
            },
          },
          required: ['expression'],
        },
      },
    ];
  }

  private async handleToolCall(request: any) {
    const { name, arguments: args } = request.params;

    switch (name) {
      case 'get_weather':
        return await this.getWeather(args.city, args.unit);
      case 'calculate':
        return await this.calculate(args.expression);
      default:
        throw new Error(`Unknown tool: ${name}`);
    }
  }

  private async getWeather(city: string, unit: string = 'celsius') {
    // 模拟天气API调用
    const weatherData = {
      city,
      temperature: unit === 'celsius' ? 22 : 72,
      unit,
      condition: '晴朗',
      humidity: 65,
      windSpeed: 10,
    };

    return {
      content: [
        {
          type: 'text',
          text: `${city}的天气信息：
温度: ${weatherData.temperature}°${unit === 'celsius' ? 'C' : 'F'}
天气: ${weatherData.condition}
湿度: ${weatherData.humidity}%
风速: ${weatherData.windSpeed} km/h`,
        },
      ],
    };
  }

  private async calculate(expression: string) {
    try {
      // 安全的数学表达式计算
      const result = this.safeEval(expression);
      
      return {
        content: [
          {
            type: 'text',
            text: `计算结果: ${expression} = ${result}`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `计算错误: ${error.message}`,
          },
        ],
        isError: true,
      };
    }
  }

  private safeEval(expression: string): number {
    // 简单的安全数学计算实现
    const sanitized = expression.replace(/[^0-9+\-*/().\s]/g, '');
    return Function(`"use strict"; return (${sanitized})`)();
  }

  async start() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('MCP Server started');
  }
}

// 启动服务器
const server = new MyMCPServer();
server.start().catch(console.error);
```

## 高级功能实现

### 2. 资源管理

```typescript
// src/resources/ResourceManager.ts
import {
  ListResourcesRequestSchema,
  ReadResourceRequestSchema,
  Resource,
} from '@modelcontextprotocol/sdk/types.js';

export class ResourceManager {
  private resources: Map<string, any> = new Map();

  constructor(private server: Server) {
    this.setupResourceHandlers();
    this.initializeResources();
  }

  private setupResourceHandlers() {
    // 列出可用资源
    this.server.setRequestHandler(ListResourcesRequestSchema, async () => {
      return {
        resources: Array.from(this.resources.keys()).map(uri => ({
          uri,
          name: this.getResourceName(uri),
          description: this.getResourceDescription(uri),
          mimeType: this.getResourceMimeType(uri),
        })),
      };
    });

    // 读取资源内容
    this.server.setRequestHandler(ReadResourceRequestSchema, async (request) => {
      const { uri } = request.params;
      const resource = this.resources.get(uri);
      
      if (!resource) {
        throw new Error(`Resource not found: ${uri}`);
      }

      return {
        contents: [
          {
            uri,
            mimeType: this.getResourceMimeType(uri),
            text: JSON.stringify(resource, null, 2),
          },
        ],
      };
    });
  }

  private initializeResources() {
    // 添加示例资源
    this.resources.set('config://app-settings', {
      theme: 'dark',
      language: 'zh-CN',
      notifications: true,
    });

    this.resources.set('data://user-preferences', {
      timezone: 'Asia/Shanghai',
      dateFormat: 'YYYY-MM-DD',
      currency: 'CNY',
    });
  }

  private getResourceName(uri: string): string {
    const names = {
      'config://app-settings': '应用设置',
      'data://user-preferences': '用户偏好',
    };
    return names[uri] || uri;
  }

  private getResourceDescription(uri: string): string {
    const descriptions = {
      'config://app-settings': '应用程序配置信息',
      'data://user-preferences': '用户个人偏好设置',
    };
    return descriptions[uri] || '资源描述';
  }

  private getResourceMimeType(uri: string): string {
    return 'application/json';
  }

  // 动态添加资源
  addResource(uri: string, data: any) {
    this.resources.set(uri, data);
  }

  // 更新资源
  updateResource(uri: string, data: any) {
    if (this.resources.has(uri)) {
      this.resources.set(uri, data);
      return true;
    }
    return false;
  }

  // 删除资源
  removeResource(uri: string) {
    return this.resources.delete(uri);
  }
}
```

### 3. 提示模板管理

```typescript
// src/prompts/PromptManager.ts
import {
  ListPromptsRequestSchema,
  GetPromptRequestSchema,
  Prompt,
} from '@modelcontextprotocol/sdk/types.js';

export class PromptManager {
  private prompts: Map<string, Prompt> = new Map();

  constructor(private server: Server) {
    this.setupPromptHandlers();
    this.initializePrompts();
  }

  private setupPromptHandlers() {
    // 列出可用提示模板
    this.server.setRequestHandler(ListPromptsRequestSchema, async () => {
      return {
        prompts: Array.from(this.prompts.values()),
      };
    });

    // 获取提示模板内容
    this.server.setRequestHandler(GetPromptRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;
      const prompt = this.prompts.get(name);
      
      if (!prompt) {
        throw new Error(`Prompt not found: ${name}`);
      }

      return {
        description: prompt.description,
        messages: this.generatePromptMessages(name, args || {}),
      };
    });
  }

  private initializePrompts() {
    this.prompts.set('code_review', {
      name: 'code_review',
      description: '代码审查提示模板',
      arguments: [
        {
          name: 'code',
          description: '要审查的代码',
          required: true,
        },
        {
          name: 'language',
          description: '编程语言',
          required: false,
        },
      ],
    });

    this.prompts.set('translate', {
      name: 'translate',
      description: '翻译提示模板',
      arguments: [
        {
          name: 'text',
          description: '要翻译的文本',
          required: true,
        },
        {
          name: 'target_language',
          description: '目标语言',
          required: true,
        },
        {
          name: 'source_language',
          description: '源语言',
          required: false,
        },
      ],
    });
  }

  private generatePromptMessages(name: string, args: Record<string, any>) {
    switch (name) {
      case 'code_review':
        return [
          {
            role: 'system',
            content: {
              type: 'text',
              text: '你是一个专业的代码审查员。请仔细审查提供的代码，指出潜在问题、改进建议和最佳实践。',
            },
          },
          {
            role: 'user',
            content: {
              type: 'text',
              text: `请审查以下${args.language || ''}代码：\n\n${args.code}`,
            },
          },
        ];

      case 'translate':
        return [
          {
            role: 'system',
            content: {
              type: 'text',
              text: '你是一个专业的翻译员。请准确翻译提供的文本，保持原意和语境。',
            },
          },
          {
            role: 'user',
            content: {
              type: 'text',
              text: `请将以下文本从${args.source_language || '自动检测语言'}翻译为${args.target_language}：\n\n${args.text}`,
            },
          },
        ];

      default:
        return [];
    }
  }

  // 动态添加提示模板
  addPrompt(prompt: Prompt) {
    this.prompts.set(prompt.name, prompt);
  }

  // 删除提示模板
  removePrompt(name: string) {
    return this.prompts.delete(name);
  }
}
```

## 实际应用示例

### 4. 文件系统MCP Server

```typescript
// src/tools/FileSystemTool.ts
import * as fs from 'fs/promises';
import * as path from 'path';

export class FileSystemTool {
  private allowedPaths: string[];

  constructor(allowedPaths: string[] = []) {
    this.allowedPaths = allowedPaths.length > 0 ? allowedPaths : [process.cwd()];
  }

  getTools() {
    return [
      {
        name: 'read_file',
        description: '读取文件内容',
        inputSchema: {
          type: 'object',
          properties: {
            path: {
              type: 'string',
              description: '文件路径',
            },
          },
          required: ['path'],
        },
      },
      {
        name: 'write_file',
        description: '写入文件内容',
        inputSchema: {
          type: 'object',
          properties: {
            path: {
              type: 'string',
              description: '文件路径',
            },
            content: {
              type: 'string',
              description: '文件内容',
            },
          },
          required: ['path', 'content'],
        },
      },
      {
        name: 'list_directory',
        description: '列出目录内容',
        inputSchema: {
          type: 'object',
          properties: {
            path: {
              type: 'string',
              description: '目录路径',
            },
          },
          required: ['path'],
        },
      },
    ];
  }

  async handleToolCall(name: string, args: any) {
    switch (name) {
      case 'read_file':
        return await this.readFile(args.path);
      case 'write_file':
        return await this.writeFile(args.path, args.content);
      case 'list_directory':
        return await this.listDirectory(args.path);
      default:
        throw new Error(`Unknown file system tool: ${name}`);
    }
  }

  private async readFile(filePath: string) {
    try {
      this.validatePath(filePath);
      const content = await fs.readFile(filePath, 'utf-8');
      
      return {
        content: [
          {
            type: 'text',
            text: `文件内容 (${filePath}):\n\n${content}`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `读取文件失败: ${error.message}`,
          },
        ],
        isError: true,
      };
    }
  }

  private async writeFile(filePath: string, content: string) {
    try {
      this.validatePath(filePath);
      await fs.writeFile(filePath, content, 'utf-8');
      
      return {
        content: [
          {
            type: 'text',
            text: `文件写入成功: ${filePath}`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `写入文件失败: ${error.message}`,
          },
        ],
        isError: true,
      };
    }
  }

  private async listDirectory(dirPath: string) {
    try {
      this.validatePath(dirPath);
      const items = await fs.readdir(dirPath, { withFileTypes: true });
      
      const fileList = items.map(item => ({
        name: item.name,
        type: item.isDirectory() ? 'directory' : 'file',
        path: path.join(dirPath, item.name),
      }));

      return {
        content: [
          {
            type: 'text',
            text: `目录内容 (${dirPath}):\n\n${JSON.stringify(fileList, null, 2)}`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `列出目录失败: ${error.message}`,
          },
        ],
        isError: true,
      };
    }
  }

  private validatePath(filePath: string) {
    const resolvedPath = path.resolve(filePath);
    const isAllowed = this.allowedPaths.some(allowedPath => 
      resolvedPath.startsWith(path.resolve(allowedPath))
    );
    
    if (!isAllowed) {
      throw new Error(`Access denied: ${filePath}`);
    }
  }
}
```

## 配置和部署

### 5. 配置文件

```json
// package.json
{
  "name": "my-mcp-server",
  "version": "1.0.0",
  "description": "Custom MCP Server built with Node.js",
  "main": "dist/index.js",
  "scripts": {
    "build": "tsc",
    "start": "node dist/index.js",
    "dev": "nodemon --exec ts-node src/index.ts",
    "test": "jest"
  },
  "dependencies": {
    "@modelcontextprotocol/sdk": "^0.4.0"
  },
  "devDependencies": {
    "@types/node": "^20.0.0",
    "typescript": "^5.0.0",
    "nodemon": "^3.0.0",
    "ts-node": "^10.0.0"
  }
}
```

```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "ESNext",
    "moduleResolution": "node",
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
```

### 6. MCP配置文件

```json
// mcp-config.json - 用于Cline等MCP客户端
{
  "mcpServers": {
    "my-mcp-server": {
      "command": "node",
      "args": ["dist/index.js"],
      "cwd": "/path/to/my-mcp-server"
    }
  }
}
```

## 最佳实践

### 错误处理
- 始终提供有意义的错误消息
- 使用适当的错误类型和状态码
- 实现优雅的降级机制

### 安全性
- 验证所有输入参数
- 限制文件系统访问权限
- 实现速率限制和资源限制

### 性能优化
- 使用异步操作避免阻塞
- 实现适当的缓存机制
- 优化大数据处理

### 可维护性
- 模块化代码结构
- 完善的类型定义
- 详细的文档和注释

## 总结

通过本指南，您已经学会了如何使用Node.js开发功能完整的MCP Server。MCP协议为AI应用提供了强大的扩展能力，让大型语言模型能够访问外部工具和数据源。

继续探索MCP生态系统，您可以开发更多专业化的工具，为AI应用提供更丰富的功能支持。
