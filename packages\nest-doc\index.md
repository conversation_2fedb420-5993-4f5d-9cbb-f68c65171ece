# Nest是什么
<div style="text-align: center;">
    <img style="display:block;margin:auto" src="https://docs.nestjs.com/assets/logo-small-gradient.svg" alt="Nest" width="20%" height="10%">
</div>

Nest（NestJS）是一个用于构建高效、可扩展的 Node.js 服务器端应用程序的框架。它使用渐进式 JavaScript，基于 TypeScript 构建并完全支持 TypeScript（同时仍然允许开发者在纯 JavaScript 中编写代码），并结合了面向对象编程（OOP）、函数式编程（FP）和函数式响应式编程（FRP）的元素。

Nest官网:   
::: tip
https://docs.nestjs.com/
:::

Nest基于[Express](https://expressjs.com/)或[Fastify](https://fastify.dev/)等底层框架，提供了一套完整的解决方案，`包括路由`、`控制器`、`中间件`、`依赖注入`、`模块化`、`异常处理`等。它还支持TypeScript的类型检查和自动补全，提高了开发效率和代码质量。