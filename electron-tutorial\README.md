# Electron 实战教程

> 从零开始学习 Electron 桌面应用开发，掌握跨平台桌面应用开发的核心技能

## 教程简介

本教程将带你深入学习 Electron 框架，从基础概念到实战项目，全面掌握桌面应用开发技能。作为一名高级前端开发者，我将分享在 Electron 开发中的实践经验和最佳实践。

### 你将学到什么

- Electron 的核心架构和工作原理
- 主进程与渲染进程的通信机制
- 窗口管理和用户界面开发
- 原生 API 的使用和系统集成
- 应用打包、分发和性能优化
- 安全最佳实践和常见问题解决
- 完整的实战项目开发

### 适合人群

- 有一定 JavaScript/Node.js 基础的开发者
- 想要学习桌面应用开发的前端工程师
- 希望将 Web 技术应用到桌面端的开发者
- 对跨平台应用开发感兴趣的程序员

## 教程目录

### [第1章：Electron 基础入门](./chapter-01/README.md)
- Electron 是什么
- 架构原理和优势
- 开发环境搭建
- 第一个 Hello World 应用

### [第2章：主进程与渲染进程](./chapter-02/README.md)
- 进程模型详解
- 主进程的职责和 API
- 渲染进程的特点
- 进程生命周期管理

### [第3章：窗口管理与 BrowserWindow](./chapter-03/README.md)
- BrowserWindow API 详解
- 窗口创建和配置
- 多窗口管理
- 窗口状态控制

### [第4章：进程间通信(IPC)](./chapter-04/README.md)
- IPC 通信机制
- ipcMain 和 ipcRenderer
- 同步与异步通信
- 数据传递最佳实践

### [第5章：菜单与快捷键](./chapter-05/README.md)
- 应用菜单创建
- 上下文菜单
- 全局快捷键
- 系统托盘

### [第6章：文件系统与对话框](./chapter-06/README.md)
- 文件操作 API
- 文件选择对话框
- 系统通知
- 剪贴板操作

### [第7章：应用打包与分发](./chapter-07/README.md)
- electron-builder 配置
- 多平台打包
- 代码签名
- 自动更新机制

### [第8章：性能优化与安全](./chapter-08/README.md)
- 性能优化策略
- 内存管理
- 安全最佳实践
- 常见问题排查

### [第9章：实战项目：桌面笔记应用](./chapter-09/README.md)
- 项目需求分析
- 架构设计
- 功能实现
- 测试与部署

## 学习建议

1. **循序渐进**：按照章节顺序学习，每章都有实践代码
2. **动手实践**：跟着教程编写代码，理论结合实践
3. **深入思考**：理解每个概念背后的原理和设计思想
4. **扩展学习**：尝试修改示例代码，探索更多可能性

## 开发环境要求

- Node.js 16+ 
- npm 或 yarn 包管理器
- 代码编辑器（推荐 VS Code）
- Git 版本控制

## 相关资源

- [Electron 官方文档](https://www.electronjs.org/docs)
- [Electron API 参考](https://www.electronjs.org/docs/api)
- [示例代码仓库](https://github.com/electron/electron-quick-start)

## 作者介绍

作为一名高级前端开发工程师，我在 Electron 开发领域有着丰富的实战经验，参与过多个大型桌面应用项目的开发。本教程结合了我在实际项目中的经验和最佳实践，希望能帮助你快速掌握 Electron 开发技能。

---

让我们开始这段 Electron 学习之旅吧！🚀
