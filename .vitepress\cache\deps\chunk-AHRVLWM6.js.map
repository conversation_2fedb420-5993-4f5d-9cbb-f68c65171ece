{"version": 3, "sources": ["../../../node_modules/.pnpm/@vueuse+shared@12.8.2/node_modules/@vueuse/shared/index.mjs", "../../../node_modules/.pnpm/@vueuse+core@12.8.2/node_modules/@vueuse/core/index.mjs"], "sourcesContent": ["import { shallowRef, watchEffect, readonly, watch, customRef, getCurrentScope, onScopeDispose, effectScope, getCurrentInstance, hasInjectionContext, inject, provide, ref, isRef, unref, toValue as toValue$1, computed, reactive, toRefs as toRefs$1, toRef as toRef$1, onBeforeMount, nextTick, onBeforeUnmount, onMounted, onUnmounted, isReactive } from 'vue';\n\nfunction computedEager(fn, options) {\n  var _a;\n  const result = shallowRef();\n  watchEffect(() => {\n    result.value = fn();\n  }, {\n    ...options,\n    flush: (_a = options == null ? void 0 : options.flush) != null ? _a : \"sync\"\n  });\n  return readonly(result);\n}\n\nfunction computedWithControl(source, fn) {\n  let v = void 0;\n  let track;\n  let trigger;\n  const dirty = shallowRef(true);\n  const update = () => {\n    dirty.value = true;\n    trigger();\n  };\n  watch(source, update, { flush: \"sync\" });\n  const get = typeof fn === \"function\" ? fn : fn.get;\n  const set = typeof fn === \"function\" ? void 0 : fn.set;\n  const result = customRef((_track, _trigger) => {\n    track = _track;\n    trigger = _trigger;\n    return {\n      get() {\n        if (dirty.value) {\n          v = get(v);\n          dirty.value = false;\n        }\n        track();\n        return v;\n      },\n      set(v2) {\n        set == null ? void 0 : set(v2);\n      }\n    };\n  });\n  if (Object.isExtensible(result))\n    result.trigger = update;\n  return result;\n}\n\nfunction tryOnScopeDispose(fn) {\n  if (getCurrentScope()) {\n    onScopeDispose(fn);\n    return true;\n  }\n  return false;\n}\n\nfunction createEventHook() {\n  const fns = /* @__PURE__ */ new Set();\n  const off = (fn) => {\n    fns.delete(fn);\n  };\n  const clear = () => {\n    fns.clear();\n  };\n  const on = (fn) => {\n    fns.add(fn);\n    const offFn = () => off(fn);\n    tryOnScopeDispose(offFn);\n    return {\n      off: offFn\n    };\n  };\n  const trigger = (...args) => {\n    return Promise.all(Array.from(fns).map((fn) => fn(...args)));\n  };\n  return {\n    on,\n    off,\n    trigger,\n    clear\n  };\n}\n\nfunction createGlobalState(stateFactory) {\n  let initialized = false;\n  let state;\n  const scope = effectScope(true);\n  return (...args) => {\n    if (!initialized) {\n      state = scope.run(() => stateFactory(...args));\n      initialized = true;\n    }\n    return state;\n  };\n}\n\nconst localProvidedStateMap = /* @__PURE__ */ new WeakMap();\n\nconst injectLocal = (...args) => {\n  var _a;\n  const key = args[0];\n  const instance = (_a = getCurrentInstance()) == null ? void 0 : _a.proxy;\n  if (instance == null && !hasInjectionContext())\n    throw new Error(\"injectLocal must be called in setup\");\n  if (instance && localProvidedStateMap.has(instance) && key in localProvidedStateMap.get(instance))\n    return localProvidedStateMap.get(instance)[key];\n  return inject(...args);\n};\n\nconst provideLocal = (key, value) => {\n  var _a;\n  const instance = (_a = getCurrentInstance()) == null ? void 0 : _a.proxy;\n  if (instance == null)\n    throw new Error(\"provideLocal must be called in setup\");\n  if (!localProvidedStateMap.has(instance))\n    localProvidedStateMap.set(instance, /* @__PURE__ */ Object.create(null));\n  const localProvidedState = localProvidedStateMap.get(instance);\n  localProvidedState[key] = value;\n  provide(key, value);\n};\n\nfunction createInjectionState(composable, options) {\n  const key = (options == null ? void 0 : options.injectionKey) || Symbol(composable.name || \"InjectionState\");\n  const defaultValue = options == null ? void 0 : options.defaultValue;\n  const useProvidingState = (...args) => {\n    const state = composable(...args);\n    provideLocal(key, state);\n    return state;\n  };\n  const useInjectedState = () => injectLocal(key, defaultValue);\n  return [useProvidingState, useInjectedState];\n}\n\nfunction createRef(value, deep) {\n  if (deep === true) {\n    return ref(value);\n  } else {\n    return shallowRef(value);\n  }\n}\n\nfunction createSharedComposable(composable) {\n  let subscribers = 0;\n  let state;\n  let scope;\n  const dispose = () => {\n    subscribers -= 1;\n    if (scope && subscribers <= 0) {\n      scope.stop();\n      state = void 0;\n      scope = void 0;\n    }\n  };\n  return (...args) => {\n    subscribers += 1;\n    if (!scope) {\n      scope = effectScope(true);\n      state = scope.run(() => composable(...args));\n    }\n    tryOnScopeDispose(dispose);\n    return state;\n  };\n}\n\nfunction extendRef(ref, extend, { enumerable = false, unwrap = true } = {}) {\n  for (const [key, value] of Object.entries(extend)) {\n    if (key === \"value\")\n      continue;\n    if (isRef(value) && unwrap) {\n      Object.defineProperty(ref, key, {\n        get() {\n          return value.value;\n        },\n        set(v) {\n          value.value = v;\n        },\n        enumerable\n      });\n    } else {\n      Object.defineProperty(ref, key, { value, enumerable });\n    }\n  }\n  return ref;\n}\n\nfunction get(obj, key) {\n  if (key == null)\n    return unref(obj);\n  return unref(obj)[key];\n}\n\nfunction isDefined(v) {\n  return unref(v) != null;\n}\n\nfunction makeDestructurable(obj, arr) {\n  if (typeof Symbol !== \"undefined\") {\n    const clone = { ...obj };\n    Object.defineProperty(clone, Symbol.iterator, {\n      enumerable: false,\n      value() {\n        let index = 0;\n        return {\n          next: () => ({\n            value: arr[index++],\n            done: index > arr.length\n          })\n        };\n      }\n    });\n    return clone;\n  } else {\n    return Object.assign([...arr], obj);\n  }\n}\n\nfunction reactify(fn, options) {\n  const unrefFn = (options == null ? void 0 : options.computedGetter) === false ? unref : toValue$1;\n  return function(...args) {\n    return computed(() => fn.apply(this, args.map((i) => unrefFn(i))));\n  };\n}\n\nfunction reactifyObject(obj, optionsOrKeys = {}) {\n  let keys = [];\n  let options;\n  if (Array.isArray(optionsOrKeys)) {\n    keys = optionsOrKeys;\n  } else {\n    options = optionsOrKeys;\n    const { includeOwnProperties = true } = optionsOrKeys;\n    keys.push(...Object.keys(obj));\n    if (includeOwnProperties)\n      keys.push(...Object.getOwnPropertyNames(obj));\n  }\n  return Object.fromEntries(\n    keys.map((key) => {\n      const value = obj[key];\n      return [\n        key,\n        typeof value === \"function\" ? reactify(value.bind(obj), options) : value\n      ];\n    })\n  );\n}\n\nfunction toReactive(objectRef) {\n  if (!isRef(objectRef))\n    return reactive(objectRef);\n  const proxy = new Proxy({}, {\n    get(_, p, receiver) {\n      return unref(Reflect.get(objectRef.value, p, receiver));\n    },\n    set(_, p, value) {\n      if (isRef(objectRef.value[p]) && !isRef(value))\n        objectRef.value[p].value = value;\n      else\n        objectRef.value[p] = value;\n      return true;\n    },\n    deleteProperty(_, p) {\n      return Reflect.deleteProperty(objectRef.value, p);\n    },\n    has(_, p) {\n      return Reflect.has(objectRef.value, p);\n    },\n    ownKeys() {\n      return Object.keys(objectRef.value);\n    },\n    getOwnPropertyDescriptor() {\n      return {\n        enumerable: true,\n        configurable: true\n      };\n    }\n  });\n  return reactive(proxy);\n}\n\nfunction reactiveComputed(fn) {\n  return toReactive(computed(fn));\n}\n\nfunction reactiveOmit(obj, ...keys) {\n  const flatKeys = keys.flat();\n  const predicate = flatKeys[0];\n  return reactiveComputed(() => typeof predicate === \"function\" ? Object.fromEntries(Object.entries(toRefs$1(obj)).filter(([k, v]) => !predicate(toValue$1(v), k))) : Object.fromEntries(Object.entries(toRefs$1(obj)).filter((e) => !flatKeys.includes(e[0]))));\n}\n\nconst isClient = typeof window !== \"undefined\" && typeof document !== \"undefined\";\nconst isWorker = typeof WorkerGlobalScope !== \"undefined\" && globalThis instanceof WorkerGlobalScope;\nconst isDef = (val) => typeof val !== \"undefined\";\nconst notNullish = (val) => val != null;\nconst assert = (condition, ...infos) => {\n  if (!condition)\n    console.warn(...infos);\n};\nconst toString = Object.prototype.toString;\nconst isObject = (val) => toString.call(val) === \"[object Object]\";\nconst now = () => Date.now();\nconst timestamp = () => +Date.now();\nconst clamp = (n, min, max) => Math.min(max, Math.max(min, n));\nconst noop = () => {\n};\nconst rand = (min, max) => {\n  min = Math.ceil(min);\n  max = Math.floor(max);\n  return Math.floor(Math.random() * (max - min + 1)) + min;\n};\nconst hasOwn = (val, key) => Object.prototype.hasOwnProperty.call(val, key);\nconst isIOS = /* @__PURE__ */ getIsIOS();\nfunction getIsIOS() {\n  var _a, _b;\n  return isClient && ((_a = window == null ? void 0 : window.navigator) == null ? void 0 : _a.userAgent) && (/iP(?:ad|hone|od)/.test(window.navigator.userAgent) || ((_b = window == null ? void 0 : window.navigator) == null ? void 0 : _b.maxTouchPoints) > 2 && /iPad|Macintosh/.test(window == null ? void 0 : window.navigator.userAgent));\n}\n\nfunction createFilterWrapper(filter, fn) {\n  function wrapper(...args) {\n    return new Promise((resolve, reject) => {\n      Promise.resolve(filter(() => fn.apply(this, args), { fn, thisArg: this, args })).then(resolve).catch(reject);\n    });\n  }\n  return wrapper;\n}\nconst bypassFilter = (invoke) => {\n  return invoke();\n};\nfunction debounceFilter(ms, options = {}) {\n  let timer;\n  let maxTimer;\n  let lastRejector = noop;\n  const _clearTimeout = (timer2) => {\n    clearTimeout(timer2);\n    lastRejector();\n    lastRejector = noop;\n  };\n  let lastInvoker;\n  const filter = (invoke) => {\n    const duration = toValue$1(ms);\n    const maxDuration = toValue$1(options.maxWait);\n    if (timer)\n      _clearTimeout(timer);\n    if (duration <= 0 || maxDuration !== void 0 && maxDuration <= 0) {\n      if (maxTimer) {\n        _clearTimeout(maxTimer);\n        maxTimer = null;\n      }\n      return Promise.resolve(invoke());\n    }\n    return new Promise((resolve, reject) => {\n      lastRejector = options.rejectOnCancel ? reject : resolve;\n      lastInvoker = invoke;\n      if (maxDuration && !maxTimer) {\n        maxTimer = setTimeout(() => {\n          if (timer)\n            _clearTimeout(timer);\n          maxTimer = null;\n          resolve(lastInvoker());\n        }, maxDuration);\n      }\n      timer = setTimeout(() => {\n        if (maxTimer)\n          _clearTimeout(maxTimer);\n        maxTimer = null;\n        resolve(invoke());\n      }, duration);\n    });\n  };\n  return filter;\n}\nfunction throttleFilter(...args) {\n  let lastExec = 0;\n  let timer;\n  let isLeading = true;\n  let lastRejector = noop;\n  let lastValue;\n  let ms;\n  let trailing;\n  let leading;\n  let rejectOnCancel;\n  if (!isRef(args[0]) && typeof args[0] === \"object\")\n    ({ delay: ms, trailing = true, leading = true, rejectOnCancel = false } = args[0]);\n  else\n    [ms, trailing = true, leading = true, rejectOnCancel = false] = args;\n  const clear = () => {\n    if (timer) {\n      clearTimeout(timer);\n      timer = void 0;\n      lastRejector();\n      lastRejector = noop;\n    }\n  };\n  const filter = (_invoke) => {\n    const duration = toValue$1(ms);\n    const elapsed = Date.now() - lastExec;\n    const invoke = () => {\n      return lastValue = _invoke();\n    };\n    clear();\n    if (duration <= 0) {\n      lastExec = Date.now();\n      return invoke();\n    }\n    if (elapsed > duration && (leading || !isLeading)) {\n      lastExec = Date.now();\n      invoke();\n    } else if (trailing) {\n      lastValue = new Promise((resolve, reject) => {\n        lastRejector = rejectOnCancel ? reject : resolve;\n        timer = setTimeout(() => {\n          lastExec = Date.now();\n          isLeading = true;\n          resolve(invoke());\n          clear();\n        }, Math.max(0, duration - elapsed));\n      });\n    }\n    if (!leading && !timer)\n      timer = setTimeout(() => isLeading = true, duration);\n    isLeading = false;\n    return lastValue;\n  };\n  return filter;\n}\nfunction pausableFilter(extendFilter = bypassFilter, options = {}) {\n  const {\n    initialState = \"active\"\n  } = options;\n  const isActive = toRef(initialState === \"active\");\n  function pause() {\n    isActive.value = false;\n  }\n  function resume() {\n    isActive.value = true;\n  }\n  const eventFilter = (...args) => {\n    if (isActive.value)\n      extendFilter(...args);\n  };\n  return { isActive: readonly(isActive), pause, resume, eventFilter };\n}\n\nfunction cacheStringFunction(fn) {\n  const cache = /* @__PURE__ */ Object.create(null);\n  return (str) => {\n    const hit = cache[str];\n    return hit || (cache[str] = fn(str));\n  };\n}\nconst hyphenateRE = /\\B([A-Z])/g;\nconst hyphenate = cacheStringFunction((str) => str.replace(hyphenateRE, \"-$1\").toLowerCase());\nconst camelizeRE = /-(\\w)/g;\nconst camelize = cacheStringFunction((str) => {\n  return str.replace(camelizeRE, (_, c) => c ? c.toUpperCase() : \"\");\n});\n\nfunction promiseTimeout(ms, throwOnTimeout = false, reason = \"Timeout\") {\n  return new Promise((resolve, reject) => {\n    if (throwOnTimeout)\n      setTimeout(() => reject(reason), ms);\n    else\n      setTimeout(resolve, ms);\n  });\n}\nfunction identity(arg) {\n  return arg;\n}\nfunction createSingletonPromise(fn) {\n  let _promise;\n  function wrapper() {\n    if (!_promise)\n      _promise = fn();\n    return _promise;\n  }\n  wrapper.reset = async () => {\n    const _prev = _promise;\n    _promise = void 0;\n    if (_prev)\n      await _prev;\n  };\n  return wrapper;\n}\nfunction invoke(fn) {\n  return fn();\n}\nfunction containsProp(obj, ...props) {\n  return props.some((k) => k in obj);\n}\nfunction increaseWithUnit(target, delta) {\n  var _a;\n  if (typeof target === \"number\")\n    return target + delta;\n  const value = ((_a = target.match(/^-?\\d+\\.?\\d*/)) == null ? void 0 : _a[0]) || \"\";\n  const unit = target.slice(value.length);\n  const result = Number.parseFloat(value) + delta;\n  if (Number.isNaN(result))\n    return target;\n  return result + unit;\n}\nfunction pxValue(px) {\n  return px.endsWith(\"rem\") ? Number.parseFloat(px) * 16 : Number.parseFloat(px);\n}\nfunction objectPick(obj, keys, omitUndefined = false) {\n  return keys.reduce((n, k) => {\n    if (k in obj) {\n      if (!omitUndefined || obj[k] !== void 0)\n        n[k] = obj[k];\n    }\n    return n;\n  }, {});\n}\nfunction objectOmit(obj, keys, omitUndefined = false) {\n  return Object.fromEntries(Object.entries(obj).filter(([key, value]) => {\n    return (!omitUndefined || value !== void 0) && !keys.includes(key);\n  }));\n}\nfunction objectEntries(obj) {\n  return Object.entries(obj);\n}\nfunction getLifeCycleTarget(target) {\n  return target || getCurrentInstance();\n}\nfunction toArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\n\nfunction toRef(...args) {\n  if (args.length !== 1)\n    return toRef$1(...args);\n  const r = args[0];\n  return typeof r === \"function\" ? readonly(customRef(() => ({ get: r, set: noop }))) : ref(r);\n}\nconst resolveRef = toRef;\n\nfunction reactivePick(obj, ...keys) {\n  const flatKeys = keys.flat();\n  const predicate = flatKeys[0];\n  return reactiveComputed(() => typeof predicate === \"function\" ? Object.fromEntries(Object.entries(toRefs$1(obj)).filter(([k, v]) => predicate(toValue$1(v), k))) : Object.fromEntries(flatKeys.map((k) => [k, toRef(obj, k)])));\n}\n\nfunction refAutoReset(defaultValue, afterMs = 1e4) {\n  return customRef((track, trigger) => {\n    let value = toValue$1(defaultValue);\n    let timer;\n    const resetAfter = () => setTimeout(() => {\n      value = toValue$1(defaultValue);\n      trigger();\n    }, toValue$1(afterMs));\n    tryOnScopeDispose(() => {\n      clearTimeout(timer);\n    });\n    return {\n      get() {\n        track();\n        return value;\n      },\n      set(newValue) {\n        value = newValue;\n        trigger();\n        clearTimeout(timer);\n        timer = resetAfter();\n      }\n    };\n  });\n}\n\nfunction useDebounceFn(fn, ms = 200, options = {}) {\n  return createFilterWrapper(\n    debounceFilter(ms, options),\n    fn\n  );\n}\n\nfunction refDebounced(value, ms = 200, options = {}) {\n  const debounced = ref(value.value);\n  const updater = useDebounceFn(() => {\n    debounced.value = value.value;\n  }, ms, options);\n  watch(value, () => updater());\n  return debounced;\n}\n\nfunction refDefault(source, defaultValue) {\n  return computed({\n    get() {\n      var _a;\n      return (_a = source.value) != null ? _a : defaultValue;\n    },\n    set(value) {\n      source.value = value;\n    }\n  });\n}\n\nfunction useThrottleFn(fn, ms = 200, trailing = false, leading = true, rejectOnCancel = false) {\n  return createFilterWrapper(\n    throttleFilter(ms, trailing, leading, rejectOnCancel),\n    fn\n  );\n}\n\nfunction refThrottled(value, delay = 200, trailing = true, leading = true) {\n  if (delay <= 0)\n    return value;\n  const throttled = ref(value.value);\n  const updater = useThrottleFn(() => {\n    throttled.value = value.value;\n  }, delay, trailing, leading);\n  watch(value, () => updater());\n  return throttled;\n}\n\nfunction refWithControl(initial, options = {}) {\n  let source = initial;\n  let track;\n  let trigger;\n  const ref = customRef((_track, _trigger) => {\n    track = _track;\n    trigger = _trigger;\n    return {\n      get() {\n        return get();\n      },\n      set(v) {\n        set(v);\n      }\n    };\n  });\n  function get(tracking = true) {\n    if (tracking)\n      track();\n    return source;\n  }\n  function set(value, triggering = true) {\n    var _a, _b;\n    if (value === source)\n      return;\n    const old = source;\n    if (((_a = options.onBeforeChange) == null ? void 0 : _a.call(options, value, old)) === false)\n      return;\n    source = value;\n    (_b = options.onChanged) == null ? void 0 : _b.call(options, value, old);\n    if (triggering)\n      trigger();\n  }\n  const untrackedGet = () => get(false);\n  const silentSet = (v) => set(v, false);\n  const peek = () => get(false);\n  const lay = (v) => set(v, false);\n  return extendRef(\n    ref,\n    {\n      get,\n      set,\n      untrackedGet,\n      silentSet,\n      peek,\n      lay\n    },\n    { enumerable: true }\n  );\n}\nconst controlledRef = refWithControl;\n\nfunction set(...args) {\n  if (args.length === 2) {\n    const [ref, value] = args;\n    ref.value = value;\n  }\n  if (args.length === 3) {\n    const [target, key, value] = args;\n    target[key] = value;\n  }\n}\n\nfunction watchWithFilter(source, cb, options = {}) {\n  const {\n    eventFilter = bypassFilter,\n    ...watchOptions\n  } = options;\n  return watch(\n    source,\n    createFilterWrapper(\n      eventFilter,\n      cb\n    ),\n    watchOptions\n  );\n}\n\nfunction watchPausable(source, cb, options = {}) {\n  const {\n    eventFilter: filter,\n    initialState = \"active\",\n    ...watchOptions\n  } = options;\n  const { eventFilter, pause, resume, isActive } = pausableFilter(filter, { initialState });\n  const stop = watchWithFilter(\n    source,\n    cb,\n    {\n      ...watchOptions,\n      eventFilter\n    }\n  );\n  return { stop, pause, resume, isActive };\n}\n\nfunction syncRef(left, right, ...[options]) {\n  const {\n    flush = \"sync\",\n    deep = false,\n    immediate = true,\n    direction = \"both\",\n    transform = {}\n  } = options || {};\n  const watchers = [];\n  const transformLTR = \"ltr\" in transform && transform.ltr || ((v) => v);\n  const transformRTL = \"rtl\" in transform && transform.rtl || ((v) => v);\n  if (direction === \"both\" || direction === \"ltr\") {\n    watchers.push(watchPausable(\n      left,\n      (newValue) => {\n        watchers.forEach((w) => w.pause());\n        right.value = transformLTR(newValue);\n        watchers.forEach((w) => w.resume());\n      },\n      { flush, deep, immediate }\n    ));\n  }\n  if (direction === \"both\" || direction === \"rtl\") {\n    watchers.push(watchPausable(\n      right,\n      (newValue) => {\n        watchers.forEach((w) => w.pause());\n        left.value = transformRTL(newValue);\n        watchers.forEach((w) => w.resume());\n      },\n      { flush, deep, immediate }\n    ));\n  }\n  const stop = () => {\n    watchers.forEach((w) => w.stop());\n  };\n  return stop;\n}\n\nfunction syncRefs(source, targets, options = {}) {\n  const {\n    flush = \"sync\",\n    deep = false,\n    immediate = true\n  } = options;\n  const targetsArray = toArray(targets);\n  return watch(\n    source,\n    (newValue) => targetsArray.forEach((target) => target.value = newValue),\n    { flush, deep, immediate }\n  );\n}\n\nfunction toRefs(objectRef, options = {}) {\n  if (!isRef(objectRef))\n    return toRefs$1(objectRef);\n  const result = Array.isArray(objectRef.value) ? Array.from({ length: objectRef.value.length }) : {};\n  for (const key in objectRef.value) {\n    result[key] = customRef(() => ({\n      get() {\n        return objectRef.value[key];\n      },\n      set(v) {\n        var _a;\n        const replaceRef = (_a = toValue$1(options.replaceRef)) != null ? _a : true;\n        if (replaceRef) {\n          if (Array.isArray(objectRef.value)) {\n            const copy = [...objectRef.value];\n            copy[key] = v;\n            objectRef.value = copy;\n          } else {\n            const newObject = { ...objectRef.value, [key]: v };\n            Object.setPrototypeOf(newObject, Object.getPrototypeOf(objectRef.value));\n            objectRef.value = newObject;\n          }\n        } else {\n          objectRef.value[key] = v;\n        }\n      }\n    }));\n  }\n  return result;\n}\n\nconst toValue = toValue$1;\nconst resolveUnref = toValue$1;\n\nfunction tryOnBeforeMount(fn, sync = true, target) {\n  const instance = getLifeCycleTarget(target);\n  if (instance)\n    onBeforeMount(fn, target);\n  else if (sync)\n    fn();\n  else\n    nextTick(fn);\n}\n\nfunction tryOnBeforeUnmount(fn, target) {\n  const instance = getLifeCycleTarget(target);\n  if (instance)\n    onBeforeUnmount(fn, target);\n}\n\nfunction tryOnMounted(fn, sync = true, target) {\n  const instance = getLifeCycleTarget();\n  if (instance)\n    onMounted(fn, target);\n  else if (sync)\n    fn();\n  else\n    nextTick(fn);\n}\n\nfunction tryOnUnmounted(fn, target) {\n  const instance = getLifeCycleTarget(target);\n  if (instance)\n    onUnmounted(fn, target);\n}\n\nfunction createUntil(r, isNot = false) {\n  function toMatch(condition, { flush = \"sync\", deep = false, timeout, throwOnTimeout } = {}) {\n    let stop = null;\n    const watcher = new Promise((resolve) => {\n      stop = watch(\n        r,\n        (v) => {\n          if (condition(v) !== isNot) {\n            if (stop)\n              stop();\n            else\n              nextTick(() => stop == null ? void 0 : stop());\n            resolve(v);\n          }\n        },\n        {\n          flush,\n          deep,\n          immediate: true\n        }\n      );\n    });\n    const promises = [watcher];\n    if (timeout != null) {\n      promises.push(\n        promiseTimeout(timeout, throwOnTimeout).then(() => toValue$1(r)).finally(() => stop == null ? void 0 : stop())\n      );\n    }\n    return Promise.race(promises);\n  }\n  function toBe(value, options) {\n    if (!isRef(value))\n      return toMatch((v) => v === value, options);\n    const { flush = \"sync\", deep = false, timeout, throwOnTimeout } = options != null ? options : {};\n    let stop = null;\n    const watcher = new Promise((resolve) => {\n      stop = watch(\n        [r, value],\n        ([v1, v2]) => {\n          if (isNot !== (v1 === v2)) {\n            if (stop)\n              stop();\n            else\n              nextTick(() => stop == null ? void 0 : stop());\n            resolve(v1);\n          }\n        },\n        {\n          flush,\n          deep,\n          immediate: true\n        }\n      );\n    });\n    const promises = [watcher];\n    if (timeout != null) {\n      promises.push(\n        promiseTimeout(timeout, throwOnTimeout).then(() => toValue$1(r)).finally(() => {\n          stop == null ? void 0 : stop();\n          return toValue$1(r);\n        })\n      );\n    }\n    return Promise.race(promises);\n  }\n  function toBeTruthy(options) {\n    return toMatch((v) => Boolean(v), options);\n  }\n  function toBeNull(options) {\n    return toBe(null, options);\n  }\n  function toBeUndefined(options) {\n    return toBe(void 0, options);\n  }\n  function toBeNaN(options) {\n    return toMatch(Number.isNaN, options);\n  }\n  function toContains(value, options) {\n    return toMatch((v) => {\n      const array = Array.from(v);\n      return array.includes(value) || array.includes(toValue$1(value));\n    }, options);\n  }\n  function changed(options) {\n    return changedTimes(1, options);\n  }\n  function changedTimes(n = 1, options) {\n    let count = -1;\n    return toMatch(() => {\n      count += 1;\n      return count >= n;\n    }, options);\n  }\n  if (Array.isArray(toValue$1(r))) {\n    const instance = {\n      toMatch,\n      toContains,\n      changed,\n      changedTimes,\n      get not() {\n        return createUntil(r, !isNot);\n      }\n    };\n    return instance;\n  } else {\n    const instance = {\n      toMatch,\n      toBe,\n      toBeTruthy,\n      toBeNull,\n      toBeNaN,\n      toBeUndefined,\n      changed,\n      changedTimes,\n      get not() {\n        return createUntil(r, !isNot);\n      }\n    };\n    return instance;\n  }\n}\nfunction until(r) {\n  return createUntil(r);\n}\n\nfunction defaultComparator(value, othVal) {\n  return value === othVal;\n}\nfunction useArrayDifference(...args) {\n  var _a, _b;\n  const list = args[0];\n  const values = args[1];\n  let compareFn = (_a = args[2]) != null ? _a : defaultComparator;\n  const {\n    symmetric = false\n  } = (_b = args[3]) != null ? _b : {};\n  if (typeof compareFn === \"string\") {\n    const key = compareFn;\n    compareFn = (value, othVal) => value[key] === othVal[key];\n  }\n  const diff1 = computed(() => toValue$1(list).filter((x) => toValue$1(values).findIndex((y) => compareFn(x, y)) === -1));\n  if (symmetric) {\n    const diff2 = computed(() => toValue$1(values).filter((x) => toValue$1(list).findIndex((y) => compareFn(x, y)) === -1));\n    return computed(() => symmetric ? [...toValue$1(diff1), ...toValue$1(diff2)] : toValue$1(diff1));\n  } else {\n    return diff1;\n  }\n}\n\nfunction useArrayEvery(list, fn) {\n  return computed(() => toValue$1(list).every((element, index, array) => fn(toValue$1(element), index, array)));\n}\n\nfunction useArrayFilter(list, fn) {\n  return computed(() => toValue$1(list).map((i) => toValue$1(i)).filter(fn));\n}\n\nfunction useArrayFind(list, fn) {\n  return computed(() => toValue$1(\n    toValue$1(list).find((element, index, array) => fn(toValue$1(element), index, array))\n  ));\n}\n\nfunction useArrayFindIndex(list, fn) {\n  return computed(() => toValue$1(list).findIndex((element, index, array) => fn(toValue$1(element), index, array)));\n}\n\nfunction findLast(arr, cb) {\n  let index = arr.length;\n  while (index-- > 0) {\n    if (cb(arr[index], index, arr))\n      return arr[index];\n  }\n  return void 0;\n}\nfunction useArrayFindLast(list, fn) {\n  return computed(() => toValue$1(\n    !Array.prototype.findLast ? findLast(toValue$1(list), (element, index, array) => fn(toValue$1(element), index, array)) : toValue$1(list).findLast((element, index, array) => fn(toValue$1(element), index, array))\n  ));\n}\n\nfunction isArrayIncludesOptions(obj) {\n  return isObject(obj) && containsProp(obj, \"formIndex\", \"comparator\");\n}\nfunction useArrayIncludes(...args) {\n  var _a;\n  const list = args[0];\n  const value = args[1];\n  let comparator = args[2];\n  let formIndex = 0;\n  if (isArrayIncludesOptions(comparator)) {\n    formIndex = (_a = comparator.fromIndex) != null ? _a : 0;\n    comparator = comparator.comparator;\n  }\n  if (typeof comparator === \"string\") {\n    const key = comparator;\n    comparator = (element, value2) => element[key] === toValue$1(value2);\n  }\n  comparator = comparator != null ? comparator : (element, value2) => element === toValue$1(value2);\n  return computed(() => toValue$1(list).slice(formIndex).some((element, index, array) => comparator(\n    toValue$1(element),\n    toValue$1(value),\n    index,\n    toValue$1(array)\n  )));\n}\n\nfunction useArrayJoin(list, separator) {\n  return computed(() => toValue$1(list).map((i) => toValue$1(i)).join(toValue$1(separator)));\n}\n\nfunction useArrayMap(list, fn) {\n  return computed(() => toValue$1(list).map((i) => toValue$1(i)).map(fn));\n}\n\nfunction useArrayReduce(list, reducer, ...args) {\n  const reduceCallback = (sum, value, index) => reducer(toValue$1(sum), toValue$1(value), index);\n  return computed(() => {\n    const resolved = toValue$1(list);\n    return args.length ? resolved.reduce(reduceCallback, typeof args[0] === \"function\" ? toValue$1(args[0]()) : toValue$1(args[0])) : resolved.reduce(reduceCallback);\n  });\n}\n\nfunction useArraySome(list, fn) {\n  return computed(() => toValue$1(list).some((element, index, array) => fn(toValue$1(element), index, array)));\n}\n\nfunction uniq(array) {\n  return Array.from(new Set(array));\n}\nfunction uniqueElementsBy(array, fn) {\n  return array.reduce((acc, v) => {\n    if (!acc.some((x) => fn(v, x, array)))\n      acc.push(v);\n    return acc;\n  }, []);\n}\nfunction useArrayUnique(list, compareFn) {\n  return computed(() => {\n    const resolvedList = toValue$1(list).map((element) => toValue$1(element));\n    return compareFn ? uniqueElementsBy(resolvedList, compareFn) : uniq(resolvedList);\n  });\n}\n\nfunction useCounter(initialValue = 0, options = {}) {\n  let _initialValue = unref(initialValue);\n  const count = shallowRef(initialValue);\n  const {\n    max = Number.POSITIVE_INFINITY,\n    min = Number.NEGATIVE_INFINITY\n  } = options;\n  const inc = (delta = 1) => count.value = Math.max(Math.min(max, count.value + delta), min);\n  const dec = (delta = 1) => count.value = Math.min(Math.max(min, count.value - delta), max);\n  const get = () => count.value;\n  const set = (val) => count.value = Math.max(min, Math.min(max, val));\n  const reset = (val = _initialValue) => {\n    _initialValue = val;\n    return set(val);\n  };\n  return { count, inc, dec, get, set, reset };\n}\n\nconst REGEX_PARSE = /^(\\d{4})[-/]?(\\d{1,2})?[-/]?(\\d{0,2})[T\\s]*(\\d{1,2})?:?(\\d{1,2})?:?(\\d{1,2})?[.:]?(\\d+)?$/i;\nconst REGEX_FORMAT = /[YMDHhms]o|\\[([^\\]]+)\\]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a{1,2}|A{1,2}|m{1,2}|s{1,2}|Z{1,2}|z{1,4}|SSS/g;\nfunction defaultMeridiem(hours, minutes, isLowercase, hasPeriod) {\n  let m = hours < 12 ? \"AM\" : \"PM\";\n  if (hasPeriod)\n    m = m.split(\"\").reduce((acc, curr) => acc += `${curr}.`, \"\");\n  return isLowercase ? m.toLowerCase() : m;\n}\nfunction formatOrdinal(num) {\n  const suffixes = [\"th\", \"st\", \"nd\", \"rd\"];\n  const v = num % 100;\n  return num + (suffixes[(v - 20) % 10] || suffixes[v] || suffixes[0]);\n}\nfunction formatDate(date, formatStr, options = {}) {\n  var _a;\n  const years = date.getFullYear();\n  const month = date.getMonth();\n  const days = date.getDate();\n  const hours = date.getHours();\n  const minutes = date.getMinutes();\n  const seconds = date.getSeconds();\n  const milliseconds = date.getMilliseconds();\n  const day = date.getDay();\n  const meridiem = (_a = options.customMeridiem) != null ? _a : defaultMeridiem;\n  const stripTimeZone = (dateString) => {\n    var _a2;\n    return (_a2 = dateString.split(\" \")[1]) != null ? _a2 : \"\";\n  };\n  const matches = {\n    Yo: () => formatOrdinal(years),\n    YY: () => String(years).slice(-2),\n    YYYY: () => years,\n    M: () => month + 1,\n    Mo: () => formatOrdinal(month + 1),\n    MM: () => `${month + 1}`.padStart(2, \"0\"),\n    MMM: () => date.toLocaleDateString(toValue$1(options.locales), { month: \"short\" }),\n    MMMM: () => date.toLocaleDateString(toValue$1(options.locales), { month: \"long\" }),\n    D: () => String(days),\n    Do: () => formatOrdinal(days),\n    DD: () => `${days}`.padStart(2, \"0\"),\n    H: () => String(hours),\n    Ho: () => formatOrdinal(hours),\n    HH: () => `${hours}`.padStart(2, \"0\"),\n    h: () => `${hours % 12 || 12}`.padStart(1, \"0\"),\n    ho: () => formatOrdinal(hours % 12 || 12),\n    hh: () => `${hours % 12 || 12}`.padStart(2, \"0\"),\n    m: () => String(minutes),\n    mo: () => formatOrdinal(minutes),\n    mm: () => `${minutes}`.padStart(2, \"0\"),\n    s: () => String(seconds),\n    so: () => formatOrdinal(seconds),\n    ss: () => `${seconds}`.padStart(2, \"0\"),\n    SSS: () => `${milliseconds}`.padStart(3, \"0\"),\n    d: () => day,\n    dd: () => date.toLocaleDateString(toValue$1(options.locales), { weekday: \"narrow\" }),\n    ddd: () => date.toLocaleDateString(toValue$1(options.locales), { weekday: \"short\" }),\n    dddd: () => date.toLocaleDateString(toValue$1(options.locales), { weekday: \"long\" }),\n    A: () => meridiem(hours, minutes),\n    AA: () => meridiem(hours, minutes, false, true),\n    a: () => meridiem(hours, minutes, true),\n    aa: () => meridiem(hours, minutes, true, true),\n    z: () => stripTimeZone(date.toLocaleDateString(toValue$1(options.locales), { timeZoneName: \"shortOffset\" })),\n    zz: () => stripTimeZone(date.toLocaleDateString(toValue$1(options.locales), { timeZoneName: \"shortOffset\" })),\n    zzz: () => stripTimeZone(date.toLocaleDateString(toValue$1(options.locales), { timeZoneName: \"shortOffset\" })),\n    zzzz: () => stripTimeZone(date.toLocaleDateString(toValue$1(options.locales), { timeZoneName: \"longOffset\" }))\n  };\n  return formatStr.replace(REGEX_FORMAT, (match, $1) => {\n    var _a2, _b;\n    return (_b = $1 != null ? $1 : (_a2 = matches[match]) == null ? void 0 : _a2.call(matches)) != null ? _b : match;\n  });\n}\nfunction normalizeDate(date) {\n  if (date === null)\n    return new Date(Number.NaN);\n  if (date === void 0)\n    return /* @__PURE__ */ new Date();\n  if (date instanceof Date)\n    return new Date(date);\n  if (typeof date === \"string\" && !/Z$/i.test(date)) {\n    const d = date.match(REGEX_PARSE);\n    if (d) {\n      const m = d[2] - 1 || 0;\n      const ms = (d[7] || \"0\").substring(0, 3);\n      return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);\n    }\n  }\n  return new Date(date);\n}\nfunction useDateFormat(date, formatStr = \"HH:mm:ss\", options = {}) {\n  return computed(() => formatDate(normalizeDate(toValue$1(date)), toValue$1(formatStr), options));\n}\n\nfunction useIntervalFn(cb, interval = 1e3, options = {}) {\n  const {\n    immediate = true,\n    immediateCallback = false\n  } = options;\n  let timer = null;\n  const isActive = shallowRef(false);\n  function clean() {\n    if (timer) {\n      clearInterval(timer);\n      timer = null;\n    }\n  }\n  function pause() {\n    isActive.value = false;\n    clean();\n  }\n  function resume() {\n    const intervalValue = toValue$1(interval);\n    if (intervalValue <= 0)\n      return;\n    isActive.value = true;\n    if (immediateCallback)\n      cb();\n    clean();\n    if (isActive.value)\n      timer = setInterval(cb, intervalValue);\n  }\n  if (immediate && isClient)\n    resume();\n  if (isRef(interval) || typeof interval === \"function\") {\n    const stopWatch = watch(interval, () => {\n      if (isActive.value && isClient)\n        resume();\n    });\n    tryOnScopeDispose(stopWatch);\n  }\n  tryOnScopeDispose(pause);\n  return {\n    isActive,\n    pause,\n    resume\n  };\n}\n\nfunction useInterval(interval = 1e3, options = {}) {\n  const {\n    controls: exposeControls = false,\n    immediate = true,\n    callback\n  } = options;\n  const counter = shallowRef(0);\n  const update = () => counter.value += 1;\n  const reset = () => {\n    counter.value = 0;\n  };\n  const controls = useIntervalFn(\n    callback ? () => {\n      update();\n      callback(counter.value);\n    } : update,\n    interval,\n    { immediate }\n  );\n  if (exposeControls) {\n    return {\n      counter,\n      reset,\n      ...controls\n    };\n  } else {\n    return counter;\n  }\n}\n\nfunction useLastChanged(source, options = {}) {\n  var _a;\n  const ms = shallowRef((_a = options.initialValue) != null ? _a : null);\n  watch(\n    source,\n    () => ms.value = timestamp(),\n    options\n  );\n  return ms;\n}\n\nfunction useTimeoutFn(cb, interval, options = {}) {\n  const {\n    immediate = true,\n    immediateCallback = false\n  } = options;\n  const isPending = shallowRef(false);\n  let timer = null;\n  function clear() {\n    if (timer) {\n      clearTimeout(timer);\n      timer = null;\n    }\n  }\n  function stop() {\n    isPending.value = false;\n    clear();\n  }\n  function start(...args) {\n    if (immediateCallback)\n      cb();\n    clear();\n    isPending.value = true;\n    timer = setTimeout(() => {\n      isPending.value = false;\n      timer = null;\n      cb(...args);\n    }, toValue$1(interval));\n  }\n  if (immediate) {\n    isPending.value = true;\n    if (isClient)\n      start();\n  }\n  tryOnScopeDispose(stop);\n  return {\n    isPending: readonly(isPending),\n    start,\n    stop\n  };\n}\n\nfunction useTimeout(interval = 1e3, options = {}) {\n  const {\n    controls: exposeControls = false,\n    callback\n  } = options;\n  const controls = useTimeoutFn(\n    callback != null ? callback : noop,\n    interval,\n    options\n  );\n  const ready = computed(() => !controls.isPending.value);\n  if (exposeControls) {\n    return {\n      ready,\n      ...controls\n    };\n  } else {\n    return ready;\n  }\n}\n\nfunction useToNumber(value, options = {}) {\n  const {\n    method = \"parseFloat\",\n    radix,\n    nanToZero\n  } = options;\n  return computed(() => {\n    let resolved = toValue$1(value);\n    if (typeof method === \"function\")\n      resolved = method(resolved);\n    else if (typeof resolved === \"string\")\n      resolved = Number[method](resolved, radix);\n    if (nanToZero && Number.isNaN(resolved))\n      resolved = 0;\n    return resolved;\n  });\n}\n\nfunction useToString(value) {\n  return computed(() => `${toValue$1(value)}`);\n}\n\nfunction useToggle(initialValue = false, options = {}) {\n  const {\n    truthyValue = true,\n    falsyValue = false\n  } = options;\n  const valueIsRef = isRef(initialValue);\n  const _value = shallowRef(initialValue);\n  function toggle(value) {\n    if (arguments.length) {\n      _value.value = value;\n      return _value.value;\n    } else {\n      const truthy = toValue$1(truthyValue);\n      _value.value = _value.value === truthy ? toValue$1(falsyValue) : truthy;\n      return _value.value;\n    }\n  }\n  if (valueIsRef)\n    return toggle;\n  else\n    return [_value, toggle];\n}\n\nfunction watchArray(source, cb, options) {\n  let oldList = (options == null ? void 0 : options.immediate) ? [] : [...typeof source === \"function\" ? source() : Array.isArray(source) ? source : toValue$1(source)];\n  return watch(source, (newList, _, onCleanup) => {\n    const oldListRemains = Array.from({ length: oldList.length });\n    const added = [];\n    for (const obj of newList) {\n      let found = false;\n      for (let i = 0; i < oldList.length; i++) {\n        if (!oldListRemains[i] && obj === oldList[i]) {\n          oldListRemains[i] = true;\n          found = true;\n          break;\n        }\n      }\n      if (!found)\n        added.push(obj);\n    }\n    const removed = oldList.filter((_2, i) => !oldListRemains[i]);\n    cb(newList, oldList, added, removed, onCleanup);\n    oldList = [...newList];\n  }, options);\n}\n\nfunction watchAtMost(source, cb, options) {\n  const {\n    count,\n    ...watchOptions\n  } = options;\n  const current = shallowRef(0);\n  const stop = watchWithFilter(\n    source,\n    (...args) => {\n      current.value += 1;\n      if (current.value >= toValue$1(count))\n        nextTick(() => stop());\n      cb(...args);\n    },\n    watchOptions\n  );\n  return { count: current, stop };\n}\n\nfunction watchDebounced(source, cb, options = {}) {\n  const {\n    debounce = 0,\n    maxWait = void 0,\n    ...watchOptions\n  } = options;\n  return watchWithFilter(\n    source,\n    cb,\n    {\n      ...watchOptions,\n      eventFilter: debounceFilter(debounce, { maxWait })\n    }\n  );\n}\n\nfunction watchDeep(source, cb, options) {\n  return watch(\n    source,\n    cb,\n    {\n      ...options,\n      deep: true\n    }\n  );\n}\n\nfunction watchIgnorable(source, cb, options = {}) {\n  const {\n    eventFilter = bypassFilter,\n    ...watchOptions\n  } = options;\n  const filteredCb = createFilterWrapper(\n    eventFilter,\n    cb\n  );\n  let ignoreUpdates;\n  let ignorePrevAsyncUpdates;\n  let stop;\n  if (watchOptions.flush === \"sync\") {\n    const ignore = shallowRef(false);\n    ignorePrevAsyncUpdates = () => {\n    };\n    ignoreUpdates = (updater) => {\n      ignore.value = true;\n      updater();\n      ignore.value = false;\n    };\n    stop = watch(\n      source,\n      (...args) => {\n        if (!ignore.value)\n          filteredCb(...args);\n      },\n      watchOptions\n    );\n  } else {\n    const disposables = [];\n    const ignoreCounter = shallowRef(0);\n    const syncCounter = shallowRef(0);\n    ignorePrevAsyncUpdates = () => {\n      ignoreCounter.value = syncCounter.value;\n    };\n    disposables.push(\n      watch(\n        source,\n        () => {\n          syncCounter.value++;\n        },\n        { ...watchOptions, flush: \"sync\" }\n      )\n    );\n    ignoreUpdates = (updater) => {\n      const syncCounterPrev = syncCounter.value;\n      updater();\n      ignoreCounter.value += syncCounter.value - syncCounterPrev;\n    };\n    disposables.push(\n      watch(\n        source,\n        (...args) => {\n          const ignore = ignoreCounter.value > 0 && ignoreCounter.value === syncCounter.value;\n          ignoreCounter.value = 0;\n          syncCounter.value = 0;\n          if (ignore)\n            return;\n          filteredCb(...args);\n        },\n        watchOptions\n      )\n    );\n    stop = () => {\n      disposables.forEach((fn) => fn());\n    };\n  }\n  return { stop, ignoreUpdates, ignorePrevAsyncUpdates };\n}\n\nfunction watchImmediate(source, cb, options) {\n  return watch(\n    source,\n    cb,\n    {\n      ...options,\n      immediate: true\n    }\n  );\n}\n\nfunction watchOnce(source, cb, options) {\n  const stop = watch(source, (...args) => {\n    nextTick(() => stop());\n    return cb(...args);\n  }, options);\n  return stop;\n}\n\nfunction watchThrottled(source, cb, options = {}) {\n  const {\n    throttle = 0,\n    trailing = true,\n    leading = true,\n    ...watchOptions\n  } = options;\n  return watchWithFilter(\n    source,\n    cb,\n    {\n      ...watchOptions,\n      eventFilter: throttleFilter(throttle, trailing, leading)\n    }\n  );\n}\n\nfunction watchTriggerable(source, cb, options = {}) {\n  let cleanupFn;\n  function onEffect() {\n    if (!cleanupFn)\n      return;\n    const fn = cleanupFn;\n    cleanupFn = void 0;\n    fn();\n  }\n  function onCleanup(callback) {\n    cleanupFn = callback;\n  }\n  const _cb = (value, oldValue) => {\n    onEffect();\n    return cb(value, oldValue, onCleanup);\n  };\n  const res = watchIgnorable(source, _cb, options);\n  const { ignoreUpdates } = res;\n  const trigger = () => {\n    let res2;\n    ignoreUpdates(() => {\n      res2 = _cb(getWatchSources(source), getOldValue(source));\n    });\n    return res2;\n  };\n  return {\n    ...res,\n    trigger\n  };\n}\nfunction getWatchSources(sources) {\n  if (isReactive(sources))\n    return sources;\n  if (Array.isArray(sources))\n    return sources.map((item) => toValue$1(item));\n  return toValue$1(sources);\n}\nfunction getOldValue(source) {\n  return Array.isArray(source) ? source.map(() => void 0) : void 0;\n}\n\nfunction whenever(source, cb, options) {\n  const stop = watch(\n    source,\n    (v, ov, onInvalidate) => {\n      if (v) {\n        if (options == null ? void 0 : options.once)\n          nextTick(() => stop());\n        cb(v, ov, onInvalidate);\n      }\n    },\n    {\n      ...options,\n      once: false\n    }\n  );\n  return stop;\n}\n\nexport { assert, refAutoReset as autoResetRef, bypassFilter, camelize, clamp, computedEager, computedWithControl, containsProp, computedWithControl as controlledComputed, controlledRef, createEventHook, createFilterWrapper, createGlobalState, createInjectionState, reactify as createReactiveFn, createRef, createSharedComposable, createSingletonPromise, debounceFilter, refDebounced as debouncedRef, watchDebounced as debouncedWatch, computedEager as eagerComputed, extendRef, formatDate, get, getLifeCycleTarget, hasOwn, hyphenate, identity, watchIgnorable as ignorableWatch, increaseWithUnit, injectLocal, invoke, isClient, isDef, isDefined, isIOS, isObject, isWorker, makeDestructurable, noop, normalizeDate, notNullish, now, objectEntries, objectOmit, objectPick, pausableFilter, watchPausable as pausableWatch, promiseTimeout, provideLocal, pxValue, rand, reactify, reactifyObject, reactiveComputed, reactiveOmit, reactivePick, refAutoReset, refDebounced, refDefault, refThrottled, refWithControl, resolveRef, resolveUnref, set, syncRef, syncRefs, throttleFilter, refThrottled as throttledRef, watchThrottled as throttledWatch, timestamp, toArray, toReactive, toRef, toRefs, toValue, tryOnBeforeMount, tryOnBeforeUnmount, tryOnMounted, tryOnScopeDispose, tryOnUnmounted, until, useArrayDifference, useArrayEvery, useArrayFilter, useArrayFind, useArrayFindIndex, useArrayFindLast, useArrayIncludes, useArrayJoin, useArrayMap, useArrayReduce, useArraySome, useArrayUnique, useCounter, useDateFormat, refDebounced as useDebounce, useDebounceFn, useInterval, useIntervalFn, useLastChanged, refThrottled as useThrottle, useThrottleFn, useTimeout, useTimeoutFn, useToNumber, useToString, useToggle, watchArray, watchAtMost, watchDebounced, watchDeep, watchIgnorable, watchImmediate, watchOnce, watchPausable, watchThrottled, watchTriggerable, watchWithFilter, whenever };\n", "import { noop, makeDestructurable, camelize, isClient, toArray, watchImmediate, isObject, tryOnScopeDispose, isIOS, notNullish, tryOnMounted, objectOmit, promiseTimeout, until, injectLocal, provideLocal, pxValue, increaseWithUnit, objectEntries, createRef, createSingletonPromise, useTimeoutFn, pausableWatch, toRef, createEventHook, useIntervalFn, computedWithControl, timestamp, pausableFilter, watchIgnorable, debounceFilter, bypassFilter, createFilterWrapper, toRefs, watchOnce, containsProp, hasOwn, throttleFilter, useDebounceFn, useThrottleFn, tryOnUnmounted, clamp, syncRef, objectPick, watchWithFilter, identity, isDef, whenever, isWorker } from '@vueuse/shared';\nexport * from '@vueuse/shared';\nimport { isRef, shallowRef, ref, watchEffect, computed, inject, defineComponent, h, TransitionGroup, shallowReactive, Fragment, toValue, unref, getCurrentInstance, onMounted, watch, customRef, onUpdated, readonly, reactive, hasInjectionContext, toRaw, nextTick, markRaw, getCurrentScope, isReadonly, onBeforeUpdate } from 'vue';\n\nfunction computedAsync(evaluationCallback, initialState, optionsOrRef) {\n  let options;\n  if (isRef(optionsOrRef)) {\n    options = {\n      evaluating: optionsOrRef\n    };\n  } else {\n    options = optionsOrRef || {};\n  }\n  const {\n    lazy = false,\n    evaluating = void 0,\n    shallow = true,\n    onError = noop\n  } = options;\n  const started = shallowRef(!lazy);\n  const current = shallow ? shallowRef(initialState) : ref(initialState);\n  let counter = 0;\n  watchEffect(async (onInvalidate) => {\n    if (!started.value)\n      return;\n    counter++;\n    const counterAtBeginning = counter;\n    let hasFinished = false;\n    if (evaluating) {\n      Promise.resolve().then(() => {\n        evaluating.value = true;\n      });\n    }\n    try {\n      const result = await evaluationCallback((cancelCallback) => {\n        onInvalidate(() => {\n          if (evaluating)\n            evaluating.value = false;\n          if (!hasFinished)\n            cancelCallback();\n        });\n      });\n      if (counterAtBeginning === counter)\n        current.value = result;\n    } catch (e) {\n      onError(e);\n    } finally {\n      if (evaluating && counterAtBeginning === counter)\n        evaluating.value = false;\n      hasFinished = true;\n    }\n  });\n  if (lazy) {\n    return computed(() => {\n      started.value = true;\n      return current.value;\n    });\n  } else {\n    return current;\n  }\n}\n\nfunction computedInject(key, options, defaultSource, treatDefaultAsFactory) {\n  let source = inject(key);\n  if (defaultSource)\n    source = inject(key, defaultSource);\n  if (treatDefaultAsFactory)\n    source = inject(key, defaultSource, treatDefaultAsFactory);\n  if (typeof options === \"function\") {\n    return computed((ctx) => options(source, ctx));\n  } else {\n    return computed({\n      get: (ctx) => options.get(source, ctx),\n      set: options.set\n    });\n  }\n}\n\nfunction createReusableTemplate(options = {}) {\n  const {\n    inheritAttrs = true\n  } = options;\n  const render = shallowRef();\n  const define = /*@__PURE__*/ defineComponent({\n    setup(_, { slots }) {\n      return () => {\n        render.value = slots.default;\n      };\n    }\n  });\n  const reuse = /*@__PURE__*/ defineComponent({\n    inheritAttrs,\n    props: options.props,\n    setup(props, { attrs, slots }) {\n      return () => {\n        var _a;\n        if (!render.value && process.env.NODE_ENV !== \"production\")\n          throw new Error(\"[VueUse] Failed to find the definition of reusable template\");\n        const vnode = (_a = render.value) == null ? void 0 : _a.call(render, {\n          ...options.props == null ? keysToCamelKebabCase(attrs) : props,\n          $slots: slots\n        });\n        return inheritAttrs && (vnode == null ? void 0 : vnode.length) === 1 ? vnode[0] : vnode;\n      };\n    }\n  });\n  return makeDestructurable(\n    { define, reuse },\n    [define, reuse]\n  );\n}\nfunction keysToCamelKebabCase(obj) {\n  const newObj = {};\n  for (const key in obj)\n    newObj[camelize(key)] = obj[key];\n  return newObj;\n}\n\nfunction createTemplatePromise(options = {}) {\n  let index = 0;\n  const instances = ref([]);\n  function create(...args) {\n    const props = shallowReactive({\n      key: index++,\n      args,\n      promise: void 0,\n      resolve: () => {\n      },\n      reject: () => {\n      },\n      isResolving: false,\n      options\n    });\n    instances.value.push(props);\n    props.promise = new Promise((_resolve, _reject) => {\n      props.resolve = (v) => {\n        props.isResolving = true;\n        return _resolve(v);\n      };\n      props.reject = _reject;\n    }).finally(() => {\n      props.promise = void 0;\n      const index2 = instances.value.indexOf(props);\n      if (index2 !== -1)\n        instances.value.splice(index2, 1);\n    });\n    return props.promise;\n  }\n  function start(...args) {\n    if (options.singleton && instances.value.length > 0)\n      return instances.value[0].promise;\n    return create(...args);\n  }\n  const component = /*@__PURE__*/ defineComponent((_, { slots }) => {\n    const renderList = () => instances.value.map((props) => {\n      var _a;\n      return h(Fragment, { key: props.key }, (_a = slots.default) == null ? void 0 : _a.call(slots, props));\n    });\n    if (options.transition)\n      return () => h(TransitionGroup, options.transition, renderList);\n    return renderList;\n  });\n  component.start = start;\n  return component;\n}\n\nfunction createUnrefFn(fn) {\n  return function(...args) {\n    return fn.apply(this, args.map((i) => toValue(i)));\n  };\n}\n\nconst defaultWindow = isClient ? window : void 0;\nconst defaultDocument = isClient ? window.document : void 0;\nconst defaultNavigator = isClient ? window.navigator : void 0;\nconst defaultLocation = isClient ? window.location : void 0;\n\nfunction unrefElement(elRef) {\n  var _a;\n  const plain = toValue(elRef);\n  return (_a = plain == null ? void 0 : plain.$el) != null ? _a : plain;\n}\n\nfunction useEventListener(...args) {\n  const cleanups = [];\n  const cleanup = () => {\n    cleanups.forEach((fn) => fn());\n    cleanups.length = 0;\n  };\n  const register = (el, event, listener, options) => {\n    el.addEventListener(event, listener, options);\n    return () => el.removeEventListener(event, listener, options);\n  };\n  const firstParamTargets = computed(() => {\n    const test = toArray(toValue(args[0])).filter((e) => e != null);\n    return test.every((e) => typeof e !== \"string\") ? test : void 0;\n  });\n  const stopWatch = watchImmediate(\n    () => {\n      var _a, _b;\n      return [\n        (_b = (_a = firstParamTargets.value) == null ? void 0 : _a.map((e) => unrefElement(e))) != null ? _b : [defaultWindow].filter((e) => e != null),\n        toArray(toValue(firstParamTargets.value ? args[1] : args[0])),\n        toArray(unref(firstParamTargets.value ? args[2] : args[1])),\n        // @ts-expect-error - TypeScript gets the correct types, but somehow still complains\n        toValue(firstParamTargets.value ? args[3] : args[2])\n      ];\n    },\n    ([raw_targets, raw_events, raw_listeners, raw_options]) => {\n      cleanup();\n      if (!(raw_targets == null ? void 0 : raw_targets.length) || !(raw_events == null ? void 0 : raw_events.length) || !(raw_listeners == null ? void 0 : raw_listeners.length))\n        return;\n      const optionsClone = isObject(raw_options) ? { ...raw_options } : raw_options;\n      cleanups.push(\n        ...raw_targets.flatMap(\n          (el) => raw_events.flatMap(\n            (event) => raw_listeners.map((listener) => register(el, event, listener, optionsClone))\n          )\n        )\n      );\n    },\n    { flush: \"post\" }\n  );\n  const stop = () => {\n    stopWatch();\n    cleanup();\n  };\n  tryOnScopeDispose(cleanup);\n  return stop;\n}\n\nlet _iOSWorkaround = false;\nfunction onClickOutside(target, handler, options = {}) {\n  const { window = defaultWindow, ignore = [], capture = true, detectIframe = false, controls = false } = options;\n  if (!window) {\n    return controls ? { stop: noop, cancel: noop, trigger: noop } : noop;\n  }\n  if (isIOS && !_iOSWorkaround) {\n    _iOSWorkaround = true;\n    const listenerOptions = { passive: true };\n    Array.from(window.document.body.children).forEach((el) => useEventListener(el, \"click\", noop, listenerOptions));\n    useEventListener(window.document.documentElement, \"click\", noop, listenerOptions);\n  }\n  let shouldListen = true;\n  const shouldIgnore = (event) => {\n    return toValue(ignore).some((target2) => {\n      if (typeof target2 === \"string\") {\n        return Array.from(window.document.querySelectorAll(target2)).some((el) => el === event.target || event.composedPath().includes(el));\n      } else {\n        const el = unrefElement(target2);\n        return el && (event.target === el || event.composedPath().includes(el));\n      }\n    });\n  };\n  function hasMultipleRoots(target2) {\n    const vm = toValue(target2);\n    return vm && vm.$.subTree.shapeFlag === 16;\n  }\n  function checkMultipleRoots(target2, event) {\n    const vm = toValue(target2);\n    const children = vm.$.subTree && vm.$.subTree.children;\n    if (children == null || !Array.isArray(children))\n      return false;\n    return children.some((child) => child.el === event.target || event.composedPath().includes(child.el));\n  }\n  const listener = (event) => {\n    const el = unrefElement(target);\n    if (event.target == null)\n      return;\n    if (!(el instanceof Element) && hasMultipleRoots(target) && checkMultipleRoots(target, event))\n      return;\n    if (!el || el === event.target || event.composedPath().includes(el))\n      return;\n    if (\"detail\" in event && event.detail === 0)\n      shouldListen = !shouldIgnore(event);\n    if (!shouldListen) {\n      shouldListen = true;\n      return;\n    }\n    handler(event);\n  };\n  let isProcessingClick = false;\n  const cleanup = [\n    useEventListener(window, \"click\", (event) => {\n      if (!isProcessingClick) {\n        isProcessingClick = true;\n        setTimeout(() => {\n          isProcessingClick = false;\n        }, 0);\n        listener(event);\n      }\n    }, { passive: true, capture }),\n    useEventListener(window, \"pointerdown\", (e) => {\n      const el = unrefElement(target);\n      shouldListen = !shouldIgnore(e) && !!(el && !e.composedPath().includes(el));\n    }, { passive: true }),\n    detectIframe && useEventListener(window, \"blur\", (event) => {\n      setTimeout(() => {\n        var _a;\n        const el = unrefElement(target);\n        if (((_a = window.document.activeElement) == null ? void 0 : _a.tagName) === \"IFRAME\" && !(el == null ? void 0 : el.contains(window.document.activeElement))) {\n          handler(event);\n        }\n      }, 0);\n    }, { passive: true })\n  ].filter(Boolean);\n  const stop = () => cleanup.forEach((fn) => fn());\n  if (controls) {\n    return {\n      stop,\n      cancel: () => {\n        shouldListen = false;\n      },\n      trigger: (event) => {\n        shouldListen = true;\n        listener(event);\n        shouldListen = false;\n      }\n    };\n  }\n  return stop;\n}\n\nfunction useMounted() {\n  const isMounted = shallowRef(false);\n  const instance = getCurrentInstance();\n  if (instance) {\n    onMounted(() => {\n      isMounted.value = true;\n    }, instance);\n  }\n  return isMounted;\n}\n\nfunction useSupported(callback) {\n  const isMounted = useMounted();\n  return computed(() => {\n    isMounted.value;\n    return Boolean(callback());\n  });\n}\n\nfunction useMutationObserver(target, callback, options = {}) {\n  const { window = defaultWindow, ...mutationOptions } = options;\n  let observer;\n  const isSupported = useSupported(() => window && \"MutationObserver\" in window);\n  const cleanup = () => {\n    if (observer) {\n      observer.disconnect();\n      observer = void 0;\n    }\n  };\n  const targets = computed(() => {\n    const value = toValue(target);\n    const items = toArray(value).map(unrefElement).filter(notNullish);\n    return new Set(items);\n  });\n  const stopWatch = watch(\n    () => targets.value,\n    (targets2) => {\n      cleanup();\n      if (isSupported.value && targets2.size) {\n        observer = new MutationObserver(callback);\n        targets2.forEach((el) => observer.observe(el, mutationOptions));\n      }\n    },\n    { immediate: true, flush: \"post\" }\n  );\n  const takeRecords = () => {\n    return observer == null ? void 0 : observer.takeRecords();\n  };\n  const stop = () => {\n    stopWatch();\n    cleanup();\n  };\n  tryOnScopeDispose(stop);\n  return {\n    isSupported,\n    stop,\n    takeRecords\n  };\n}\n\nfunction onElementRemoval(target, callback, options = {}) {\n  const {\n    window = defaultWindow,\n    document = window == null ? void 0 : window.document,\n    flush = \"sync\"\n  } = options;\n  if (!window || !document)\n    return noop;\n  let stopFn;\n  const cleanupAndUpdate = (fn) => {\n    stopFn == null ? void 0 : stopFn();\n    stopFn = fn;\n  };\n  const stopWatch = watchEffect(() => {\n    const el = unrefElement(target);\n    if (el) {\n      const { stop } = useMutationObserver(\n        document,\n        (mutationsList) => {\n          const targetRemoved = mutationsList.map((mutation) => [...mutation.removedNodes]).flat().some((node) => node === el || node.contains(el));\n          if (targetRemoved) {\n            callback(mutationsList);\n          }\n        },\n        {\n          window,\n          childList: true,\n          subtree: true\n        }\n      );\n      cleanupAndUpdate(stop);\n    }\n  }, { flush });\n  const stopHandle = () => {\n    stopWatch();\n    cleanupAndUpdate();\n  };\n  tryOnScopeDispose(stopHandle);\n  return stopHandle;\n}\n\nfunction createKeyPredicate(keyFilter) {\n  if (typeof keyFilter === \"function\")\n    return keyFilter;\n  else if (typeof keyFilter === \"string\")\n    return (event) => event.key === keyFilter;\n  else if (Array.isArray(keyFilter))\n    return (event) => keyFilter.includes(event.key);\n  return () => true;\n}\nfunction onKeyStroke(...args) {\n  let key;\n  let handler;\n  let options = {};\n  if (args.length === 3) {\n    key = args[0];\n    handler = args[1];\n    options = args[2];\n  } else if (args.length === 2) {\n    if (typeof args[1] === \"object\") {\n      key = true;\n      handler = args[0];\n      options = args[1];\n    } else {\n      key = args[0];\n      handler = args[1];\n    }\n  } else {\n    key = true;\n    handler = args[0];\n  }\n  const {\n    target = defaultWindow,\n    eventName = \"keydown\",\n    passive = false,\n    dedupe = false\n  } = options;\n  const predicate = createKeyPredicate(key);\n  const listener = (e) => {\n    if (e.repeat && toValue(dedupe))\n      return;\n    if (predicate(e))\n      handler(e);\n  };\n  return useEventListener(target, eventName, listener, passive);\n}\nfunction onKeyDown(key, handler, options = {}) {\n  return onKeyStroke(key, handler, { ...options, eventName: \"keydown\" });\n}\nfunction onKeyPressed(key, handler, options = {}) {\n  return onKeyStroke(key, handler, { ...options, eventName: \"keypress\" });\n}\nfunction onKeyUp(key, handler, options = {}) {\n  return onKeyStroke(key, handler, { ...options, eventName: \"keyup\" });\n}\n\nconst DEFAULT_DELAY = 500;\nconst DEFAULT_THRESHOLD = 10;\nfunction onLongPress(target, handler, options) {\n  var _a, _b;\n  const elementRef = computed(() => unrefElement(target));\n  let timeout;\n  let posStart;\n  let startTimestamp;\n  let hasLongPressed = false;\n  function clear() {\n    if (timeout) {\n      clearTimeout(timeout);\n      timeout = void 0;\n    }\n    posStart = void 0;\n    startTimestamp = void 0;\n    hasLongPressed = false;\n  }\n  function onRelease(ev) {\n    var _a2, _b2, _c;\n    const [_startTimestamp, _posStart, _hasLongPressed] = [startTimestamp, posStart, hasLongPressed];\n    clear();\n    if (!(options == null ? void 0 : options.onMouseUp) || !_posStart || !_startTimestamp)\n      return;\n    if (((_a2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _a2.self) && ev.target !== elementRef.value)\n      return;\n    if ((_b2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _b2.prevent)\n      ev.preventDefault();\n    if ((_c = options == null ? void 0 : options.modifiers) == null ? void 0 : _c.stop)\n      ev.stopPropagation();\n    const dx = ev.x - _posStart.x;\n    const dy = ev.y - _posStart.y;\n    const distance = Math.sqrt(dx * dx + dy * dy);\n    options.onMouseUp(ev.timeStamp - _startTimestamp, distance, _hasLongPressed);\n  }\n  function onDown(ev) {\n    var _a2, _b2, _c, _d;\n    if (((_a2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _a2.self) && ev.target !== elementRef.value)\n      return;\n    clear();\n    if ((_b2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _b2.prevent)\n      ev.preventDefault();\n    if ((_c = options == null ? void 0 : options.modifiers) == null ? void 0 : _c.stop)\n      ev.stopPropagation();\n    posStart = {\n      x: ev.x,\n      y: ev.y\n    };\n    startTimestamp = ev.timeStamp;\n    timeout = setTimeout(\n      () => {\n        hasLongPressed = true;\n        handler(ev);\n      },\n      (_d = options == null ? void 0 : options.delay) != null ? _d : DEFAULT_DELAY\n    );\n  }\n  function onMove(ev) {\n    var _a2, _b2, _c, _d;\n    if (((_a2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _a2.self) && ev.target !== elementRef.value)\n      return;\n    if (!posStart || (options == null ? void 0 : options.distanceThreshold) === false)\n      return;\n    if ((_b2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _b2.prevent)\n      ev.preventDefault();\n    if ((_c = options == null ? void 0 : options.modifiers) == null ? void 0 : _c.stop)\n      ev.stopPropagation();\n    const dx = ev.x - posStart.x;\n    const dy = ev.y - posStart.y;\n    const distance = Math.sqrt(dx * dx + dy * dy);\n    if (distance >= ((_d = options == null ? void 0 : options.distanceThreshold) != null ? _d : DEFAULT_THRESHOLD))\n      clear();\n  }\n  const listenerOptions = {\n    capture: (_a = options == null ? void 0 : options.modifiers) == null ? void 0 : _a.capture,\n    once: (_b = options == null ? void 0 : options.modifiers) == null ? void 0 : _b.once\n  };\n  const cleanup = [\n    useEventListener(elementRef, \"pointerdown\", onDown, listenerOptions),\n    useEventListener(elementRef, \"pointermove\", onMove, listenerOptions),\n    useEventListener(elementRef, [\"pointerup\", \"pointerleave\"], onRelease, listenerOptions)\n  ];\n  const stop = () => cleanup.forEach((fn) => fn());\n  return stop;\n}\n\nfunction isFocusedElementEditable() {\n  const { activeElement, body } = document;\n  if (!activeElement)\n    return false;\n  if (activeElement === body)\n    return false;\n  switch (activeElement.tagName) {\n    case \"INPUT\":\n    case \"TEXTAREA\":\n      return true;\n  }\n  return activeElement.hasAttribute(\"contenteditable\");\n}\nfunction isTypedCharValid({\n  keyCode,\n  metaKey,\n  ctrlKey,\n  altKey\n}) {\n  if (metaKey || ctrlKey || altKey)\n    return false;\n  if (keyCode >= 48 && keyCode <= 57 || keyCode >= 96 && keyCode <= 105)\n    return true;\n  if (keyCode >= 65 && keyCode <= 90)\n    return true;\n  return false;\n}\nfunction onStartTyping(callback, options = {}) {\n  const { document: document2 = defaultDocument } = options;\n  const keydown = (event) => {\n    if (!isFocusedElementEditable() && isTypedCharValid(event)) {\n      callback(event);\n    }\n  };\n  if (document2)\n    useEventListener(document2, \"keydown\", keydown, { passive: true });\n}\n\nfunction templateRef(key, initialValue = null) {\n  const instance = getCurrentInstance();\n  let _trigger = () => {\n  };\n  const element = customRef((track, trigger) => {\n    _trigger = trigger;\n    return {\n      get() {\n        var _a, _b;\n        track();\n        return (_b = (_a = instance == null ? void 0 : instance.proxy) == null ? void 0 : _a.$refs[key]) != null ? _b : initialValue;\n      },\n      set() {\n      }\n    };\n  });\n  tryOnMounted(_trigger);\n  onUpdated(_trigger);\n  return element;\n}\n\nfunction useActiveElement(options = {}) {\n  var _a;\n  const {\n    window = defaultWindow,\n    deep = true,\n    triggerOnRemoval = false\n  } = options;\n  const document = (_a = options.document) != null ? _a : window == null ? void 0 : window.document;\n  const getDeepActiveElement = () => {\n    var _a2;\n    let element = document == null ? void 0 : document.activeElement;\n    if (deep) {\n      while (element == null ? void 0 : element.shadowRoot)\n        element = (_a2 = element == null ? void 0 : element.shadowRoot) == null ? void 0 : _a2.activeElement;\n    }\n    return element;\n  };\n  const activeElement = shallowRef();\n  const trigger = () => {\n    activeElement.value = getDeepActiveElement();\n  };\n  if (window) {\n    const listenerOptions = {\n      capture: true,\n      passive: true\n    };\n    useEventListener(\n      window,\n      \"blur\",\n      (event) => {\n        if (event.relatedTarget !== null)\n          return;\n        trigger();\n      },\n      listenerOptions\n    );\n    useEventListener(\n      window,\n      \"focus\",\n      trigger,\n      listenerOptions\n    );\n  }\n  if (triggerOnRemoval) {\n    onElementRemoval(activeElement, trigger, { document });\n  }\n  trigger();\n  return activeElement;\n}\n\nfunction useRafFn(fn, options = {}) {\n  const {\n    immediate = true,\n    fpsLimit = void 0,\n    window = defaultWindow,\n    once = false\n  } = options;\n  const isActive = shallowRef(false);\n  const intervalLimit = computed(() => {\n    return fpsLimit ? 1e3 / toValue(fpsLimit) : null;\n  });\n  let previousFrameTimestamp = 0;\n  let rafId = null;\n  function loop(timestamp) {\n    if (!isActive.value || !window)\n      return;\n    if (!previousFrameTimestamp)\n      previousFrameTimestamp = timestamp;\n    const delta = timestamp - previousFrameTimestamp;\n    if (intervalLimit.value && delta < intervalLimit.value) {\n      rafId = window.requestAnimationFrame(loop);\n      return;\n    }\n    previousFrameTimestamp = timestamp;\n    fn({ delta, timestamp });\n    if (once) {\n      isActive.value = false;\n      rafId = null;\n      return;\n    }\n    rafId = window.requestAnimationFrame(loop);\n  }\n  function resume() {\n    if (!isActive.value && window) {\n      isActive.value = true;\n      previousFrameTimestamp = 0;\n      rafId = window.requestAnimationFrame(loop);\n    }\n  }\n  function pause() {\n    isActive.value = false;\n    if (rafId != null && window) {\n      window.cancelAnimationFrame(rafId);\n      rafId = null;\n    }\n  }\n  if (immediate)\n    resume();\n  tryOnScopeDispose(pause);\n  return {\n    isActive: readonly(isActive),\n    pause,\n    resume\n  };\n}\n\nfunction useAnimate(target, keyframes, options) {\n  let config;\n  let animateOptions;\n  if (isObject(options)) {\n    config = options;\n    animateOptions = objectOmit(options, [\"window\", \"immediate\", \"commitStyles\", \"persist\", \"onReady\", \"onError\"]);\n  } else {\n    config = { duration: options };\n    animateOptions = options;\n  }\n  const {\n    window = defaultWindow,\n    immediate = true,\n    commitStyles,\n    persist,\n    playbackRate: _playbackRate = 1,\n    onReady,\n    onError = (e) => {\n      console.error(e);\n    }\n  } = config;\n  const isSupported = useSupported(() => window && HTMLElement && \"animate\" in HTMLElement.prototype);\n  const animate = shallowRef(void 0);\n  const store = shallowReactive({\n    startTime: null,\n    currentTime: null,\n    timeline: null,\n    playbackRate: _playbackRate,\n    pending: false,\n    playState: immediate ? \"idle\" : \"paused\",\n    replaceState: \"active\"\n  });\n  const pending = computed(() => store.pending);\n  const playState = computed(() => store.playState);\n  const replaceState = computed(() => store.replaceState);\n  const startTime = computed({\n    get() {\n      return store.startTime;\n    },\n    set(value) {\n      store.startTime = value;\n      if (animate.value)\n        animate.value.startTime = value;\n    }\n  });\n  const currentTime = computed({\n    get() {\n      return store.currentTime;\n    },\n    set(value) {\n      store.currentTime = value;\n      if (animate.value) {\n        animate.value.currentTime = value;\n        syncResume();\n      }\n    }\n  });\n  const timeline = computed({\n    get() {\n      return store.timeline;\n    },\n    set(value) {\n      store.timeline = value;\n      if (animate.value)\n        animate.value.timeline = value;\n    }\n  });\n  const playbackRate = computed({\n    get() {\n      return store.playbackRate;\n    },\n    set(value) {\n      store.playbackRate = value;\n      if (animate.value)\n        animate.value.playbackRate = value;\n    }\n  });\n  const play = () => {\n    if (animate.value) {\n      try {\n        animate.value.play();\n        syncResume();\n      } catch (e) {\n        syncPause();\n        onError(e);\n      }\n    } else {\n      update();\n    }\n  };\n  const pause = () => {\n    var _a;\n    try {\n      (_a = animate.value) == null ? void 0 : _a.pause();\n      syncPause();\n    } catch (e) {\n      onError(e);\n    }\n  };\n  const reverse = () => {\n    var _a;\n    if (!animate.value)\n      update();\n    try {\n      (_a = animate.value) == null ? void 0 : _a.reverse();\n      syncResume();\n    } catch (e) {\n      syncPause();\n      onError(e);\n    }\n  };\n  const finish = () => {\n    var _a;\n    try {\n      (_a = animate.value) == null ? void 0 : _a.finish();\n      syncPause();\n    } catch (e) {\n      onError(e);\n    }\n  };\n  const cancel = () => {\n    var _a;\n    try {\n      (_a = animate.value) == null ? void 0 : _a.cancel();\n      syncPause();\n    } catch (e) {\n      onError(e);\n    }\n  };\n  watch(() => unrefElement(target), (el) => {\n    if (el) {\n      update();\n    } else {\n      animate.value = void 0;\n    }\n  });\n  watch(() => keyframes, (value) => {\n    if (animate.value) {\n      update();\n      const targetEl = unrefElement(target);\n      if (targetEl) {\n        animate.value.effect = new KeyframeEffect(\n          targetEl,\n          toValue(value),\n          animateOptions\n        );\n      }\n    }\n  }, { deep: true });\n  tryOnMounted(() => update(true), false);\n  tryOnScopeDispose(cancel);\n  function update(init) {\n    const el = unrefElement(target);\n    if (!isSupported.value || !el)\n      return;\n    if (!animate.value)\n      animate.value = el.animate(toValue(keyframes), animateOptions);\n    if (persist)\n      animate.value.persist();\n    if (_playbackRate !== 1)\n      animate.value.playbackRate = _playbackRate;\n    if (init && !immediate)\n      animate.value.pause();\n    else\n      syncResume();\n    onReady == null ? void 0 : onReady(animate.value);\n  }\n  const listenerOptions = { passive: true };\n  useEventListener(animate, [\"cancel\", \"finish\", \"remove\"], syncPause, listenerOptions);\n  useEventListener(animate, \"finish\", () => {\n    var _a;\n    if (commitStyles)\n      (_a = animate.value) == null ? void 0 : _a.commitStyles();\n  }, listenerOptions);\n  const { resume: resumeRef, pause: pauseRef } = useRafFn(() => {\n    if (!animate.value)\n      return;\n    store.pending = animate.value.pending;\n    store.playState = animate.value.playState;\n    store.replaceState = animate.value.replaceState;\n    store.startTime = animate.value.startTime;\n    store.currentTime = animate.value.currentTime;\n    store.timeline = animate.value.timeline;\n    store.playbackRate = animate.value.playbackRate;\n  }, { immediate: false });\n  function syncResume() {\n    if (isSupported.value)\n      resumeRef();\n  }\n  function syncPause() {\n    if (isSupported.value && window)\n      window.requestAnimationFrame(pauseRef);\n  }\n  return {\n    isSupported,\n    animate,\n    // actions\n    play,\n    pause,\n    reverse,\n    finish,\n    cancel,\n    // state\n    pending,\n    playState,\n    replaceState,\n    startTime,\n    currentTime,\n    timeline,\n    playbackRate\n  };\n}\n\nfunction useAsyncQueue(tasks, options) {\n  const {\n    interrupt = true,\n    onError = noop,\n    onFinished = noop,\n    signal\n  } = options || {};\n  const promiseState = {\n    aborted: \"aborted\",\n    fulfilled: \"fulfilled\",\n    pending: \"pending\",\n    rejected: \"rejected\"\n  };\n  const initialResult = Array.from(Array.from({ length: tasks.length }), () => ({ state: promiseState.pending, data: null }));\n  const result = reactive(initialResult);\n  const activeIndex = shallowRef(-1);\n  if (!tasks || tasks.length === 0) {\n    onFinished();\n    return {\n      activeIndex,\n      result\n    };\n  }\n  function updateResult(state, res) {\n    activeIndex.value++;\n    result[activeIndex.value].data = res;\n    result[activeIndex.value].state = state;\n  }\n  tasks.reduce((prev, curr) => {\n    return prev.then((prevRes) => {\n      var _a;\n      if (signal == null ? void 0 : signal.aborted) {\n        updateResult(promiseState.aborted, new Error(\"aborted\"));\n        return;\n      }\n      if (((_a = result[activeIndex.value]) == null ? void 0 : _a.state) === promiseState.rejected && interrupt) {\n        onFinished();\n        return;\n      }\n      const done = curr(prevRes).then((currentRes) => {\n        updateResult(promiseState.fulfilled, currentRes);\n        if (activeIndex.value === tasks.length - 1)\n          onFinished();\n        return currentRes;\n      });\n      if (!signal)\n        return done;\n      return Promise.race([done, whenAborted(signal)]);\n    }).catch((e) => {\n      if (signal == null ? void 0 : signal.aborted) {\n        updateResult(promiseState.aborted, e);\n        return e;\n      }\n      updateResult(promiseState.rejected, e);\n      onError();\n      return e;\n    });\n  }, Promise.resolve());\n  return {\n    activeIndex,\n    result\n  };\n}\nfunction whenAborted(signal) {\n  return new Promise((resolve, reject) => {\n    const error = new Error(\"aborted\");\n    if (signal.aborted)\n      reject(error);\n    else\n      signal.addEventListener(\"abort\", () => reject(error), { once: true });\n  });\n}\n\nfunction useAsyncState(promise, initialState, options) {\n  const {\n    immediate = true,\n    delay = 0,\n    onError = noop,\n    onSuccess = noop,\n    resetOnExecute = true,\n    shallow = true,\n    throwError\n  } = options != null ? options : {};\n  const state = shallow ? shallowRef(initialState) : ref(initialState);\n  const isReady = shallowRef(false);\n  const isLoading = shallowRef(false);\n  const error = shallowRef(void 0);\n  async function execute(delay2 = 0, ...args) {\n    if (resetOnExecute)\n      state.value = initialState;\n    error.value = void 0;\n    isReady.value = false;\n    isLoading.value = true;\n    if (delay2 > 0)\n      await promiseTimeout(delay2);\n    const _promise = typeof promise === \"function\" ? promise(...args) : promise;\n    try {\n      const data = await _promise;\n      state.value = data;\n      isReady.value = true;\n      onSuccess(data);\n    } catch (e) {\n      error.value = e;\n      onError(e);\n      if (throwError)\n        throw e;\n    } finally {\n      isLoading.value = false;\n    }\n    return state.value;\n  }\n  if (immediate) {\n    execute(delay);\n  }\n  const shell = {\n    state,\n    isReady,\n    isLoading,\n    error,\n    execute\n  };\n  function waitUntilIsLoaded() {\n    return new Promise((resolve, reject) => {\n      until(isLoading).toBe(false).then(() => resolve(shell)).catch(reject);\n    });\n  }\n  return {\n    ...shell,\n    then(onFulfilled, onRejected) {\n      return waitUntilIsLoaded().then(onFulfilled, onRejected);\n    }\n  };\n}\n\nconst defaults = {\n  array: (v) => JSON.stringify(v),\n  object: (v) => JSON.stringify(v),\n  set: (v) => JSON.stringify(Array.from(v)),\n  map: (v) => JSON.stringify(Object.fromEntries(v)),\n  null: () => \"\"\n};\nfunction getDefaultSerialization(target) {\n  if (!target)\n    return defaults.null;\n  if (target instanceof Map)\n    return defaults.map;\n  else if (target instanceof Set)\n    return defaults.set;\n  else if (Array.isArray(target))\n    return defaults.array;\n  else\n    return defaults.object;\n}\n\nfunction useBase64(target, options) {\n  const base64 = shallowRef(\"\");\n  const promise = shallowRef();\n  function execute() {\n    if (!isClient)\n      return;\n    promise.value = new Promise((resolve, reject) => {\n      try {\n        const _target = toValue(target);\n        if (_target == null) {\n          resolve(\"\");\n        } else if (typeof _target === \"string\") {\n          resolve(blobToBase64(new Blob([_target], { type: \"text/plain\" })));\n        } else if (_target instanceof Blob) {\n          resolve(blobToBase64(_target));\n        } else if (_target instanceof ArrayBuffer) {\n          resolve(window.btoa(String.fromCharCode(...new Uint8Array(_target))));\n        } else if (_target instanceof HTMLCanvasElement) {\n          resolve(_target.toDataURL(options == null ? void 0 : options.type, options == null ? void 0 : options.quality));\n        } else if (_target instanceof HTMLImageElement) {\n          const img = _target.cloneNode(false);\n          img.crossOrigin = \"Anonymous\";\n          imgLoaded(img).then(() => {\n            const canvas = document.createElement(\"canvas\");\n            const ctx = canvas.getContext(\"2d\");\n            canvas.width = img.width;\n            canvas.height = img.height;\n            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\n            resolve(canvas.toDataURL(options == null ? void 0 : options.type, options == null ? void 0 : options.quality));\n          }).catch(reject);\n        } else if (typeof _target === \"object\") {\n          const _serializeFn = (options == null ? void 0 : options.serializer) || getDefaultSerialization(_target);\n          const serialized = _serializeFn(_target);\n          return resolve(blobToBase64(new Blob([serialized], { type: \"application/json\" })));\n        } else {\n          reject(new Error(\"target is unsupported types\"));\n        }\n      } catch (error) {\n        reject(error);\n      }\n    });\n    promise.value.then((res) => {\n      base64.value = (options == null ? void 0 : options.dataUrl) === false ? res.replace(/^data:.*?;base64,/, \"\") : res;\n    });\n    return promise.value;\n  }\n  if (isRef(target) || typeof target === \"function\")\n    watch(target, execute, { immediate: true });\n  else\n    execute();\n  return {\n    base64,\n    promise,\n    execute\n  };\n}\nfunction imgLoaded(img) {\n  return new Promise((resolve, reject) => {\n    if (!img.complete) {\n      img.onload = () => {\n        resolve();\n      };\n      img.onerror = reject;\n    } else {\n      resolve();\n    }\n  });\n}\nfunction blobToBase64(blob) {\n  return new Promise((resolve, reject) => {\n    const fr = new FileReader();\n    fr.onload = (e) => {\n      resolve(e.target.result);\n    };\n    fr.onerror = reject;\n    fr.readAsDataURL(blob);\n  });\n}\n\nfunction useBattery(options = {}) {\n  const { navigator = defaultNavigator } = options;\n  const events = [\"chargingchange\", \"chargingtimechange\", \"dischargingtimechange\", \"levelchange\"];\n  const isSupported = useSupported(() => navigator && \"getBattery\" in navigator && typeof navigator.getBattery === \"function\");\n  const charging = shallowRef(false);\n  const chargingTime = shallowRef(0);\n  const dischargingTime = shallowRef(0);\n  const level = shallowRef(1);\n  let battery;\n  function updateBatteryInfo() {\n    charging.value = this.charging;\n    chargingTime.value = this.chargingTime || 0;\n    dischargingTime.value = this.dischargingTime || 0;\n    level.value = this.level;\n  }\n  if (isSupported.value) {\n    navigator.getBattery().then((_battery) => {\n      battery = _battery;\n      updateBatteryInfo.call(battery);\n      useEventListener(battery, events, updateBatteryInfo, { passive: true });\n    });\n  }\n  return {\n    isSupported,\n    charging,\n    chargingTime,\n    dischargingTime,\n    level\n  };\n}\n\nfunction useBluetooth(options) {\n  let {\n    acceptAllDevices = false\n  } = options || {};\n  const {\n    filters = void 0,\n    optionalServices = void 0,\n    navigator = defaultNavigator\n  } = options || {};\n  const isSupported = useSupported(() => navigator && \"bluetooth\" in navigator);\n  const device = shallowRef();\n  const error = shallowRef(null);\n  watch(device, () => {\n    connectToBluetoothGATTServer();\n  });\n  async function requestDevice() {\n    if (!isSupported.value)\n      return;\n    error.value = null;\n    if (filters && filters.length > 0)\n      acceptAllDevices = false;\n    try {\n      device.value = await (navigator == null ? void 0 : navigator.bluetooth.requestDevice({\n        acceptAllDevices,\n        filters,\n        optionalServices\n      }));\n    } catch (err) {\n      error.value = err;\n    }\n  }\n  const server = shallowRef();\n  const isConnected = shallowRef(false);\n  function reset() {\n    isConnected.value = false;\n    device.value = void 0;\n    server.value = void 0;\n  }\n  async function connectToBluetoothGATTServer() {\n    error.value = null;\n    if (device.value && device.value.gatt) {\n      useEventListener(device, \"gattserverdisconnected\", reset, { passive: true });\n      try {\n        server.value = await device.value.gatt.connect();\n        isConnected.value = server.value.connected;\n      } catch (err) {\n        error.value = err;\n      }\n    }\n  }\n  tryOnMounted(() => {\n    var _a;\n    if (device.value)\n      (_a = device.value.gatt) == null ? void 0 : _a.connect();\n  });\n  tryOnScopeDispose(() => {\n    var _a;\n    if (device.value)\n      (_a = device.value.gatt) == null ? void 0 : _a.disconnect();\n  });\n  return {\n    isSupported,\n    isConnected: readonly(isConnected),\n    // Device:\n    device,\n    requestDevice,\n    // Server:\n    server,\n    // Errors:\n    error\n  };\n}\n\nconst ssrWidthSymbol = Symbol(\"vueuse-ssr-width\");\nfunction useSSRWidth() {\n  const ssrWidth = hasInjectionContext() ? injectLocal(ssrWidthSymbol, null) : null;\n  return typeof ssrWidth === \"number\" ? ssrWidth : void 0;\n}\nfunction provideSSRWidth(width, app) {\n  if (app !== void 0) {\n    app.provide(ssrWidthSymbol, width);\n  } else {\n    provideLocal(ssrWidthSymbol, width);\n  }\n}\n\nfunction useMediaQuery(query, options = {}) {\n  const { window = defaultWindow, ssrWidth = useSSRWidth() } = options;\n  const isSupported = useSupported(() => window && \"matchMedia\" in window && typeof window.matchMedia === \"function\");\n  const ssrSupport = shallowRef(typeof ssrWidth === \"number\");\n  const mediaQuery = shallowRef();\n  const matches = shallowRef(false);\n  const handler = (event) => {\n    matches.value = event.matches;\n  };\n  watchEffect(() => {\n    if (ssrSupport.value) {\n      ssrSupport.value = !isSupported.value;\n      const queryStrings = toValue(query).split(\",\");\n      matches.value = queryStrings.some((queryString) => {\n        const not = queryString.includes(\"not all\");\n        const minWidth = queryString.match(/\\(\\s*min-width:\\s*(-?\\d+(?:\\.\\d*)?[a-z]+\\s*)\\)/);\n        const maxWidth = queryString.match(/\\(\\s*max-width:\\s*(-?\\d+(?:\\.\\d*)?[a-z]+\\s*)\\)/);\n        let res = Boolean(minWidth || maxWidth);\n        if (minWidth && res) {\n          res = ssrWidth >= pxValue(minWidth[1]);\n        }\n        if (maxWidth && res) {\n          res = ssrWidth <= pxValue(maxWidth[1]);\n        }\n        return not ? !res : res;\n      });\n      return;\n    }\n    if (!isSupported.value)\n      return;\n    mediaQuery.value = window.matchMedia(toValue(query));\n    matches.value = mediaQuery.value.matches;\n  });\n  useEventListener(mediaQuery, \"change\", handler, { passive: true });\n  return computed(() => matches.value);\n}\n\nconst breakpointsTailwind = {\n  \"sm\": 640,\n  \"md\": 768,\n  \"lg\": 1024,\n  \"xl\": 1280,\n  \"2xl\": 1536\n};\nconst breakpointsBootstrapV5 = {\n  xs: 0,\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200,\n  xxl: 1400\n};\nconst breakpointsVuetifyV2 = {\n  xs: 0,\n  sm: 600,\n  md: 960,\n  lg: 1264,\n  xl: 1904\n};\nconst breakpointsVuetifyV3 = {\n  xs: 0,\n  sm: 600,\n  md: 960,\n  lg: 1280,\n  xl: 1920,\n  xxl: 2560\n};\nconst breakpointsVuetify = breakpointsVuetifyV2;\nconst breakpointsAntDesign = {\n  xs: 480,\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200,\n  xxl: 1600\n};\nconst breakpointsQuasar = {\n  xs: 0,\n  sm: 600,\n  md: 1024,\n  lg: 1440,\n  xl: 1920\n};\nconst breakpointsSematic = {\n  mobileS: 320,\n  mobileM: 375,\n  mobileL: 425,\n  tablet: 768,\n  laptop: 1024,\n  laptopL: 1440,\n  desktop4K: 2560\n};\nconst breakpointsMasterCss = {\n  \"3xs\": 360,\n  \"2xs\": 480,\n  \"xs\": 600,\n  \"sm\": 768,\n  \"md\": 1024,\n  \"lg\": 1280,\n  \"xl\": 1440,\n  \"2xl\": 1600,\n  \"3xl\": 1920,\n  \"4xl\": 2560\n};\nconst breakpointsPrimeFlex = {\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200\n};\nconst breakpointsElement = {\n  xs: 0,\n  sm: 768,\n  md: 992,\n  lg: 1200,\n  xl: 1920\n};\n\nfunction useBreakpoints(breakpoints, options = {}) {\n  function getValue(k, delta) {\n    let v = toValue(breakpoints[toValue(k)]);\n    if (delta != null)\n      v = increaseWithUnit(v, delta);\n    if (typeof v === \"number\")\n      v = `${v}px`;\n    return v;\n  }\n  const { window = defaultWindow, strategy = \"min-width\", ssrWidth = useSSRWidth() } = options;\n  const ssrSupport = typeof ssrWidth === \"number\";\n  const mounted = ssrSupport ? shallowRef(false) : { value: true };\n  if (ssrSupport) {\n    tryOnMounted(() => mounted.value = !!window);\n  }\n  function match(query, size) {\n    if (!mounted.value && ssrSupport) {\n      return query === \"min\" ? ssrWidth >= pxValue(size) : ssrWidth <= pxValue(size);\n    }\n    if (!window)\n      return false;\n    return window.matchMedia(`(${query}-width: ${size})`).matches;\n  }\n  const greaterOrEqual = (k) => {\n    return useMediaQuery(() => `(min-width: ${getValue(k)})`, options);\n  };\n  const smallerOrEqual = (k) => {\n    return useMediaQuery(() => `(max-width: ${getValue(k)})`, options);\n  };\n  const shortcutMethods = Object.keys(breakpoints).reduce((shortcuts, k) => {\n    Object.defineProperty(shortcuts, k, {\n      get: () => strategy === \"min-width\" ? greaterOrEqual(k) : smallerOrEqual(k),\n      enumerable: true,\n      configurable: true\n    });\n    return shortcuts;\n  }, {});\n  function current() {\n    const points = Object.keys(breakpoints).map((k) => [k, shortcutMethods[k], pxValue(getValue(k))]).sort((a, b) => a[2] - b[2]);\n    return computed(() => points.filter(([, v]) => v.value).map(([k]) => k));\n  }\n  return Object.assign(shortcutMethods, {\n    greaterOrEqual,\n    smallerOrEqual,\n    greater(k) {\n      return useMediaQuery(() => `(min-width: ${getValue(k, 0.1)})`, options);\n    },\n    smaller(k) {\n      return useMediaQuery(() => `(max-width: ${getValue(k, -0.1)})`, options);\n    },\n    between(a, b) {\n      return useMediaQuery(() => `(min-width: ${getValue(a)}) and (max-width: ${getValue(b, -0.1)})`, options);\n    },\n    isGreater(k) {\n      return match(\"min\", getValue(k, 0.1));\n    },\n    isGreaterOrEqual(k) {\n      return match(\"min\", getValue(k));\n    },\n    isSmaller(k) {\n      return match(\"max\", getValue(k, -0.1));\n    },\n    isSmallerOrEqual(k) {\n      return match(\"max\", getValue(k));\n    },\n    isInBetween(a, b) {\n      return match(\"min\", getValue(a)) && match(\"max\", getValue(b, -0.1));\n    },\n    current,\n    active() {\n      const bps = current();\n      return computed(() => bps.value.length === 0 ? \"\" : bps.value.at(strategy === \"min-width\" ? -1 : 0));\n    }\n  });\n}\n\nfunction useBroadcastChannel(options) {\n  const {\n    name,\n    window = defaultWindow\n  } = options;\n  const isSupported = useSupported(() => window && \"BroadcastChannel\" in window);\n  const isClosed = shallowRef(false);\n  const channel = ref();\n  const data = ref();\n  const error = shallowRef(null);\n  const post = (data2) => {\n    if (channel.value)\n      channel.value.postMessage(data2);\n  };\n  const close = () => {\n    if (channel.value)\n      channel.value.close();\n    isClosed.value = true;\n  };\n  if (isSupported.value) {\n    tryOnMounted(() => {\n      error.value = null;\n      channel.value = new BroadcastChannel(name);\n      const listenerOptions = {\n        passive: true\n      };\n      useEventListener(channel, \"message\", (e) => {\n        data.value = e.data;\n      }, listenerOptions);\n      useEventListener(channel, \"messageerror\", (e) => {\n        error.value = e;\n      }, listenerOptions);\n      useEventListener(channel, \"close\", () => {\n        isClosed.value = true;\n      }, listenerOptions);\n    });\n  }\n  tryOnScopeDispose(() => {\n    close();\n  });\n  return {\n    isSupported,\n    channel,\n    data,\n    post,\n    close,\n    error,\n    isClosed\n  };\n}\n\nconst WRITABLE_PROPERTIES = [\n  \"hash\",\n  \"host\",\n  \"hostname\",\n  \"href\",\n  \"pathname\",\n  \"port\",\n  \"protocol\",\n  \"search\"\n];\nfunction useBrowserLocation(options = {}) {\n  const { window = defaultWindow } = options;\n  const refs = Object.fromEntries(\n    WRITABLE_PROPERTIES.map((key) => [key, ref()])\n  );\n  for (const [key, ref] of objectEntries(refs)) {\n    watch(ref, (value) => {\n      if (!(window == null ? void 0 : window.location) || window.location[key] === value)\n        return;\n      window.location[key] = value;\n    });\n  }\n  const buildState = (trigger) => {\n    var _a;\n    const { state: state2, length } = (window == null ? void 0 : window.history) || {};\n    const { origin } = (window == null ? void 0 : window.location) || {};\n    for (const key of WRITABLE_PROPERTIES)\n      refs[key].value = (_a = window == null ? void 0 : window.location) == null ? void 0 : _a[key];\n    return reactive({\n      trigger,\n      state: state2,\n      length,\n      origin,\n      ...refs\n    });\n  };\n  const state = ref(buildState(\"load\"));\n  if (window) {\n    const listenerOptions = { passive: true };\n    useEventListener(window, \"popstate\", () => state.value = buildState(\"popstate\"), listenerOptions);\n    useEventListener(window, \"hashchange\", () => state.value = buildState(\"hashchange\"), listenerOptions);\n  }\n  return state;\n}\n\nfunction useCached(refValue, comparator = (a, b) => a === b, options) {\n  const { deepRefs = true, ...watchOptions } = options || {};\n  const cachedValue = createRef(refValue.value, deepRefs);\n  watch(() => refValue.value, (value) => {\n    if (!comparator(value, cachedValue.value))\n      cachedValue.value = value;\n  }, watchOptions);\n  return cachedValue;\n}\n\nfunction usePermission(permissionDesc, options = {}) {\n  const {\n    controls = false,\n    navigator = defaultNavigator\n  } = options;\n  const isSupported = useSupported(() => navigator && \"permissions\" in navigator);\n  const permissionStatus = shallowRef();\n  const desc = typeof permissionDesc === \"string\" ? { name: permissionDesc } : permissionDesc;\n  const state = shallowRef();\n  const update = () => {\n    var _a, _b;\n    state.value = (_b = (_a = permissionStatus.value) == null ? void 0 : _a.state) != null ? _b : \"prompt\";\n  };\n  useEventListener(permissionStatus, \"change\", update, { passive: true });\n  const query = createSingletonPromise(async () => {\n    if (!isSupported.value)\n      return;\n    if (!permissionStatus.value) {\n      try {\n        permissionStatus.value = await navigator.permissions.query(desc);\n      } catch (e) {\n        permissionStatus.value = void 0;\n      } finally {\n        update();\n      }\n    }\n    if (controls)\n      return toRaw(permissionStatus.value);\n  });\n  query();\n  if (controls) {\n    return {\n      state,\n      isSupported,\n      query\n    };\n  } else {\n    return state;\n  }\n}\n\nfunction useClipboard(options = {}) {\n  const {\n    navigator = defaultNavigator,\n    read = false,\n    source,\n    copiedDuring = 1500,\n    legacy = false\n  } = options;\n  const isClipboardApiSupported = useSupported(() => navigator && \"clipboard\" in navigator);\n  const permissionRead = usePermission(\"clipboard-read\");\n  const permissionWrite = usePermission(\"clipboard-write\");\n  const isSupported = computed(() => isClipboardApiSupported.value || legacy);\n  const text = shallowRef(\"\");\n  const copied = shallowRef(false);\n  const timeout = useTimeoutFn(() => copied.value = false, copiedDuring, { immediate: false });\n  async function updateText() {\n    let useLegacy = !(isClipboardApiSupported.value && isAllowed(permissionRead.value));\n    if (!useLegacy) {\n      try {\n        text.value = await navigator.clipboard.readText();\n      } catch (e) {\n        useLegacy = true;\n      }\n    }\n    if (useLegacy) {\n      text.value = legacyRead();\n    }\n  }\n  if (isSupported.value && read)\n    useEventListener([\"copy\", \"cut\"], updateText, { passive: true });\n  async function copy(value = toValue(source)) {\n    if (isSupported.value && value != null) {\n      let useLegacy = !(isClipboardApiSupported.value && isAllowed(permissionWrite.value));\n      if (!useLegacy) {\n        try {\n          await navigator.clipboard.writeText(value);\n        } catch (e) {\n          useLegacy = true;\n        }\n      }\n      if (useLegacy)\n        legacyCopy(value);\n      text.value = value;\n      copied.value = true;\n      timeout.start();\n    }\n  }\n  function legacyCopy(value) {\n    const ta = document.createElement(\"textarea\");\n    ta.value = value != null ? value : \"\";\n    ta.style.position = \"absolute\";\n    ta.style.opacity = \"0\";\n    document.body.appendChild(ta);\n    ta.select();\n    document.execCommand(\"copy\");\n    ta.remove();\n  }\n  function legacyRead() {\n    var _a, _b, _c;\n    return (_c = (_b = (_a = document == null ? void 0 : document.getSelection) == null ? void 0 : _a.call(document)) == null ? void 0 : _b.toString()) != null ? _c : \"\";\n  }\n  function isAllowed(status) {\n    return status === \"granted\" || status === \"prompt\";\n  }\n  return {\n    isSupported,\n    text,\n    copied,\n    copy\n  };\n}\n\nfunction useClipboardItems(options = {}) {\n  const {\n    navigator = defaultNavigator,\n    read = false,\n    source,\n    copiedDuring = 1500\n  } = options;\n  const isSupported = useSupported(() => navigator && \"clipboard\" in navigator);\n  const content = ref([]);\n  const copied = shallowRef(false);\n  const timeout = useTimeoutFn(() => copied.value = false, copiedDuring, { immediate: false });\n  function updateContent() {\n    if (isSupported.value) {\n      navigator.clipboard.read().then((items) => {\n        content.value = items;\n      });\n    }\n  }\n  if (isSupported.value && read)\n    useEventListener([\"copy\", \"cut\"], updateContent, { passive: true });\n  async function copy(value = toValue(source)) {\n    if (isSupported.value && value != null) {\n      await navigator.clipboard.write(value);\n      content.value = value;\n      copied.value = true;\n      timeout.start();\n    }\n  }\n  return {\n    isSupported,\n    content,\n    copied,\n    copy\n  };\n}\n\nfunction cloneFnJSON(source) {\n  return JSON.parse(JSON.stringify(source));\n}\nfunction useCloned(source, options = {}) {\n  const cloned = ref({});\n  const isModified = shallowRef(false);\n  let _lastSync = false;\n  const {\n    manual,\n    clone = cloneFnJSON,\n    // watch options\n    deep = true,\n    immediate = true\n  } = options;\n  watch(cloned, () => {\n    if (_lastSync) {\n      _lastSync = false;\n      return;\n    }\n    isModified.value = true;\n  }, {\n    deep: true,\n    flush: \"sync\"\n  });\n  function sync() {\n    _lastSync = true;\n    isModified.value = false;\n    cloned.value = clone(toValue(source));\n  }\n  if (!manual && (isRef(source) || typeof source === \"function\")) {\n    watch(source, sync, {\n      ...options,\n      deep,\n      immediate\n    });\n  } else {\n    sync();\n  }\n  return { cloned, isModified, sync };\n}\n\nconst _global = typeof globalThis !== \"undefined\" ? globalThis : typeof window !== \"undefined\" ? window : typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : {};\nconst globalKey = \"__vueuse_ssr_handlers__\";\nconst handlers = /* @__PURE__ */ getHandlers();\nfunction getHandlers() {\n  if (!(globalKey in _global))\n    _global[globalKey] = _global[globalKey] || {};\n  return _global[globalKey];\n}\nfunction getSSRHandler(key, fallback) {\n  return handlers[key] || fallback;\n}\nfunction setSSRHandler(key, fn) {\n  handlers[key] = fn;\n}\n\nfunction usePreferredDark(options) {\n  return useMediaQuery(\"(prefers-color-scheme: dark)\", options);\n}\n\nfunction guessSerializerType(rawInit) {\n  return rawInit == null ? \"any\" : rawInit instanceof Set ? \"set\" : rawInit instanceof Map ? \"map\" : rawInit instanceof Date ? \"date\" : typeof rawInit === \"boolean\" ? \"boolean\" : typeof rawInit === \"string\" ? \"string\" : typeof rawInit === \"object\" ? \"object\" : !Number.isNaN(rawInit) ? \"number\" : \"any\";\n}\n\nconst StorageSerializers = {\n  boolean: {\n    read: (v) => v === \"true\",\n    write: (v) => String(v)\n  },\n  object: {\n    read: (v) => JSON.parse(v),\n    write: (v) => JSON.stringify(v)\n  },\n  number: {\n    read: (v) => Number.parseFloat(v),\n    write: (v) => String(v)\n  },\n  any: {\n    read: (v) => v,\n    write: (v) => String(v)\n  },\n  string: {\n    read: (v) => v,\n    write: (v) => String(v)\n  },\n  map: {\n    read: (v) => new Map(JSON.parse(v)),\n    write: (v) => JSON.stringify(Array.from(v.entries()))\n  },\n  set: {\n    read: (v) => new Set(JSON.parse(v)),\n    write: (v) => JSON.stringify(Array.from(v))\n  },\n  date: {\n    read: (v) => new Date(v),\n    write: (v) => v.toISOString()\n  }\n};\nconst customStorageEventName = \"vueuse-storage\";\nfunction useStorage(key, defaults, storage, options = {}) {\n  var _a;\n  const {\n    flush = \"pre\",\n    deep = true,\n    listenToStorageChanges = true,\n    writeDefaults = true,\n    mergeDefaults = false,\n    shallow,\n    window = defaultWindow,\n    eventFilter,\n    onError = (e) => {\n      console.error(e);\n    },\n    initOnMounted\n  } = options;\n  const data = (shallow ? shallowRef : ref)(typeof defaults === \"function\" ? defaults() : defaults);\n  const keyComputed = computed(() => toValue(key));\n  if (!storage) {\n    try {\n      storage = getSSRHandler(\"getDefaultStorage\", () => {\n        var _a2;\n        return (_a2 = defaultWindow) == null ? void 0 : _a2.localStorage;\n      })();\n    } catch (e) {\n      onError(e);\n    }\n  }\n  if (!storage)\n    return data;\n  const rawInit = toValue(defaults);\n  const type = guessSerializerType(rawInit);\n  const serializer = (_a = options.serializer) != null ? _a : StorageSerializers[type];\n  const { pause: pauseWatch, resume: resumeWatch } = pausableWatch(\n    data,\n    () => write(data.value),\n    { flush, deep, eventFilter }\n  );\n  watch(keyComputed, () => update(), { flush });\n  if (window && listenToStorageChanges) {\n    tryOnMounted(() => {\n      if (storage instanceof Storage)\n        useEventListener(window, \"storage\", update, { passive: true });\n      else\n        useEventListener(window, customStorageEventName, updateFromCustomEvent);\n      if (initOnMounted)\n        update();\n    });\n  }\n  if (!initOnMounted)\n    update();\n  function dispatchWriteEvent(oldValue, newValue) {\n    if (window) {\n      const payload = {\n        key: keyComputed.value,\n        oldValue,\n        newValue,\n        storageArea: storage\n      };\n      window.dispatchEvent(storage instanceof Storage ? new StorageEvent(\"storage\", payload) : new CustomEvent(customStorageEventName, {\n        detail: payload\n      }));\n    }\n  }\n  function write(v) {\n    try {\n      const oldValue = storage.getItem(keyComputed.value);\n      if (v == null) {\n        dispatchWriteEvent(oldValue, null);\n        storage.removeItem(keyComputed.value);\n      } else {\n        const serialized = serializer.write(v);\n        if (oldValue !== serialized) {\n          storage.setItem(keyComputed.value, serialized);\n          dispatchWriteEvent(oldValue, serialized);\n        }\n      }\n    } catch (e) {\n      onError(e);\n    }\n  }\n  function read(event) {\n    const rawValue = event ? event.newValue : storage.getItem(keyComputed.value);\n    if (rawValue == null) {\n      if (writeDefaults && rawInit != null)\n        storage.setItem(keyComputed.value, serializer.write(rawInit));\n      return rawInit;\n    } else if (!event && mergeDefaults) {\n      const value = serializer.read(rawValue);\n      if (typeof mergeDefaults === \"function\")\n        return mergeDefaults(value, rawInit);\n      else if (type === \"object\" && !Array.isArray(value))\n        return { ...rawInit, ...value };\n      return value;\n    } else if (typeof rawValue !== \"string\") {\n      return rawValue;\n    } else {\n      return serializer.read(rawValue);\n    }\n  }\n  function update(event) {\n    if (event && event.storageArea !== storage)\n      return;\n    if (event && event.key == null) {\n      data.value = rawInit;\n      return;\n    }\n    if (event && event.key !== keyComputed.value)\n      return;\n    pauseWatch();\n    try {\n      if ((event == null ? void 0 : event.newValue) !== serializer.write(data.value))\n        data.value = read(event);\n    } catch (e) {\n      onError(e);\n    } finally {\n      if (event)\n        nextTick(resumeWatch);\n      else\n        resumeWatch();\n    }\n  }\n  function updateFromCustomEvent(event) {\n    update(event.detail);\n  }\n  return data;\n}\n\nconst CSS_DISABLE_TRANS = \"*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}\";\nfunction useColorMode(options = {}) {\n  const {\n    selector = \"html\",\n    attribute = \"class\",\n    initialValue = \"auto\",\n    window = defaultWindow,\n    storage,\n    storageKey = \"vueuse-color-scheme\",\n    listenToStorageChanges = true,\n    storageRef,\n    emitAuto,\n    disableTransition = true\n  } = options;\n  const modes = {\n    auto: \"\",\n    light: \"light\",\n    dark: \"dark\",\n    ...options.modes || {}\n  };\n  const preferredDark = usePreferredDark({ window });\n  const system = computed(() => preferredDark.value ? \"dark\" : \"light\");\n  const store = storageRef || (storageKey == null ? toRef(initialValue) : useStorage(storageKey, initialValue, storage, { window, listenToStorageChanges }));\n  const state = computed(() => store.value === \"auto\" ? system.value : store.value);\n  const updateHTMLAttrs = getSSRHandler(\n    \"updateHTMLAttrs\",\n    (selector2, attribute2, value) => {\n      const el = typeof selector2 === \"string\" ? window == null ? void 0 : window.document.querySelector(selector2) : unrefElement(selector2);\n      if (!el)\n        return;\n      const classesToAdd = /* @__PURE__ */ new Set();\n      const classesToRemove = /* @__PURE__ */ new Set();\n      let attributeToChange = null;\n      if (attribute2 === \"class\") {\n        const current = value.split(/\\s/g);\n        Object.values(modes).flatMap((i) => (i || \"\").split(/\\s/g)).filter(Boolean).forEach((v) => {\n          if (current.includes(v))\n            classesToAdd.add(v);\n          else\n            classesToRemove.add(v);\n        });\n      } else {\n        attributeToChange = { key: attribute2, value };\n      }\n      if (classesToAdd.size === 0 && classesToRemove.size === 0 && attributeToChange === null)\n        return;\n      let style;\n      if (disableTransition) {\n        style = window.document.createElement(\"style\");\n        style.appendChild(document.createTextNode(CSS_DISABLE_TRANS));\n        window.document.head.appendChild(style);\n      }\n      for (const c of classesToAdd) {\n        el.classList.add(c);\n      }\n      for (const c of classesToRemove) {\n        el.classList.remove(c);\n      }\n      if (attributeToChange) {\n        el.setAttribute(attributeToChange.key, attributeToChange.value);\n      }\n      if (disableTransition) {\n        window.getComputedStyle(style).opacity;\n        document.head.removeChild(style);\n      }\n    }\n  );\n  function defaultOnChanged(mode) {\n    var _a;\n    updateHTMLAttrs(selector, attribute, (_a = modes[mode]) != null ? _a : mode);\n  }\n  function onChanged(mode) {\n    if (options.onChanged)\n      options.onChanged(mode, defaultOnChanged);\n    else\n      defaultOnChanged(mode);\n  }\n  watch(state, onChanged, { flush: \"post\", immediate: true });\n  tryOnMounted(() => onChanged(state.value));\n  const auto = computed({\n    get() {\n      return emitAuto ? store.value : state.value;\n    },\n    set(v) {\n      store.value = v;\n    }\n  });\n  return Object.assign(auto, { store, system, state });\n}\n\nfunction useConfirmDialog(revealed = shallowRef(false)) {\n  const confirmHook = createEventHook();\n  const cancelHook = createEventHook();\n  const revealHook = createEventHook();\n  let _resolve = noop;\n  const reveal = (data) => {\n    revealHook.trigger(data);\n    revealed.value = true;\n    return new Promise((resolve) => {\n      _resolve = resolve;\n    });\n  };\n  const confirm = (data) => {\n    revealed.value = false;\n    confirmHook.trigger(data);\n    _resolve({ data, isCanceled: false });\n  };\n  const cancel = (data) => {\n    revealed.value = false;\n    cancelHook.trigger(data);\n    _resolve({ data, isCanceled: true });\n  };\n  return {\n    isRevealed: computed(() => revealed.value),\n    reveal,\n    confirm,\n    cancel,\n    onReveal: revealHook.on,\n    onConfirm: confirmHook.on,\n    onCancel: cancelHook.on\n  };\n}\n\nfunction useCountdown(initialCountdown, options) {\n  var _a, _b;\n  const remaining = shallowRef(toValue(initialCountdown));\n  const intervalController = useIntervalFn(() => {\n    var _a2, _b2;\n    const value = remaining.value - 1;\n    remaining.value = value < 0 ? 0 : value;\n    (_a2 = options == null ? void 0 : options.onTick) == null ? void 0 : _a2.call(options);\n    if (remaining.value <= 0) {\n      intervalController.pause();\n      (_b2 = options == null ? void 0 : options.onComplete) == null ? void 0 : _b2.call(options);\n    }\n  }, (_a = options == null ? void 0 : options.interval) != null ? _a : 1e3, { immediate: (_b = options == null ? void 0 : options.immediate) != null ? _b : false });\n  const reset = (countdown) => {\n    var _a2;\n    remaining.value = (_a2 = toValue(countdown)) != null ? _a2 : toValue(initialCountdown);\n  };\n  const stop = () => {\n    intervalController.pause();\n    reset();\n  };\n  const resume = () => {\n    if (!intervalController.isActive.value) {\n      if (remaining.value > 0) {\n        intervalController.resume();\n      }\n    }\n  };\n  const start = (countdown) => {\n    reset(countdown);\n    intervalController.resume();\n  };\n  return {\n    remaining,\n    reset,\n    stop,\n    start,\n    pause: intervalController.pause,\n    resume,\n    isActive: intervalController.isActive\n  };\n}\n\nfunction useCssVar(prop, target, options = {}) {\n  const { window = defaultWindow, initialValue, observe = false } = options;\n  const variable = shallowRef(initialValue);\n  const elRef = computed(() => {\n    var _a;\n    return unrefElement(target) || ((_a = window == null ? void 0 : window.document) == null ? void 0 : _a.documentElement);\n  });\n  function updateCssVar() {\n    var _a;\n    const key = toValue(prop);\n    const el = toValue(elRef);\n    if (el && window && key) {\n      const value = (_a = window.getComputedStyle(el).getPropertyValue(key)) == null ? void 0 : _a.trim();\n      variable.value = value || variable.value || initialValue;\n    }\n  }\n  if (observe) {\n    useMutationObserver(elRef, updateCssVar, {\n      attributeFilter: [\"style\", \"class\"],\n      window\n    });\n  }\n  watch(\n    [elRef, () => toValue(prop)],\n    (_, old) => {\n      if (old[0] && old[1])\n        old[0].style.removeProperty(old[1]);\n      updateCssVar();\n    },\n    { immediate: true }\n  );\n  watch(\n    [variable, elRef],\n    ([val, el]) => {\n      const raw_prop = toValue(prop);\n      if ((el == null ? void 0 : el.style) && raw_prop) {\n        if (val == null)\n          el.style.removeProperty(raw_prop);\n        else\n          el.style.setProperty(raw_prop, val);\n      }\n    },\n    { immediate: true }\n  );\n  return variable;\n}\n\nfunction useCurrentElement(rootComponent) {\n  const vm = getCurrentInstance();\n  const currentElement = computedWithControl(\n    () => null,\n    () => rootComponent ? unrefElement(rootComponent) : vm.proxy.$el\n  );\n  onUpdated(currentElement.trigger);\n  onMounted(currentElement.trigger);\n  return currentElement;\n}\n\nfunction useCycleList(list, options) {\n  const state = shallowRef(getInitialValue());\n  const listRef = toRef(list);\n  const index = computed({\n    get() {\n      var _a;\n      const targetList = listRef.value;\n      let index2 = (options == null ? void 0 : options.getIndexOf) ? options.getIndexOf(state.value, targetList) : targetList.indexOf(state.value);\n      if (index2 < 0)\n        index2 = (_a = options == null ? void 0 : options.fallbackIndex) != null ? _a : 0;\n      return index2;\n    },\n    set(v) {\n      set(v);\n    }\n  });\n  function set(i) {\n    const targetList = listRef.value;\n    const length = targetList.length;\n    const index2 = (i % length + length) % length;\n    const value = targetList[index2];\n    state.value = value;\n    return value;\n  }\n  function shift(delta = 1) {\n    return set(index.value + delta);\n  }\n  function next(n = 1) {\n    return shift(n);\n  }\n  function prev(n = 1) {\n    return shift(-n);\n  }\n  function getInitialValue() {\n    var _a, _b;\n    return (_b = toValue((_a = options == null ? void 0 : options.initialValue) != null ? _a : toValue(list)[0])) != null ? _b : void 0;\n  }\n  watch(listRef, () => set(index.value));\n  return {\n    state,\n    index,\n    next,\n    prev,\n    go: set\n  };\n}\n\nfunction useDark(options = {}) {\n  const {\n    valueDark = \"dark\",\n    valueLight = \"\"\n  } = options;\n  const mode = useColorMode({\n    ...options,\n    onChanged: (mode2, defaultHandler) => {\n      var _a;\n      if (options.onChanged)\n        (_a = options.onChanged) == null ? void 0 : _a.call(options, mode2 === \"dark\", defaultHandler, mode2);\n      else\n        defaultHandler(mode2);\n    },\n    modes: {\n      dark: valueDark,\n      light: valueLight\n    }\n  });\n  const system = computed(() => mode.system.value);\n  const isDark = computed({\n    get() {\n      return mode.value === \"dark\";\n    },\n    set(v) {\n      const modeVal = v ? \"dark\" : \"light\";\n      if (system.value === modeVal)\n        mode.value = \"auto\";\n      else\n        mode.value = modeVal;\n    }\n  });\n  return isDark;\n}\n\nfunction fnBypass(v) {\n  return v;\n}\nfunction fnSetSource(source, value) {\n  return source.value = value;\n}\nfunction defaultDump(clone) {\n  return clone ? typeof clone === \"function\" ? clone : cloneFnJSON : fnBypass;\n}\nfunction defaultParse(clone) {\n  return clone ? typeof clone === \"function\" ? clone : cloneFnJSON : fnBypass;\n}\nfunction useManualRefHistory(source, options = {}) {\n  const {\n    clone = false,\n    dump = defaultDump(clone),\n    parse = defaultParse(clone),\n    setSource = fnSetSource\n  } = options;\n  function _createHistoryRecord() {\n    return markRaw({\n      snapshot: dump(source.value),\n      timestamp: timestamp()\n    });\n  }\n  const last = ref(_createHistoryRecord());\n  const undoStack = ref([]);\n  const redoStack = ref([]);\n  const _setSource = (record) => {\n    setSource(source, parse(record.snapshot));\n    last.value = record;\n  };\n  const commit = () => {\n    undoStack.value.unshift(last.value);\n    last.value = _createHistoryRecord();\n    if (options.capacity && undoStack.value.length > options.capacity)\n      undoStack.value.splice(options.capacity, Number.POSITIVE_INFINITY);\n    if (redoStack.value.length)\n      redoStack.value.splice(0, redoStack.value.length);\n  };\n  const clear = () => {\n    undoStack.value.splice(0, undoStack.value.length);\n    redoStack.value.splice(0, redoStack.value.length);\n  };\n  const undo = () => {\n    const state = undoStack.value.shift();\n    if (state) {\n      redoStack.value.unshift(last.value);\n      _setSource(state);\n    }\n  };\n  const redo = () => {\n    const state = redoStack.value.shift();\n    if (state) {\n      undoStack.value.unshift(last.value);\n      _setSource(state);\n    }\n  };\n  const reset = () => {\n    _setSource(last.value);\n  };\n  const history = computed(() => [last.value, ...undoStack.value]);\n  const canUndo = computed(() => undoStack.value.length > 0);\n  const canRedo = computed(() => redoStack.value.length > 0);\n  return {\n    source,\n    undoStack,\n    redoStack,\n    last,\n    history,\n    canUndo,\n    canRedo,\n    clear,\n    commit,\n    reset,\n    undo,\n    redo\n  };\n}\n\nfunction useRefHistory(source, options = {}) {\n  const {\n    deep = false,\n    flush = \"pre\",\n    eventFilter\n  } = options;\n  const {\n    eventFilter: composedFilter,\n    pause,\n    resume: resumeTracking,\n    isActive: isTracking\n  } = pausableFilter(eventFilter);\n  const {\n    ignoreUpdates,\n    ignorePrevAsyncUpdates,\n    stop\n  } = watchIgnorable(\n    source,\n    commit,\n    { deep, flush, eventFilter: composedFilter }\n  );\n  function setSource(source2, value) {\n    ignorePrevAsyncUpdates();\n    ignoreUpdates(() => {\n      source2.value = value;\n    });\n  }\n  const manualHistory = useManualRefHistory(source, { ...options, clone: options.clone || deep, setSource });\n  const { clear, commit: manualCommit } = manualHistory;\n  function commit() {\n    ignorePrevAsyncUpdates();\n    manualCommit();\n  }\n  function resume(commitNow) {\n    resumeTracking();\n    if (commitNow)\n      commit();\n  }\n  function batch(fn) {\n    let canceled = false;\n    const cancel = () => canceled = true;\n    ignoreUpdates(() => {\n      fn(cancel);\n    });\n    if (!canceled)\n      commit();\n  }\n  function dispose() {\n    stop();\n    clear();\n  }\n  return {\n    ...manualHistory,\n    isTracking,\n    pause,\n    resume,\n    commit,\n    batch,\n    dispose\n  };\n}\n\nfunction useDebouncedRefHistory(source, options = {}) {\n  const filter = options.debounce ? debounceFilter(options.debounce) : void 0;\n  const history = useRefHistory(source, { ...options, eventFilter: filter });\n  return {\n    ...history\n  };\n}\n\nfunction useDeviceMotion(options = {}) {\n  const {\n    window = defaultWindow,\n    requestPermissions = false,\n    eventFilter = bypassFilter\n  } = options;\n  const isSupported = useSupported(() => typeof DeviceMotionEvent !== \"undefined\");\n  const requirePermissions = useSupported(() => isSupported.value && \"requestPermission\" in DeviceMotionEvent && typeof DeviceMotionEvent.requestPermission === \"function\");\n  const permissionGranted = shallowRef(false);\n  const acceleration = ref({ x: null, y: null, z: null });\n  const rotationRate = ref({ alpha: null, beta: null, gamma: null });\n  const interval = shallowRef(0);\n  const accelerationIncludingGravity = ref({\n    x: null,\n    y: null,\n    z: null\n  });\n  function init() {\n    if (window) {\n      const onDeviceMotion = createFilterWrapper(\n        eventFilter,\n        (event) => {\n          var _a, _b, _c, _d, _e, _f, _g, _h, _i;\n          acceleration.value = {\n            x: ((_a = event.acceleration) == null ? void 0 : _a.x) || null,\n            y: ((_b = event.acceleration) == null ? void 0 : _b.y) || null,\n            z: ((_c = event.acceleration) == null ? void 0 : _c.z) || null\n          };\n          accelerationIncludingGravity.value = {\n            x: ((_d = event.accelerationIncludingGravity) == null ? void 0 : _d.x) || null,\n            y: ((_e = event.accelerationIncludingGravity) == null ? void 0 : _e.y) || null,\n            z: ((_f = event.accelerationIncludingGravity) == null ? void 0 : _f.z) || null\n          };\n          rotationRate.value = {\n            alpha: ((_g = event.rotationRate) == null ? void 0 : _g.alpha) || null,\n            beta: ((_h = event.rotationRate) == null ? void 0 : _h.beta) || null,\n            gamma: ((_i = event.rotationRate) == null ? void 0 : _i.gamma) || null\n          };\n          interval.value = event.interval;\n        }\n      );\n      useEventListener(window, \"devicemotion\", onDeviceMotion, { passive: true });\n    }\n  }\n  const ensurePermissions = async () => {\n    if (!requirePermissions.value)\n      permissionGranted.value = true;\n    if (permissionGranted.value)\n      return;\n    if (requirePermissions.value) {\n      const requestPermission = DeviceMotionEvent.requestPermission;\n      try {\n        const response = await requestPermission();\n        if (response === \"granted\") {\n          permissionGranted.value = true;\n          init();\n        }\n      } catch (error) {\n        console.error(error);\n      }\n    }\n  };\n  if (isSupported.value) {\n    if (requestPermissions && requirePermissions.value) {\n      ensurePermissions().then(() => init());\n    } else {\n      init();\n    }\n  }\n  return {\n    acceleration,\n    accelerationIncludingGravity,\n    rotationRate,\n    interval,\n    isSupported,\n    requirePermissions,\n    ensurePermissions,\n    permissionGranted\n  };\n}\n\nfunction useDeviceOrientation(options = {}) {\n  const { window = defaultWindow } = options;\n  const isSupported = useSupported(() => window && \"DeviceOrientationEvent\" in window);\n  const isAbsolute = shallowRef(false);\n  const alpha = shallowRef(null);\n  const beta = shallowRef(null);\n  const gamma = shallowRef(null);\n  if (window && isSupported.value) {\n    useEventListener(window, \"deviceorientation\", (event) => {\n      isAbsolute.value = event.absolute;\n      alpha.value = event.alpha;\n      beta.value = event.beta;\n      gamma.value = event.gamma;\n    }, { passive: true });\n  }\n  return {\n    isSupported,\n    isAbsolute,\n    alpha,\n    beta,\n    gamma\n  };\n}\n\nfunction useDevicePixelRatio(options = {}) {\n  const {\n    window = defaultWindow\n  } = options;\n  const pixelRatio = shallowRef(1);\n  const query = useMediaQuery(() => `(resolution: ${pixelRatio.value}dppx)`, options);\n  let stop = noop;\n  if (window) {\n    stop = watchImmediate(query, () => pixelRatio.value = window.devicePixelRatio);\n  }\n  return {\n    pixelRatio: readonly(pixelRatio),\n    stop\n  };\n}\n\nfunction useDevicesList(options = {}) {\n  const {\n    navigator = defaultNavigator,\n    requestPermissions = false,\n    constraints = { audio: true, video: true },\n    onUpdated\n  } = options;\n  const devices = ref([]);\n  const videoInputs = computed(() => devices.value.filter((i) => i.kind === \"videoinput\"));\n  const audioInputs = computed(() => devices.value.filter((i) => i.kind === \"audioinput\"));\n  const audioOutputs = computed(() => devices.value.filter((i) => i.kind === \"audiooutput\"));\n  const isSupported = useSupported(() => navigator && navigator.mediaDevices && navigator.mediaDevices.enumerateDevices);\n  const permissionGranted = shallowRef(false);\n  let stream;\n  async function update() {\n    if (!isSupported.value)\n      return;\n    devices.value = await navigator.mediaDevices.enumerateDevices();\n    onUpdated == null ? void 0 : onUpdated(devices.value);\n    if (stream) {\n      stream.getTracks().forEach((t) => t.stop());\n      stream = null;\n    }\n  }\n  async function ensurePermissions() {\n    const deviceName = constraints.video ? \"camera\" : \"microphone\";\n    if (!isSupported.value)\n      return false;\n    if (permissionGranted.value)\n      return true;\n    const { state, query } = usePermission(deviceName, { controls: true });\n    await query();\n    if (state.value !== \"granted\") {\n      let granted = true;\n      try {\n        stream = await navigator.mediaDevices.getUserMedia(constraints);\n      } catch (e) {\n        stream = null;\n        granted = false;\n      }\n      update();\n      permissionGranted.value = granted;\n    } else {\n      permissionGranted.value = true;\n    }\n    return permissionGranted.value;\n  }\n  if (isSupported.value) {\n    if (requestPermissions)\n      ensurePermissions();\n    useEventListener(navigator.mediaDevices, \"devicechange\", update, { passive: true });\n    update();\n  }\n  return {\n    devices,\n    ensurePermissions,\n    permissionGranted,\n    videoInputs,\n    audioInputs,\n    audioOutputs,\n    isSupported\n  };\n}\n\nfunction useDisplayMedia(options = {}) {\n  var _a;\n  const enabled = shallowRef((_a = options.enabled) != null ? _a : false);\n  const video = options.video;\n  const audio = options.audio;\n  const { navigator = defaultNavigator } = options;\n  const isSupported = useSupported(() => {\n    var _a2;\n    return (_a2 = navigator == null ? void 0 : navigator.mediaDevices) == null ? void 0 : _a2.getDisplayMedia;\n  });\n  const constraint = { audio, video };\n  const stream = shallowRef();\n  async function _start() {\n    var _a2;\n    if (!isSupported.value || stream.value)\n      return;\n    stream.value = await navigator.mediaDevices.getDisplayMedia(constraint);\n    (_a2 = stream.value) == null ? void 0 : _a2.getTracks().forEach((t) => useEventListener(t, \"ended\", stop, { passive: true }));\n    return stream.value;\n  }\n  async function _stop() {\n    var _a2;\n    (_a2 = stream.value) == null ? void 0 : _a2.getTracks().forEach((t) => t.stop());\n    stream.value = void 0;\n  }\n  function stop() {\n    _stop();\n    enabled.value = false;\n  }\n  async function start() {\n    await _start();\n    if (stream.value)\n      enabled.value = true;\n    return stream.value;\n  }\n  watch(\n    enabled,\n    (v) => {\n      if (v)\n        _start();\n      else\n        _stop();\n    },\n    { immediate: true }\n  );\n  return {\n    isSupported,\n    stream,\n    start,\n    stop,\n    enabled\n  };\n}\n\nfunction useDocumentVisibility(options = {}) {\n  const { document = defaultDocument } = options;\n  if (!document)\n    return shallowRef(\"visible\");\n  const visibility = shallowRef(document.visibilityState);\n  useEventListener(document, \"visibilitychange\", () => {\n    visibility.value = document.visibilityState;\n  }, { passive: true });\n  return visibility;\n}\n\nfunction useDraggable(target, options = {}) {\n  var _a;\n  const {\n    pointerTypes,\n    preventDefault,\n    stopPropagation,\n    exact,\n    onMove,\n    onEnd,\n    onStart,\n    initialValue,\n    axis = \"both\",\n    draggingElement = defaultWindow,\n    containerElement,\n    handle: draggingHandle = target,\n    buttons = [0]\n  } = options;\n  const position = ref(\n    (_a = toValue(initialValue)) != null ? _a : { x: 0, y: 0 }\n  );\n  const pressedDelta = ref();\n  const filterEvent = (e) => {\n    if (pointerTypes)\n      return pointerTypes.includes(e.pointerType);\n    return true;\n  };\n  const handleEvent = (e) => {\n    if (toValue(preventDefault))\n      e.preventDefault();\n    if (toValue(stopPropagation))\n      e.stopPropagation();\n  };\n  const start = (e) => {\n    var _a2;\n    if (!toValue(buttons).includes(e.button))\n      return;\n    if (toValue(options.disabled) || !filterEvent(e))\n      return;\n    if (toValue(exact) && e.target !== toValue(target))\n      return;\n    const container = toValue(containerElement);\n    const containerRect = (_a2 = container == null ? void 0 : container.getBoundingClientRect) == null ? void 0 : _a2.call(container);\n    const targetRect = toValue(target).getBoundingClientRect();\n    const pos = {\n      x: e.clientX - (container ? targetRect.left - containerRect.left + container.scrollLeft : targetRect.left),\n      y: e.clientY - (container ? targetRect.top - containerRect.top + container.scrollTop : targetRect.top)\n    };\n    if ((onStart == null ? void 0 : onStart(pos, e)) === false)\n      return;\n    pressedDelta.value = pos;\n    handleEvent(e);\n  };\n  const move = (e) => {\n    if (toValue(options.disabled) || !filterEvent(e))\n      return;\n    if (!pressedDelta.value)\n      return;\n    const container = toValue(containerElement);\n    const targetRect = toValue(target).getBoundingClientRect();\n    let { x, y } = position.value;\n    if (axis === \"x\" || axis === \"both\") {\n      x = e.clientX - pressedDelta.value.x;\n      if (container)\n        x = Math.min(Math.max(0, x), container.scrollWidth - targetRect.width);\n    }\n    if (axis === \"y\" || axis === \"both\") {\n      y = e.clientY - pressedDelta.value.y;\n      if (container)\n        y = Math.min(Math.max(0, y), container.scrollHeight - targetRect.height);\n    }\n    position.value = {\n      x,\n      y\n    };\n    onMove == null ? void 0 : onMove(position.value, e);\n    handleEvent(e);\n  };\n  const end = (e) => {\n    if (toValue(options.disabled) || !filterEvent(e))\n      return;\n    if (!pressedDelta.value)\n      return;\n    pressedDelta.value = void 0;\n    onEnd == null ? void 0 : onEnd(position.value, e);\n    handleEvent(e);\n  };\n  if (isClient) {\n    const config = () => {\n      var _a2;\n      return {\n        capture: (_a2 = options.capture) != null ? _a2 : true,\n        passive: !toValue(preventDefault)\n      };\n    };\n    useEventListener(draggingHandle, \"pointerdown\", start, config);\n    useEventListener(draggingElement, \"pointermove\", move, config);\n    useEventListener(draggingElement, \"pointerup\", end, config);\n  }\n  return {\n    ...toRefs(position),\n    position,\n    isDragging: computed(() => !!pressedDelta.value),\n    style: computed(\n      () => `left:${position.value.x}px;top:${position.value.y}px;`\n    )\n  };\n}\n\nfunction useDropZone(target, options = {}) {\n  var _a, _b;\n  const isOverDropZone = shallowRef(false);\n  const files = shallowRef(null);\n  let counter = 0;\n  let isValid = true;\n  if (isClient) {\n    const _options = typeof options === \"function\" ? { onDrop: options } : options;\n    const multiple = (_a = _options.multiple) != null ? _a : true;\n    const preventDefaultForUnhandled = (_b = _options.preventDefaultForUnhandled) != null ? _b : false;\n    const getFiles = (event) => {\n      var _a2, _b2;\n      const list = Array.from((_b2 = (_a2 = event.dataTransfer) == null ? void 0 : _a2.files) != null ? _b2 : []);\n      return list.length === 0 ? null : multiple ? list : [list[0]];\n    };\n    const checkDataTypes = (types) => {\n      const dataTypes = unref(_options.dataTypes);\n      if (typeof dataTypes === \"function\")\n        return dataTypes(types);\n      if (!(dataTypes == null ? void 0 : dataTypes.length))\n        return true;\n      if (types.length === 0)\n        return false;\n      return types.every(\n        (type) => dataTypes.some((allowedType) => type.includes(allowedType))\n      );\n    };\n    const checkValidity = (items) => {\n      const types = Array.from(items != null ? items : []).map((item) => item.type);\n      const dataTypesValid = checkDataTypes(types);\n      const multipleFilesValid = multiple || items.length <= 1;\n      return dataTypesValid && multipleFilesValid;\n    };\n    const isSafari = () => /^(?:(?!chrome|android).)*safari/i.test(navigator.userAgent) && !(\"chrome\" in window);\n    const handleDragEvent = (event, eventType) => {\n      var _a2, _b2, _c, _d, _e, _f;\n      const dataTransferItemList = (_a2 = event.dataTransfer) == null ? void 0 : _a2.items;\n      isValid = (_b2 = dataTransferItemList && checkValidity(dataTransferItemList)) != null ? _b2 : false;\n      if (preventDefaultForUnhandled) {\n        event.preventDefault();\n      }\n      if (!isSafari() && !isValid) {\n        if (event.dataTransfer) {\n          event.dataTransfer.dropEffect = \"none\";\n        }\n        return;\n      }\n      event.preventDefault();\n      if (event.dataTransfer) {\n        event.dataTransfer.dropEffect = \"copy\";\n      }\n      const currentFiles = getFiles(event);\n      switch (eventType) {\n        case \"enter\":\n          counter += 1;\n          isOverDropZone.value = true;\n          (_c = _options.onEnter) == null ? void 0 : _c.call(_options, null, event);\n          break;\n        case \"over\":\n          (_d = _options.onOver) == null ? void 0 : _d.call(_options, null, event);\n          break;\n        case \"leave\":\n          counter -= 1;\n          if (counter === 0)\n            isOverDropZone.value = false;\n          (_e = _options.onLeave) == null ? void 0 : _e.call(_options, null, event);\n          break;\n        case \"drop\":\n          counter = 0;\n          isOverDropZone.value = false;\n          if (isValid) {\n            files.value = currentFiles;\n            (_f = _options.onDrop) == null ? void 0 : _f.call(_options, currentFiles, event);\n          }\n          break;\n      }\n    };\n    useEventListener(target, \"dragenter\", (event) => handleDragEvent(event, \"enter\"));\n    useEventListener(target, \"dragover\", (event) => handleDragEvent(event, \"over\"));\n    useEventListener(target, \"dragleave\", (event) => handleDragEvent(event, \"leave\"));\n    useEventListener(target, \"drop\", (event) => handleDragEvent(event, \"drop\"));\n  }\n  return {\n    files,\n    isOverDropZone\n  };\n}\n\nfunction useResizeObserver(target, callback, options = {}) {\n  const { window = defaultWindow, ...observerOptions } = options;\n  let observer;\n  const isSupported = useSupported(() => window && \"ResizeObserver\" in window);\n  const cleanup = () => {\n    if (observer) {\n      observer.disconnect();\n      observer = void 0;\n    }\n  };\n  const targets = computed(() => {\n    const _targets = toValue(target);\n    return Array.isArray(_targets) ? _targets.map((el) => unrefElement(el)) : [unrefElement(_targets)];\n  });\n  const stopWatch = watch(\n    targets,\n    (els) => {\n      cleanup();\n      if (isSupported.value && window) {\n        observer = new ResizeObserver(callback);\n        for (const _el of els) {\n          if (_el)\n            observer.observe(_el, observerOptions);\n        }\n      }\n    },\n    { immediate: true, flush: \"post\" }\n  );\n  const stop = () => {\n    cleanup();\n    stopWatch();\n  };\n  tryOnScopeDispose(stop);\n  return {\n    isSupported,\n    stop\n  };\n}\n\nfunction useElementBounding(target, options = {}) {\n  const {\n    reset = true,\n    windowResize = true,\n    windowScroll = true,\n    immediate = true,\n    updateTiming = \"sync\"\n  } = options;\n  const height = shallowRef(0);\n  const bottom = shallowRef(0);\n  const left = shallowRef(0);\n  const right = shallowRef(0);\n  const top = shallowRef(0);\n  const width = shallowRef(0);\n  const x = shallowRef(0);\n  const y = shallowRef(0);\n  function recalculate() {\n    const el = unrefElement(target);\n    if (!el) {\n      if (reset) {\n        height.value = 0;\n        bottom.value = 0;\n        left.value = 0;\n        right.value = 0;\n        top.value = 0;\n        width.value = 0;\n        x.value = 0;\n        y.value = 0;\n      }\n      return;\n    }\n    const rect = el.getBoundingClientRect();\n    height.value = rect.height;\n    bottom.value = rect.bottom;\n    left.value = rect.left;\n    right.value = rect.right;\n    top.value = rect.top;\n    width.value = rect.width;\n    x.value = rect.x;\n    y.value = rect.y;\n  }\n  function update() {\n    if (updateTiming === \"sync\")\n      recalculate();\n    else if (updateTiming === \"next-frame\")\n      requestAnimationFrame(() => recalculate());\n  }\n  useResizeObserver(target, update);\n  watch(() => unrefElement(target), (ele) => !ele && update());\n  useMutationObserver(target, update, {\n    attributeFilter: [\"style\", \"class\"]\n  });\n  if (windowScroll)\n    useEventListener(\"scroll\", update, { capture: true, passive: true });\n  if (windowResize)\n    useEventListener(\"resize\", update, { passive: true });\n  tryOnMounted(() => {\n    if (immediate)\n      update();\n  });\n  return {\n    height,\n    bottom,\n    left,\n    right,\n    top,\n    width,\n    x,\n    y,\n    update\n  };\n}\n\nfunction useElementByPoint(options) {\n  const {\n    x,\n    y,\n    document = defaultDocument,\n    multiple,\n    interval = \"requestAnimationFrame\",\n    immediate = true\n  } = options;\n  const isSupported = useSupported(() => {\n    if (toValue(multiple))\n      return document && \"elementsFromPoint\" in document;\n    return document && \"elementFromPoint\" in document;\n  });\n  const element = shallowRef(null);\n  const cb = () => {\n    var _a, _b;\n    element.value = toValue(multiple) ? (_a = document == null ? void 0 : document.elementsFromPoint(toValue(x), toValue(y))) != null ? _a : [] : (_b = document == null ? void 0 : document.elementFromPoint(toValue(x), toValue(y))) != null ? _b : null;\n  };\n  const controls = interval === \"requestAnimationFrame\" ? useRafFn(cb, { immediate }) : useIntervalFn(cb, interval, { immediate });\n  return {\n    isSupported,\n    element,\n    ...controls\n  };\n}\n\nfunction useElementHover(el, options = {}) {\n  const {\n    delayEnter = 0,\n    delayLeave = 0,\n    triggerOnRemoval = false,\n    window = defaultWindow\n  } = options;\n  const isHovered = shallowRef(false);\n  let timer;\n  const toggle = (entering) => {\n    const delay = entering ? delayEnter : delayLeave;\n    if (timer) {\n      clearTimeout(timer);\n      timer = void 0;\n    }\n    if (delay)\n      timer = setTimeout(() => isHovered.value = entering, delay);\n    else\n      isHovered.value = entering;\n  };\n  if (!window)\n    return isHovered;\n  useEventListener(el, \"mouseenter\", () => toggle(true), { passive: true });\n  useEventListener(el, \"mouseleave\", () => toggle(false), { passive: true });\n  if (triggerOnRemoval) {\n    onElementRemoval(\n      computed(() => unrefElement(el)),\n      () => toggle(false)\n    );\n  }\n  return isHovered;\n}\n\nfunction useElementSize(target, initialSize = { width: 0, height: 0 }, options = {}) {\n  const { window = defaultWindow, box = \"content-box\" } = options;\n  const isSVG = computed(() => {\n    var _a, _b;\n    return (_b = (_a = unrefElement(target)) == null ? void 0 : _a.namespaceURI) == null ? void 0 : _b.includes(\"svg\");\n  });\n  const width = shallowRef(initialSize.width);\n  const height = shallowRef(initialSize.height);\n  const { stop: stop1 } = useResizeObserver(\n    target,\n    ([entry]) => {\n      const boxSize = box === \"border-box\" ? entry.borderBoxSize : box === \"content-box\" ? entry.contentBoxSize : entry.devicePixelContentBoxSize;\n      if (window && isSVG.value) {\n        const $elem = unrefElement(target);\n        if ($elem) {\n          const rect = $elem.getBoundingClientRect();\n          width.value = rect.width;\n          height.value = rect.height;\n        }\n      } else {\n        if (boxSize) {\n          const formatBoxSize = toArray(boxSize);\n          width.value = formatBoxSize.reduce((acc, { inlineSize }) => acc + inlineSize, 0);\n          height.value = formatBoxSize.reduce((acc, { blockSize }) => acc + blockSize, 0);\n        } else {\n          width.value = entry.contentRect.width;\n          height.value = entry.contentRect.height;\n        }\n      }\n    },\n    options\n  );\n  tryOnMounted(() => {\n    const ele = unrefElement(target);\n    if (ele) {\n      width.value = \"offsetWidth\" in ele ? ele.offsetWidth : initialSize.width;\n      height.value = \"offsetHeight\" in ele ? ele.offsetHeight : initialSize.height;\n    }\n  });\n  const stop2 = watch(\n    () => unrefElement(target),\n    (ele) => {\n      width.value = ele ? initialSize.width : 0;\n      height.value = ele ? initialSize.height : 0;\n    }\n  );\n  function stop() {\n    stop1();\n    stop2();\n  }\n  return {\n    width,\n    height,\n    stop\n  };\n}\n\nfunction useIntersectionObserver(target, callback, options = {}) {\n  const {\n    root,\n    rootMargin = \"0px\",\n    threshold = 0,\n    window = defaultWindow,\n    immediate = true\n  } = options;\n  const isSupported = useSupported(() => window && \"IntersectionObserver\" in window);\n  const targets = computed(() => {\n    const _target = toValue(target);\n    return toArray(_target).map(unrefElement).filter(notNullish);\n  });\n  let cleanup = noop;\n  const isActive = shallowRef(immediate);\n  const stopWatch = isSupported.value ? watch(\n    () => [targets.value, unrefElement(root), isActive.value],\n    ([targets2, root2]) => {\n      cleanup();\n      if (!isActive.value)\n        return;\n      if (!targets2.length)\n        return;\n      const observer = new IntersectionObserver(\n        callback,\n        {\n          root: unrefElement(root2),\n          rootMargin,\n          threshold\n        }\n      );\n      targets2.forEach((el) => el && observer.observe(el));\n      cleanup = () => {\n        observer.disconnect();\n        cleanup = noop;\n      };\n    },\n    { immediate, flush: \"post\" }\n  ) : noop;\n  const stop = () => {\n    cleanup();\n    stopWatch();\n    isActive.value = false;\n  };\n  tryOnScopeDispose(stop);\n  return {\n    isSupported,\n    isActive,\n    pause() {\n      cleanup();\n      isActive.value = false;\n    },\n    resume() {\n      isActive.value = true;\n    },\n    stop\n  };\n}\n\nfunction useElementVisibility(element, options = {}) {\n  const {\n    window = defaultWindow,\n    scrollTarget,\n    threshold = 0,\n    rootMargin,\n    once = false\n  } = options;\n  const elementIsVisible = shallowRef(false);\n  const { stop } = useIntersectionObserver(\n    element,\n    (intersectionObserverEntries) => {\n      let isIntersecting = elementIsVisible.value;\n      let latestTime = 0;\n      for (const entry of intersectionObserverEntries) {\n        if (entry.time >= latestTime) {\n          latestTime = entry.time;\n          isIntersecting = entry.isIntersecting;\n        }\n      }\n      elementIsVisible.value = isIntersecting;\n      if (once) {\n        watchOnce(elementIsVisible, () => {\n          stop();\n        });\n      }\n    },\n    {\n      root: scrollTarget,\n      window,\n      threshold,\n      rootMargin: toValue(rootMargin)\n    }\n  );\n  return elementIsVisible;\n}\n\nconst events = /* @__PURE__ */ new Map();\n\nfunction useEventBus(key) {\n  const scope = getCurrentScope();\n  function on(listener) {\n    var _a;\n    const listeners = events.get(key) || /* @__PURE__ */ new Set();\n    listeners.add(listener);\n    events.set(key, listeners);\n    const _off = () => off(listener);\n    (_a = scope == null ? void 0 : scope.cleanups) == null ? void 0 : _a.push(_off);\n    return _off;\n  }\n  function once(listener) {\n    function _listener(...args) {\n      off(_listener);\n      listener(...args);\n    }\n    return on(_listener);\n  }\n  function off(listener) {\n    const listeners = events.get(key);\n    if (!listeners)\n      return;\n    listeners.delete(listener);\n    if (!listeners.size)\n      reset();\n  }\n  function reset() {\n    events.delete(key);\n  }\n  function emit(event, payload) {\n    var _a;\n    (_a = events.get(key)) == null ? void 0 : _a.forEach((v) => v(event, payload));\n  }\n  return { on, once, off, emit, reset };\n}\n\nfunction resolveNestedOptions$1(options) {\n  if (options === true)\n    return {};\n  return options;\n}\nfunction useEventSource(url, events = [], options = {}) {\n  const event = shallowRef(null);\n  const data = shallowRef(null);\n  const status = shallowRef(\"CONNECTING\");\n  const eventSource = ref(null);\n  const error = shallowRef(null);\n  const urlRef = toRef(url);\n  const lastEventId = shallowRef(null);\n  let explicitlyClosed = false;\n  let retried = 0;\n  const {\n    withCredentials = false,\n    immediate = true,\n    autoConnect = true,\n    autoReconnect\n  } = options;\n  const close = () => {\n    if (isClient && eventSource.value) {\n      eventSource.value.close();\n      eventSource.value = null;\n      status.value = \"CLOSED\";\n      explicitlyClosed = true;\n    }\n  };\n  const _init = () => {\n    if (explicitlyClosed || typeof urlRef.value === \"undefined\")\n      return;\n    const es = new EventSource(urlRef.value, { withCredentials });\n    status.value = \"CONNECTING\";\n    eventSource.value = es;\n    es.onopen = () => {\n      status.value = \"OPEN\";\n      error.value = null;\n    };\n    es.onerror = (e) => {\n      status.value = \"CLOSED\";\n      error.value = e;\n      if (es.readyState === 2 && !explicitlyClosed && autoReconnect) {\n        es.close();\n        const {\n          retries = -1,\n          delay = 1e3,\n          onFailed\n        } = resolveNestedOptions$1(autoReconnect);\n        retried += 1;\n        if (typeof retries === \"number\" && (retries < 0 || retried < retries))\n          setTimeout(_init, delay);\n        else if (typeof retries === \"function\" && retries())\n          setTimeout(_init, delay);\n        else\n          onFailed == null ? void 0 : onFailed();\n      }\n    };\n    es.onmessage = (e) => {\n      event.value = null;\n      data.value = e.data;\n      lastEventId.value = e.lastEventId;\n    };\n    for (const event_name of events) {\n      useEventListener(es, event_name, (e) => {\n        event.value = event_name;\n        data.value = e.data || null;\n      }, { passive: true });\n    }\n  };\n  const open = () => {\n    if (!isClient)\n      return;\n    close();\n    explicitlyClosed = false;\n    retried = 0;\n    _init();\n  };\n  if (immediate)\n    open();\n  if (autoConnect)\n    watch(urlRef, open);\n  tryOnScopeDispose(close);\n  return {\n    eventSource,\n    event,\n    data,\n    status,\n    error,\n    open,\n    close,\n    lastEventId\n  };\n}\n\nfunction useEyeDropper(options = {}) {\n  const { initialValue = \"\" } = options;\n  const isSupported = useSupported(() => typeof window !== \"undefined\" && \"EyeDropper\" in window);\n  const sRGBHex = shallowRef(initialValue);\n  async function open(openOptions) {\n    if (!isSupported.value)\n      return;\n    const eyeDropper = new window.EyeDropper();\n    const result = await eyeDropper.open(openOptions);\n    sRGBHex.value = result.sRGBHex;\n    return result;\n  }\n  return { isSupported, sRGBHex, open };\n}\n\nfunction useFavicon(newIcon = null, options = {}) {\n  const {\n    baseUrl = \"\",\n    rel = \"icon\",\n    document = defaultDocument\n  } = options;\n  const favicon = toRef(newIcon);\n  const applyIcon = (icon) => {\n    const elements = document == null ? void 0 : document.head.querySelectorAll(`link[rel*=\"${rel}\"]`);\n    if (!elements || elements.length === 0) {\n      const link = document == null ? void 0 : document.createElement(\"link\");\n      if (link) {\n        link.rel = rel;\n        link.href = `${baseUrl}${icon}`;\n        link.type = `image/${icon.split(\".\").pop()}`;\n        document == null ? void 0 : document.head.append(link);\n      }\n      return;\n    }\n    elements == null ? void 0 : elements.forEach((el) => el.href = `${baseUrl}${icon}`);\n  };\n  watch(\n    favicon,\n    (i, o) => {\n      if (typeof i === \"string\" && i !== o)\n        applyIcon(i);\n    },\n    { immediate: true }\n  );\n  return favicon;\n}\n\nconst payloadMapping = {\n  json: \"application/json\",\n  text: \"text/plain\"\n};\nfunction isFetchOptions(obj) {\n  return obj && containsProp(obj, \"immediate\", \"refetch\", \"initialData\", \"timeout\", \"beforeFetch\", \"afterFetch\", \"onFetchError\", \"fetch\", \"updateDataOnError\");\n}\nconst reAbsolute = /^(?:[a-z][a-z\\d+\\-.]*:)?\\/\\//i;\nfunction isAbsoluteURL(url) {\n  return reAbsolute.test(url);\n}\nfunction headersToObject(headers) {\n  if (typeof Headers !== \"undefined\" && headers instanceof Headers)\n    return Object.fromEntries(headers.entries());\n  return headers;\n}\nfunction combineCallbacks(combination, ...callbacks) {\n  if (combination === \"overwrite\") {\n    return async (ctx) => {\n      let callback;\n      for (let i = callbacks.length - 1; i >= 0; i--) {\n        if (callbacks[i] != null) {\n          callback = callbacks[i];\n          break;\n        }\n      }\n      if (callback)\n        return { ...ctx, ...await callback(ctx) };\n      return ctx;\n    };\n  } else {\n    return async (ctx) => {\n      for (const callback of callbacks) {\n        if (callback)\n          ctx = { ...ctx, ...await callback(ctx) };\n      }\n      return ctx;\n    };\n  }\n}\nfunction createFetch(config = {}) {\n  const _combination = config.combination || \"chain\";\n  const _options = config.options || {};\n  const _fetchOptions = config.fetchOptions || {};\n  function useFactoryFetch(url, ...args) {\n    const computedUrl = computed(() => {\n      const baseUrl = toValue(config.baseUrl);\n      const targetUrl = toValue(url);\n      return baseUrl && !isAbsoluteURL(targetUrl) ? joinPaths(baseUrl, targetUrl) : targetUrl;\n    });\n    let options = _options;\n    let fetchOptions = _fetchOptions;\n    if (args.length > 0) {\n      if (isFetchOptions(args[0])) {\n        options = {\n          ...options,\n          ...args[0],\n          beforeFetch: combineCallbacks(_combination, _options.beforeFetch, args[0].beforeFetch),\n          afterFetch: combineCallbacks(_combination, _options.afterFetch, args[0].afterFetch),\n          onFetchError: combineCallbacks(_combination, _options.onFetchError, args[0].onFetchError)\n        };\n      } else {\n        fetchOptions = {\n          ...fetchOptions,\n          ...args[0],\n          headers: {\n            ...headersToObject(fetchOptions.headers) || {},\n            ...headersToObject(args[0].headers) || {}\n          }\n        };\n      }\n    }\n    if (args.length > 1 && isFetchOptions(args[1])) {\n      options = {\n        ...options,\n        ...args[1],\n        beforeFetch: combineCallbacks(_combination, _options.beforeFetch, args[1].beforeFetch),\n        afterFetch: combineCallbacks(_combination, _options.afterFetch, args[1].afterFetch),\n        onFetchError: combineCallbacks(_combination, _options.onFetchError, args[1].onFetchError)\n      };\n    }\n    return useFetch(computedUrl, fetchOptions, options);\n  }\n  return useFactoryFetch;\n}\nfunction useFetch(url, ...args) {\n  var _a;\n  const supportsAbort = typeof AbortController === \"function\";\n  let fetchOptions = {};\n  let options = {\n    immediate: true,\n    refetch: false,\n    timeout: 0,\n    updateDataOnError: false\n  };\n  const config = {\n    method: \"GET\",\n    type: \"text\",\n    payload: void 0\n  };\n  if (args.length > 0) {\n    if (isFetchOptions(args[0]))\n      options = { ...options, ...args[0] };\n    else\n      fetchOptions = args[0];\n  }\n  if (args.length > 1) {\n    if (isFetchOptions(args[1]))\n      options = { ...options, ...args[1] };\n  }\n  const {\n    fetch = (_a = defaultWindow) == null ? void 0 : _a.fetch,\n    initialData,\n    timeout\n  } = options;\n  const responseEvent = createEventHook();\n  const errorEvent = createEventHook();\n  const finallyEvent = createEventHook();\n  const isFinished = shallowRef(false);\n  const isFetching = shallowRef(false);\n  const aborted = shallowRef(false);\n  const statusCode = shallowRef(null);\n  const response = shallowRef(null);\n  const error = shallowRef(null);\n  const data = shallowRef(initialData || null);\n  const canAbort = computed(() => supportsAbort && isFetching.value);\n  let controller;\n  let timer;\n  const abort = () => {\n    if (supportsAbort) {\n      controller == null ? void 0 : controller.abort();\n      controller = new AbortController();\n      controller.signal.onabort = () => aborted.value = true;\n      fetchOptions = {\n        ...fetchOptions,\n        signal: controller.signal\n      };\n    }\n  };\n  const loading = (isLoading) => {\n    isFetching.value = isLoading;\n    isFinished.value = !isLoading;\n  };\n  if (timeout)\n    timer = useTimeoutFn(abort, timeout, { immediate: false });\n  let executeCounter = 0;\n  const execute = async (throwOnFailed = false) => {\n    var _a2, _b;\n    abort();\n    loading(true);\n    error.value = null;\n    statusCode.value = null;\n    aborted.value = false;\n    executeCounter += 1;\n    const currentExecuteCounter = executeCounter;\n    const defaultFetchOptions = {\n      method: config.method,\n      headers: {}\n    };\n    const payload = toValue(config.payload);\n    if (payload) {\n      const headers = headersToObject(defaultFetchOptions.headers);\n      const proto = Object.getPrototypeOf(payload);\n      if (!config.payloadType && payload && (proto === Object.prototype || Array.isArray(proto)) && !(payload instanceof FormData))\n        config.payloadType = \"json\";\n      if (config.payloadType)\n        headers[\"Content-Type\"] = (_a2 = payloadMapping[config.payloadType]) != null ? _a2 : config.payloadType;\n      defaultFetchOptions.body = config.payloadType === \"json\" ? JSON.stringify(payload) : payload;\n    }\n    let isCanceled = false;\n    const context = {\n      url: toValue(url),\n      options: {\n        ...defaultFetchOptions,\n        ...fetchOptions\n      },\n      cancel: () => {\n        isCanceled = true;\n      }\n    };\n    if (options.beforeFetch)\n      Object.assign(context, await options.beforeFetch(context));\n    if (isCanceled || !fetch) {\n      loading(false);\n      return Promise.resolve(null);\n    }\n    let responseData = null;\n    if (timer)\n      timer.start();\n    return fetch(\n      context.url,\n      {\n        ...defaultFetchOptions,\n        ...context.options,\n        headers: {\n          ...headersToObject(defaultFetchOptions.headers),\n          ...headersToObject((_b = context.options) == null ? void 0 : _b.headers)\n        }\n      }\n    ).then(async (fetchResponse) => {\n      response.value = fetchResponse;\n      statusCode.value = fetchResponse.status;\n      responseData = await fetchResponse.clone()[config.type]();\n      if (!fetchResponse.ok) {\n        data.value = initialData || null;\n        throw new Error(fetchResponse.statusText);\n      }\n      if (options.afterFetch) {\n        ({ data: responseData } = await options.afterFetch({\n          data: responseData,\n          response: fetchResponse,\n          context,\n          execute\n        }));\n      }\n      data.value = responseData;\n      responseEvent.trigger(fetchResponse);\n      return fetchResponse;\n    }).catch(async (fetchError) => {\n      let errorData = fetchError.message || fetchError.name;\n      if (options.onFetchError) {\n        ({ error: errorData, data: responseData } = await options.onFetchError({\n          data: responseData,\n          error: fetchError,\n          response: response.value,\n          context,\n          execute\n        }));\n      }\n      error.value = errorData;\n      if (options.updateDataOnError)\n        data.value = responseData;\n      errorEvent.trigger(fetchError);\n      if (throwOnFailed)\n        throw fetchError;\n      return null;\n    }).finally(() => {\n      if (currentExecuteCounter === executeCounter)\n        loading(false);\n      if (timer)\n        timer.stop();\n      finallyEvent.trigger(null);\n    });\n  };\n  const refetch = toRef(options.refetch);\n  watch(\n    [\n      refetch,\n      toRef(url)\n    ],\n    ([refetch2]) => refetch2 && execute(),\n    { deep: true }\n  );\n  const shell = {\n    isFinished: readonly(isFinished),\n    isFetching: readonly(isFetching),\n    statusCode,\n    response,\n    error,\n    data,\n    canAbort,\n    aborted,\n    abort,\n    execute,\n    onFetchResponse: responseEvent.on,\n    onFetchError: errorEvent.on,\n    onFetchFinally: finallyEvent.on,\n    // method\n    get: setMethod(\"GET\"),\n    put: setMethod(\"PUT\"),\n    post: setMethod(\"POST\"),\n    delete: setMethod(\"DELETE\"),\n    patch: setMethod(\"PATCH\"),\n    head: setMethod(\"HEAD\"),\n    options: setMethod(\"OPTIONS\"),\n    // type\n    json: setType(\"json\"),\n    text: setType(\"text\"),\n    blob: setType(\"blob\"),\n    arrayBuffer: setType(\"arrayBuffer\"),\n    formData: setType(\"formData\")\n  };\n  function setMethod(method) {\n    return (payload, payloadType) => {\n      if (!isFetching.value) {\n        config.method = method;\n        config.payload = payload;\n        config.payloadType = payloadType;\n        if (isRef(config.payload)) {\n          watch(\n            [\n              refetch,\n              toRef(config.payload)\n            ],\n            ([refetch2]) => refetch2 && execute(),\n            { deep: true }\n          );\n        }\n        return {\n          ...shell,\n          then(onFulfilled, onRejected) {\n            return waitUntilFinished().then(onFulfilled, onRejected);\n          }\n        };\n      }\n      return void 0;\n    };\n  }\n  function waitUntilFinished() {\n    return new Promise((resolve, reject) => {\n      until(isFinished).toBe(true).then(() => resolve(shell)).catch(reject);\n    });\n  }\n  function setType(type) {\n    return () => {\n      if (!isFetching.value) {\n        config.type = type;\n        return {\n          ...shell,\n          then(onFulfilled, onRejected) {\n            return waitUntilFinished().then(onFulfilled, onRejected);\n          }\n        };\n      }\n      return void 0;\n    };\n  }\n  if (options.immediate)\n    Promise.resolve().then(() => execute());\n  return {\n    ...shell,\n    then(onFulfilled, onRejected) {\n      return waitUntilFinished().then(onFulfilled, onRejected);\n    }\n  };\n}\nfunction joinPaths(start, end) {\n  if (!start.endsWith(\"/\") && !end.startsWith(\"/\")) {\n    return `${start}/${end}`;\n  }\n  if (start.endsWith(\"/\") && end.startsWith(\"/\")) {\n    return `${start.slice(0, -1)}${end}`;\n  }\n  return `${start}${end}`;\n}\n\nconst DEFAULT_OPTIONS = {\n  multiple: true,\n  accept: \"*\",\n  reset: false,\n  directory: false\n};\nfunction prepareInitialFiles(files) {\n  if (!files)\n    return null;\n  if (files instanceof FileList)\n    return files;\n  const dt = new DataTransfer();\n  for (const file of files) {\n    dt.items.add(file);\n  }\n  return dt.files;\n}\nfunction useFileDialog(options = {}) {\n  const {\n    document = defaultDocument\n  } = options;\n  const files = ref(prepareInitialFiles(options.initialFiles));\n  const { on: onChange, trigger: changeTrigger } = createEventHook();\n  const { on: onCancel, trigger: cancelTrigger } = createEventHook();\n  let input;\n  if (document) {\n    input = document.createElement(\"input\");\n    input.type = \"file\";\n    input.onchange = (event) => {\n      const result = event.target;\n      files.value = result.files;\n      changeTrigger(files.value);\n    };\n    input.oncancel = () => {\n      cancelTrigger();\n    };\n  }\n  const reset = () => {\n    files.value = null;\n    if (input && input.value) {\n      input.value = \"\";\n      changeTrigger(null);\n    }\n  };\n  const open = (localOptions) => {\n    if (!input)\n      return;\n    const _options = {\n      ...DEFAULT_OPTIONS,\n      ...options,\n      ...localOptions\n    };\n    input.multiple = _options.multiple;\n    input.accept = _options.accept;\n    input.webkitdirectory = _options.directory;\n    if (hasOwn(_options, \"capture\"))\n      input.capture = _options.capture;\n    if (_options.reset)\n      reset();\n    input.click();\n  };\n  return {\n    files: readonly(files),\n    open,\n    reset,\n    onCancel,\n    onChange\n  };\n}\n\nfunction useFileSystemAccess(options = {}) {\n  const {\n    window: _window = defaultWindow,\n    dataType = \"Text\"\n  } = options;\n  const window = _window;\n  const isSupported = useSupported(() => window && \"showSaveFilePicker\" in window && \"showOpenFilePicker\" in window);\n  const fileHandle = shallowRef();\n  const data = shallowRef();\n  const file = shallowRef();\n  const fileName = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.name) != null ? _b : \"\";\n  });\n  const fileMIME = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.type) != null ? _b : \"\";\n  });\n  const fileSize = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.size) != null ? _b : 0;\n  });\n  const fileLastModified = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.lastModified) != null ? _b : 0;\n  });\n  async function open(_options = {}) {\n    if (!isSupported.value)\n      return;\n    const [handle] = await window.showOpenFilePicker({ ...toValue(options), ..._options });\n    fileHandle.value = handle;\n    await updateData();\n  }\n  async function create(_options = {}) {\n    if (!isSupported.value)\n      return;\n    fileHandle.value = await window.showSaveFilePicker({ ...options, ..._options });\n    data.value = void 0;\n    await updateData();\n  }\n  async function save(_options = {}) {\n    if (!isSupported.value)\n      return;\n    if (!fileHandle.value)\n      return saveAs(_options);\n    if (data.value) {\n      const writableStream = await fileHandle.value.createWritable();\n      await writableStream.write(data.value);\n      await writableStream.close();\n    }\n    await updateFile();\n  }\n  async function saveAs(_options = {}) {\n    if (!isSupported.value)\n      return;\n    fileHandle.value = await window.showSaveFilePicker({ ...options, ..._options });\n    if (data.value) {\n      const writableStream = await fileHandle.value.createWritable();\n      await writableStream.write(data.value);\n      await writableStream.close();\n    }\n    await updateFile();\n  }\n  async function updateFile() {\n    var _a;\n    file.value = await ((_a = fileHandle.value) == null ? void 0 : _a.getFile());\n  }\n  async function updateData() {\n    var _a, _b;\n    await updateFile();\n    const type = toValue(dataType);\n    if (type === \"Text\")\n      data.value = await ((_a = file.value) == null ? void 0 : _a.text());\n    else if (type === \"ArrayBuffer\")\n      data.value = await ((_b = file.value) == null ? void 0 : _b.arrayBuffer());\n    else if (type === \"Blob\")\n      data.value = file.value;\n  }\n  watch(() => toValue(dataType), updateData);\n  return {\n    isSupported,\n    data,\n    file,\n    fileName,\n    fileMIME,\n    fileSize,\n    fileLastModified,\n    open,\n    create,\n    save,\n    saveAs,\n    updateData\n  };\n}\n\nfunction useFocus(target, options = {}) {\n  const { initialValue = false, focusVisible = false, preventScroll = false } = options;\n  const innerFocused = shallowRef(false);\n  const targetElement = computed(() => unrefElement(target));\n  const listenerOptions = { passive: true };\n  useEventListener(targetElement, \"focus\", (event) => {\n    var _a, _b;\n    if (!focusVisible || ((_b = (_a = event.target).matches) == null ? void 0 : _b.call(_a, \":focus-visible\")))\n      innerFocused.value = true;\n  }, listenerOptions);\n  useEventListener(targetElement, \"blur\", () => innerFocused.value = false, listenerOptions);\n  const focused = computed({\n    get: () => innerFocused.value,\n    set(value) {\n      var _a, _b;\n      if (!value && innerFocused.value)\n        (_a = targetElement.value) == null ? void 0 : _a.blur();\n      else if (value && !innerFocused.value)\n        (_b = targetElement.value) == null ? void 0 : _b.focus({ preventScroll });\n    }\n  });\n  watch(\n    targetElement,\n    () => {\n      focused.value = initialValue;\n    },\n    { immediate: true, flush: \"post\" }\n  );\n  return { focused };\n}\n\nconst EVENT_FOCUS_IN = \"focusin\";\nconst EVENT_FOCUS_OUT = \"focusout\";\nconst PSEUDO_CLASS_FOCUS_WITHIN = \":focus-within\";\nfunction useFocusWithin(target, options = {}) {\n  const { window = defaultWindow } = options;\n  const targetElement = computed(() => unrefElement(target));\n  const _focused = shallowRef(false);\n  const focused = computed(() => _focused.value);\n  const activeElement = useActiveElement(options);\n  if (!window || !activeElement.value) {\n    return { focused };\n  }\n  const listenerOptions = { passive: true };\n  useEventListener(targetElement, EVENT_FOCUS_IN, () => _focused.value = true, listenerOptions);\n  useEventListener(targetElement, EVENT_FOCUS_OUT, () => {\n    var _a, _b, _c;\n    return _focused.value = (_c = (_b = (_a = targetElement.value) == null ? void 0 : _a.matches) == null ? void 0 : _b.call(_a, PSEUDO_CLASS_FOCUS_WITHIN)) != null ? _c : false;\n  }, listenerOptions);\n  return { focused };\n}\n\nfunction useFps(options) {\n  var _a;\n  const fps = shallowRef(0);\n  if (typeof performance === \"undefined\")\n    return fps;\n  const every = (_a = options == null ? void 0 : options.every) != null ? _a : 10;\n  let last = performance.now();\n  let ticks = 0;\n  useRafFn(() => {\n    ticks += 1;\n    if (ticks >= every) {\n      const now = performance.now();\n      const diff = now - last;\n      fps.value = Math.round(1e3 / (diff / ticks));\n      last = now;\n      ticks = 0;\n    }\n  });\n  return fps;\n}\n\nconst eventHandlers = [\n  \"fullscreenchange\",\n  \"webkitfullscreenchange\",\n  \"webkitendfullscreen\",\n  \"mozfullscreenchange\",\n  \"MSFullscreenChange\"\n];\nfunction useFullscreen(target, options = {}) {\n  const {\n    document = defaultDocument,\n    autoExit = false\n  } = options;\n  const targetRef = computed(() => {\n    var _a;\n    return (_a = unrefElement(target)) != null ? _a : document == null ? void 0 : document.documentElement;\n  });\n  const isFullscreen = shallowRef(false);\n  const requestMethod = computed(() => {\n    return [\n      \"requestFullscreen\",\n      \"webkitRequestFullscreen\",\n      \"webkitEnterFullscreen\",\n      \"webkitEnterFullScreen\",\n      \"webkitRequestFullScreen\",\n      \"mozRequestFullScreen\",\n      \"msRequestFullscreen\"\n    ].find((m) => document && m in document || targetRef.value && m in targetRef.value);\n  });\n  const exitMethod = computed(() => {\n    return [\n      \"exitFullscreen\",\n      \"webkitExitFullscreen\",\n      \"webkitExitFullScreen\",\n      \"webkitCancelFullScreen\",\n      \"mozCancelFullScreen\",\n      \"msExitFullscreen\"\n    ].find((m) => document && m in document || targetRef.value && m in targetRef.value);\n  });\n  const fullscreenEnabled = computed(() => {\n    return [\n      \"fullScreen\",\n      \"webkitIsFullScreen\",\n      \"webkitDisplayingFullscreen\",\n      \"mozFullScreen\",\n      \"msFullscreenElement\"\n    ].find((m) => document && m in document || targetRef.value && m in targetRef.value);\n  });\n  const fullscreenElementMethod = [\n    \"fullscreenElement\",\n    \"webkitFullscreenElement\",\n    \"mozFullScreenElement\",\n    \"msFullscreenElement\"\n  ].find((m) => document && m in document);\n  const isSupported = useSupported(() => targetRef.value && document && requestMethod.value !== void 0 && exitMethod.value !== void 0 && fullscreenEnabled.value !== void 0);\n  const isCurrentElementFullScreen = () => {\n    if (fullscreenElementMethod)\n      return (document == null ? void 0 : document[fullscreenElementMethod]) === targetRef.value;\n    return false;\n  };\n  const isElementFullScreen = () => {\n    if (fullscreenEnabled.value) {\n      if (document && document[fullscreenEnabled.value] != null) {\n        return document[fullscreenEnabled.value];\n      } else {\n        const target2 = targetRef.value;\n        if ((target2 == null ? void 0 : target2[fullscreenEnabled.value]) != null) {\n          return Boolean(target2[fullscreenEnabled.value]);\n        }\n      }\n    }\n    return false;\n  };\n  async function exit() {\n    if (!isSupported.value || !isFullscreen.value)\n      return;\n    if (exitMethod.value) {\n      if ((document == null ? void 0 : document[exitMethod.value]) != null) {\n        await document[exitMethod.value]();\n      } else {\n        const target2 = targetRef.value;\n        if ((target2 == null ? void 0 : target2[exitMethod.value]) != null)\n          await target2[exitMethod.value]();\n      }\n    }\n    isFullscreen.value = false;\n  }\n  async function enter() {\n    if (!isSupported.value || isFullscreen.value)\n      return;\n    if (isElementFullScreen())\n      await exit();\n    const target2 = targetRef.value;\n    if (requestMethod.value && (target2 == null ? void 0 : target2[requestMethod.value]) != null) {\n      await target2[requestMethod.value]();\n      isFullscreen.value = true;\n    }\n  }\n  async function toggle() {\n    await (isFullscreen.value ? exit() : enter());\n  }\n  const handlerCallback = () => {\n    const isElementFullScreenValue = isElementFullScreen();\n    if (!isElementFullScreenValue || isElementFullScreenValue && isCurrentElementFullScreen())\n      isFullscreen.value = isElementFullScreenValue;\n  };\n  const listenerOptions = { capture: false, passive: true };\n  useEventListener(document, eventHandlers, handlerCallback, listenerOptions);\n  useEventListener(() => unrefElement(targetRef), eventHandlers, handlerCallback, listenerOptions);\n  if (autoExit)\n    tryOnScopeDispose(exit);\n  return {\n    isSupported,\n    isFullscreen,\n    enter,\n    exit,\n    toggle\n  };\n}\n\nfunction mapGamepadToXbox360Controller(gamepad) {\n  return computed(() => {\n    if (gamepad.value) {\n      return {\n        buttons: {\n          a: gamepad.value.buttons[0],\n          b: gamepad.value.buttons[1],\n          x: gamepad.value.buttons[2],\n          y: gamepad.value.buttons[3]\n        },\n        bumper: {\n          left: gamepad.value.buttons[4],\n          right: gamepad.value.buttons[5]\n        },\n        triggers: {\n          left: gamepad.value.buttons[6],\n          right: gamepad.value.buttons[7]\n        },\n        stick: {\n          left: {\n            horizontal: gamepad.value.axes[0],\n            vertical: gamepad.value.axes[1],\n            button: gamepad.value.buttons[10]\n          },\n          right: {\n            horizontal: gamepad.value.axes[2],\n            vertical: gamepad.value.axes[3],\n            button: gamepad.value.buttons[11]\n          }\n        },\n        dpad: {\n          up: gamepad.value.buttons[12],\n          down: gamepad.value.buttons[13],\n          left: gamepad.value.buttons[14],\n          right: gamepad.value.buttons[15]\n        },\n        back: gamepad.value.buttons[8],\n        start: gamepad.value.buttons[9]\n      };\n    }\n    return null;\n  });\n}\nfunction useGamepad(options = {}) {\n  const {\n    navigator = defaultNavigator\n  } = options;\n  const isSupported = useSupported(() => navigator && \"getGamepads\" in navigator);\n  const gamepads = ref([]);\n  const onConnectedHook = createEventHook();\n  const onDisconnectedHook = createEventHook();\n  const stateFromGamepad = (gamepad) => {\n    const hapticActuators = [];\n    const vibrationActuator = \"vibrationActuator\" in gamepad ? gamepad.vibrationActuator : null;\n    if (vibrationActuator)\n      hapticActuators.push(vibrationActuator);\n    if (gamepad.hapticActuators)\n      hapticActuators.push(...gamepad.hapticActuators);\n    return {\n      id: gamepad.id,\n      index: gamepad.index,\n      connected: gamepad.connected,\n      mapping: gamepad.mapping,\n      timestamp: gamepad.timestamp,\n      vibrationActuator: gamepad.vibrationActuator,\n      hapticActuators,\n      axes: gamepad.axes.map((axes) => axes),\n      buttons: gamepad.buttons.map((button) => ({ pressed: button.pressed, touched: button.touched, value: button.value }))\n    };\n  };\n  const updateGamepadState = () => {\n    const _gamepads = (navigator == null ? void 0 : navigator.getGamepads()) || [];\n    for (const gamepad of _gamepads) {\n      if (gamepad && gamepads.value[gamepad.index])\n        gamepads.value[gamepad.index] = stateFromGamepad(gamepad);\n    }\n  };\n  const { isActive, pause, resume } = useRafFn(updateGamepadState);\n  const onGamepadConnected = (gamepad) => {\n    if (!gamepads.value.some(({ index }) => index === gamepad.index)) {\n      gamepads.value.push(stateFromGamepad(gamepad));\n      onConnectedHook.trigger(gamepad.index);\n    }\n    resume();\n  };\n  const onGamepadDisconnected = (gamepad) => {\n    gamepads.value = gamepads.value.filter((x) => x.index !== gamepad.index);\n    onDisconnectedHook.trigger(gamepad.index);\n  };\n  const listenerOptions = { passive: true };\n  useEventListener(\"gamepadconnected\", (e) => onGamepadConnected(e.gamepad), listenerOptions);\n  useEventListener(\"gamepaddisconnected\", (e) => onGamepadDisconnected(e.gamepad), listenerOptions);\n  tryOnMounted(() => {\n    const _gamepads = (navigator == null ? void 0 : navigator.getGamepads()) || [];\n    for (const gamepad of _gamepads) {\n      if (gamepad && gamepads.value[gamepad.index])\n        onGamepadConnected(gamepad);\n    }\n  });\n  pause();\n  return {\n    isSupported,\n    onConnected: onConnectedHook.on,\n    onDisconnected: onDisconnectedHook.on,\n    gamepads,\n    pause,\n    resume,\n    isActive\n  };\n}\n\nfunction useGeolocation(options = {}) {\n  const {\n    enableHighAccuracy = true,\n    maximumAge = 3e4,\n    timeout = 27e3,\n    navigator = defaultNavigator,\n    immediate = true\n  } = options;\n  const isSupported = useSupported(() => navigator && \"geolocation\" in navigator);\n  const locatedAt = shallowRef(null);\n  const error = shallowRef(null);\n  const coords = ref({\n    accuracy: 0,\n    latitude: Number.POSITIVE_INFINITY,\n    longitude: Number.POSITIVE_INFINITY,\n    altitude: null,\n    altitudeAccuracy: null,\n    heading: null,\n    speed: null\n  });\n  function updatePosition(position) {\n    locatedAt.value = position.timestamp;\n    coords.value = position.coords;\n    error.value = null;\n  }\n  let watcher;\n  function resume() {\n    if (isSupported.value) {\n      watcher = navigator.geolocation.watchPosition(\n        updatePosition,\n        (err) => error.value = err,\n        {\n          enableHighAccuracy,\n          maximumAge,\n          timeout\n        }\n      );\n    }\n  }\n  if (immediate)\n    resume();\n  function pause() {\n    if (watcher && navigator)\n      navigator.geolocation.clearWatch(watcher);\n  }\n  tryOnScopeDispose(() => {\n    pause();\n  });\n  return {\n    isSupported,\n    coords,\n    locatedAt,\n    error,\n    resume,\n    pause\n  };\n}\n\nconst defaultEvents$1 = [\"mousemove\", \"mousedown\", \"resize\", \"keydown\", \"touchstart\", \"wheel\"];\nconst oneMinute = 6e4;\nfunction useIdle(timeout = oneMinute, options = {}) {\n  const {\n    initialState = false,\n    listenForVisibilityChange = true,\n    events = defaultEvents$1,\n    window = defaultWindow,\n    eventFilter = throttleFilter(50)\n  } = options;\n  const idle = shallowRef(initialState);\n  const lastActive = shallowRef(timestamp());\n  let timer;\n  const reset = () => {\n    idle.value = false;\n    clearTimeout(timer);\n    timer = setTimeout(() => idle.value = true, timeout);\n  };\n  const onEvent = createFilterWrapper(\n    eventFilter,\n    () => {\n      lastActive.value = timestamp();\n      reset();\n    }\n  );\n  if (window) {\n    const document = window.document;\n    const listenerOptions = { passive: true };\n    for (const event of events)\n      useEventListener(window, event, onEvent, listenerOptions);\n    if (listenForVisibilityChange) {\n      useEventListener(document, \"visibilitychange\", () => {\n        if (!document.hidden)\n          onEvent();\n      }, listenerOptions);\n    }\n    reset();\n  }\n  return {\n    idle,\n    lastActive,\n    reset\n  };\n}\n\nasync function loadImage(options) {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    const { src, srcset, sizes, class: clazz, loading, crossorigin, referrerPolicy, width, height, decoding, fetchPriority, ismap, usemap } = options;\n    img.src = src;\n    if (srcset != null)\n      img.srcset = srcset;\n    if (sizes != null)\n      img.sizes = sizes;\n    if (clazz != null)\n      img.className = clazz;\n    if (loading != null)\n      img.loading = loading;\n    if (crossorigin != null)\n      img.crossOrigin = crossorigin;\n    if (referrerPolicy != null)\n      img.referrerPolicy = referrerPolicy;\n    if (width != null)\n      img.width = width;\n    if (height != null)\n      img.height = height;\n    if (decoding != null)\n      img.decoding = decoding;\n    if (fetchPriority != null)\n      img.fetchPriority = fetchPriority;\n    if (ismap != null)\n      img.isMap = ismap;\n    if (usemap != null)\n      img.useMap = usemap;\n    img.onload = () => resolve(img);\n    img.onerror = reject;\n  });\n}\nfunction useImage(options, asyncStateOptions = {}) {\n  const state = useAsyncState(\n    () => loadImage(toValue(options)),\n    void 0,\n    {\n      resetOnExecute: true,\n      ...asyncStateOptions\n    }\n  );\n  watch(\n    () => toValue(options),\n    () => state.execute(asyncStateOptions.delay),\n    { deep: true }\n  );\n  return state;\n}\n\nfunction resolveElement(el) {\n  if (typeof Window !== \"undefined\" && el instanceof Window)\n    return el.document.documentElement;\n  if (typeof Document !== \"undefined\" && el instanceof Document)\n    return el.documentElement;\n  return el;\n}\n\nconst ARRIVED_STATE_THRESHOLD_PIXELS = 1;\nfunction useScroll(element, options = {}) {\n  const {\n    throttle = 0,\n    idle = 200,\n    onStop = noop,\n    onScroll = noop,\n    offset = {\n      left: 0,\n      right: 0,\n      top: 0,\n      bottom: 0\n    },\n    eventListenerOptions = {\n      capture: false,\n      passive: true\n    },\n    behavior = \"auto\",\n    window = defaultWindow,\n    onError = (e) => {\n      console.error(e);\n    }\n  } = options;\n  const internalX = shallowRef(0);\n  const internalY = shallowRef(0);\n  const x = computed({\n    get() {\n      return internalX.value;\n    },\n    set(x2) {\n      scrollTo(x2, void 0);\n    }\n  });\n  const y = computed({\n    get() {\n      return internalY.value;\n    },\n    set(y2) {\n      scrollTo(void 0, y2);\n    }\n  });\n  function scrollTo(_x, _y) {\n    var _a, _b, _c, _d;\n    if (!window)\n      return;\n    const _element = toValue(element);\n    if (!_element)\n      return;\n    (_c = _element instanceof Document ? window.document.body : _element) == null ? void 0 : _c.scrollTo({\n      top: (_a = toValue(_y)) != null ? _a : y.value,\n      left: (_b = toValue(_x)) != null ? _b : x.value,\n      behavior: toValue(behavior)\n    });\n    const scrollContainer = ((_d = _element == null ? void 0 : _element.document) == null ? void 0 : _d.documentElement) || (_element == null ? void 0 : _element.documentElement) || _element;\n    if (x != null)\n      internalX.value = scrollContainer.scrollLeft;\n    if (y != null)\n      internalY.value = scrollContainer.scrollTop;\n  }\n  const isScrolling = shallowRef(false);\n  const arrivedState = reactive({\n    left: true,\n    right: false,\n    top: true,\n    bottom: false\n  });\n  const directions = reactive({\n    left: false,\n    right: false,\n    top: false,\n    bottom: false\n  });\n  const onScrollEnd = (e) => {\n    if (!isScrolling.value)\n      return;\n    isScrolling.value = false;\n    directions.left = false;\n    directions.right = false;\n    directions.top = false;\n    directions.bottom = false;\n    onStop(e);\n  };\n  const onScrollEndDebounced = useDebounceFn(onScrollEnd, throttle + idle);\n  const setArrivedState = (target) => {\n    var _a;\n    if (!window)\n      return;\n    const el = ((_a = target == null ? void 0 : target.document) == null ? void 0 : _a.documentElement) || (target == null ? void 0 : target.documentElement) || unrefElement(target);\n    const { display, flexDirection, direction } = getComputedStyle(el);\n    const directionMultipler = direction === \"rtl\" ? -1 : 1;\n    const scrollLeft = el.scrollLeft;\n    directions.left = scrollLeft < internalX.value;\n    directions.right = scrollLeft > internalX.value;\n    const left = Math.abs(scrollLeft * directionMultipler) <= (offset.left || 0);\n    const right = Math.abs(scrollLeft * directionMultipler) + el.clientWidth >= el.scrollWidth - (offset.right || 0) - ARRIVED_STATE_THRESHOLD_PIXELS;\n    if (display === \"flex\" && flexDirection === \"row-reverse\") {\n      arrivedState.left = right;\n      arrivedState.right = left;\n    } else {\n      arrivedState.left = left;\n      arrivedState.right = right;\n    }\n    internalX.value = scrollLeft;\n    let scrollTop = el.scrollTop;\n    if (target === window.document && !scrollTop)\n      scrollTop = window.document.body.scrollTop;\n    directions.top = scrollTop < internalY.value;\n    directions.bottom = scrollTop > internalY.value;\n    const top = Math.abs(scrollTop) <= (offset.top || 0);\n    const bottom = Math.abs(scrollTop) + el.clientHeight >= el.scrollHeight - (offset.bottom || 0) - ARRIVED_STATE_THRESHOLD_PIXELS;\n    if (display === \"flex\" && flexDirection === \"column-reverse\") {\n      arrivedState.top = bottom;\n      arrivedState.bottom = top;\n    } else {\n      arrivedState.top = top;\n      arrivedState.bottom = bottom;\n    }\n    internalY.value = scrollTop;\n  };\n  const onScrollHandler = (e) => {\n    var _a;\n    if (!window)\n      return;\n    const eventTarget = (_a = e.target.documentElement) != null ? _a : e.target;\n    setArrivedState(eventTarget);\n    isScrolling.value = true;\n    onScrollEndDebounced(e);\n    onScroll(e);\n  };\n  useEventListener(\n    element,\n    \"scroll\",\n    throttle ? useThrottleFn(onScrollHandler, throttle, true, false) : onScrollHandler,\n    eventListenerOptions\n  );\n  tryOnMounted(() => {\n    try {\n      const _element = toValue(element);\n      if (!_element)\n        return;\n      setArrivedState(_element);\n    } catch (e) {\n      onError(e);\n    }\n  });\n  useEventListener(\n    element,\n    \"scrollend\",\n    onScrollEnd,\n    eventListenerOptions\n  );\n  return {\n    x,\n    y,\n    isScrolling,\n    arrivedState,\n    directions,\n    measure() {\n      const _element = toValue(element);\n      if (window && _element)\n        setArrivedState(_element);\n    }\n  };\n}\n\nfunction useInfiniteScroll(element, onLoadMore, options = {}) {\n  var _a;\n  const {\n    direction = \"bottom\",\n    interval = 100,\n    canLoadMore = () => true\n  } = options;\n  const state = reactive(useScroll(\n    element,\n    {\n      ...options,\n      offset: {\n        [direction]: (_a = options.distance) != null ? _a : 0,\n        ...options.offset\n      }\n    }\n  ));\n  const promise = ref();\n  const isLoading = computed(() => !!promise.value);\n  const observedElement = computed(() => {\n    return resolveElement(toValue(element));\n  });\n  const isElementVisible = useElementVisibility(observedElement);\n  function checkAndLoad() {\n    state.measure();\n    if (!observedElement.value || !isElementVisible.value || !canLoadMore(observedElement.value))\n      return;\n    const { scrollHeight, clientHeight, scrollWidth, clientWidth } = observedElement.value;\n    const isNarrower = direction === \"bottom\" || direction === \"top\" ? scrollHeight <= clientHeight : scrollWidth <= clientWidth;\n    if (state.arrivedState[direction] || isNarrower) {\n      if (!promise.value) {\n        promise.value = Promise.all([\n          onLoadMore(state),\n          new Promise((resolve) => setTimeout(resolve, interval))\n        ]).finally(() => {\n          promise.value = null;\n          nextTick(() => checkAndLoad());\n        });\n      }\n    }\n  }\n  const stop = watch(\n    () => [state.arrivedState[direction], isElementVisible.value],\n    checkAndLoad,\n    { immediate: true }\n  );\n  tryOnUnmounted(stop);\n  return {\n    isLoading,\n    reset() {\n      nextTick(() => checkAndLoad());\n    }\n  };\n}\n\nconst defaultEvents = [\"mousedown\", \"mouseup\", \"keydown\", \"keyup\"];\nfunction useKeyModifier(modifier, options = {}) {\n  const {\n    events = defaultEvents,\n    document = defaultDocument,\n    initial = null\n  } = options;\n  const state = shallowRef(initial);\n  if (document) {\n    events.forEach((listenerEvent) => {\n      useEventListener(document, listenerEvent, (evt) => {\n        if (typeof evt.getModifierState === \"function\")\n          state.value = evt.getModifierState(modifier);\n      }, { passive: true });\n    });\n  }\n  return state;\n}\n\nfunction useLocalStorage(key, initialValue, options = {}) {\n  const { window = defaultWindow } = options;\n  return useStorage(key, initialValue, window == null ? void 0 : window.localStorage, options);\n}\n\nconst DefaultMagicKeysAliasMap = {\n  ctrl: \"control\",\n  command: \"meta\",\n  cmd: \"meta\",\n  option: \"alt\",\n  up: \"arrowup\",\n  down: \"arrowdown\",\n  left: \"arrowleft\",\n  right: \"arrowright\"\n};\n\nfunction useMagicKeys(options = {}) {\n  const {\n    reactive: useReactive = false,\n    target = defaultWindow,\n    aliasMap = DefaultMagicKeysAliasMap,\n    passive = true,\n    onEventFired = noop\n  } = options;\n  const current = reactive(/* @__PURE__ */ new Set());\n  const obj = {\n    toJSON() {\n      return {};\n    },\n    current\n  };\n  const refs = useReactive ? reactive(obj) : obj;\n  const metaDeps = /* @__PURE__ */ new Set();\n  const usedKeys = /* @__PURE__ */ new Set();\n  function setRefs(key, value) {\n    if (key in refs) {\n      if (useReactive)\n        refs[key] = value;\n      else\n        refs[key].value = value;\n    }\n  }\n  function reset() {\n    current.clear();\n    for (const key of usedKeys)\n      setRefs(key, false);\n  }\n  function updateRefs(e, value) {\n    var _a, _b;\n    const key = (_a = e.key) == null ? void 0 : _a.toLowerCase();\n    const code = (_b = e.code) == null ? void 0 : _b.toLowerCase();\n    const values = [code, key].filter(Boolean);\n    if (key) {\n      if (value)\n        current.add(key);\n      else\n        current.delete(key);\n    }\n    for (const key2 of values) {\n      usedKeys.add(key2);\n      setRefs(key2, value);\n    }\n    if (key === \"meta\" && !value) {\n      metaDeps.forEach((key2) => {\n        current.delete(key2);\n        setRefs(key2, false);\n      });\n      metaDeps.clear();\n    } else if (typeof e.getModifierState === \"function\" && e.getModifierState(\"Meta\") && value) {\n      [...current, ...values].forEach((key2) => metaDeps.add(key2));\n    }\n  }\n  useEventListener(target, \"keydown\", (e) => {\n    updateRefs(e, true);\n    return onEventFired(e);\n  }, { passive });\n  useEventListener(target, \"keyup\", (e) => {\n    updateRefs(e, false);\n    return onEventFired(e);\n  }, { passive });\n  useEventListener(\"blur\", reset, { passive });\n  useEventListener(\"focus\", reset, { passive });\n  const proxy = new Proxy(\n    refs,\n    {\n      get(target2, prop, rec) {\n        if (typeof prop !== \"string\")\n          return Reflect.get(target2, prop, rec);\n        prop = prop.toLowerCase();\n        if (prop in aliasMap)\n          prop = aliasMap[prop];\n        if (!(prop in refs)) {\n          if (/[+_-]/.test(prop)) {\n            const keys = prop.split(/[+_-]/g).map((i) => i.trim());\n            refs[prop] = computed(() => keys.map((key) => toValue(proxy[key])).every(Boolean));\n          } else {\n            refs[prop] = shallowRef(false);\n          }\n        }\n        const r = Reflect.get(target2, prop, rec);\n        return useReactive ? toValue(r) : r;\n      }\n    }\n  );\n  return proxy;\n}\n\nfunction usingElRef(source, cb) {\n  if (toValue(source))\n    cb(toValue(source));\n}\nfunction timeRangeToArray(timeRanges) {\n  let ranges = [];\n  for (let i = 0; i < timeRanges.length; ++i)\n    ranges = [...ranges, [timeRanges.start(i), timeRanges.end(i)]];\n  return ranges;\n}\nfunction tracksToArray(tracks) {\n  return Array.from(tracks).map(({ label, kind, language, mode, activeCues, cues, inBandMetadataTrackDispatchType }, id) => ({ id, label, kind, language, mode, activeCues, cues, inBandMetadataTrackDispatchType }));\n}\nconst defaultOptions = {\n  src: \"\",\n  tracks: []\n};\nfunction useMediaControls(target, options = {}) {\n  target = toRef(target);\n  options = {\n    ...defaultOptions,\n    ...options\n  };\n  const {\n    document = defaultDocument\n  } = options;\n  const listenerOptions = { passive: true };\n  const currentTime = shallowRef(0);\n  const duration = shallowRef(0);\n  const seeking = shallowRef(false);\n  const volume = shallowRef(1);\n  const waiting = shallowRef(false);\n  const ended = shallowRef(false);\n  const playing = shallowRef(false);\n  const rate = shallowRef(1);\n  const stalled = shallowRef(false);\n  const buffered = ref([]);\n  const tracks = ref([]);\n  const selectedTrack = shallowRef(-1);\n  const isPictureInPicture = shallowRef(false);\n  const muted = shallowRef(false);\n  const supportsPictureInPicture = document && \"pictureInPictureEnabled\" in document;\n  const sourceErrorEvent = createEventHook();\n  const playbackErrorEvent = createEventHook();\n  const disableTrack = (track) => {\n    usingElRef(target, (el) => {\n      if (track) {\n        const id = typeof track === \"number\" ? track : track.id;\n        el.textTracks[id].mode = \"disabled\";\n      } else {\n        for (let i = 0; i < el.textTracks.length; ++i)\n          el.textTracks[i].mode = \"disabled\";\n      }\n      selectedTrack.value = -1;\n    });\n  };\n  const enableTrack = (track, disableTracks = true) => {\n    usingElRef(target, (el) => {\n      const id = typeof track === \"number\" ? track : track.id;\n      if (disableTracks)\n        disableTrack();\n      el.textTracks[id].mode = \"showing\";\n      selectedTrack.value = id;\n    });\n  };\n  const togglePictureInPicture = () => {\n    return new Promise((resolve, reject) => {\n      usingElRef(target, async (el) => {\n        if (supportsPictureInPicture) {\n          if (!isPictureInPicture.value) {\n            el.requestPictureInPicture().then(resolve).catch(reject);\n          } else {\n            document.exitPictureInPicture().then(resolve).catch(reject);\n          }\n        }\n      });\n    });\n  };\n  watchEffect(() => {\n    if (!document)\n      return;\n    const el = toValue(target);\n    if (!el)\n      return;\n    const src = toValue(options.src);\n    let sources = [];\n    if (!src)\n      return;\n    if (typeof src === \"string\")\n      sources = [{ src }];\n    else if (Array.isArray(src))\n      sources = src;\n    else if (isObject(src))\n      sources = [src];\n    el.querySelectorAll(\"source\").forEach((e) => {\n      e.remove();\n    });\n    sources.forEach(({ src: src2, type, media }) => {\n      const source = document.createElement(\"source\");\n      source.setAttribute(\"src\", src2);\n      source.setAttribute(\"type\", type || \"\");\n      source.setAttribute(\"media\", media || \"\");\n      useEventListener(source, \"error\", sourceErrorEvent.trigger, listenerOptions);\n      el.appendChild(source);\n    });\n    el.load();\n  });\n  watch([target, volume], () => {\n    const el = toValue(target);\n    if (!el)\n      return;\n    el.volume = volume.value;\n  });\n  watch([target, muted], () => {\n    const el = toValue(target);\n    if (!el)\n      return;\n    el.muted = muted.value;\n  });\n  watch([target, rate], () => {\n    const el = toValue(target);\n    if (!el)\n      return;\n    el.playbackRate = rate.value;\n  });\n  watchEffect(() => {\n    if (!document)\n      return;\n    const textTracks = toValue(options.tracks);\n    const el = toValue(target);\n    if (!textTracks || !textTracks.length || !el)\n      return;\n    el.querySelectorAll(\"track\").forEach((e) => e.remove());\n    textTracks.forEach(({ default: isDefault, kind, label, src, srcLang }, i) => {\n      const track = document.createElement(\"track\");\n      track.default = isDefault || false;\n      track.kind = kind;\n      track.label = label;\n      track.src = src;\n      track.srclang = srcLang;\n      if (track.default)\n        selectedTrack.value = i;\n      el.appendChild(track);\n    });\n  });\n  const { ignoreUpdates: ignoreCurrentTimeUpdates } = watchIgnorable(currentTime, (time) => {\n    const el = toValue(target);\n    if (!el)\n      return;\n    el.currentTime = time;\n  });\n  const { ignoreUpdates: ignorePlayingUpdates } = watchIgnorable(playing, (isPlaying) => {\n    const el = toValue(target);\n    if (!el)\n      return;\n    if (isPlaying) {\n      el.play().catch((e) => {\n        playbackErrorEvent.trigger(e);\n        throw e;\n      });\n    } else {\n      el.pause();\n    }\n  });\n  useEventListener(\n    target,\n    \"timeupdate\",\n    () => ignoreCurrentTimeUpdates(() => currentTime.value = toValue(target).currentTime),\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"durationchange\",\n    () => duration.value = toValue(target).duration,\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"progress\",\n    () => buffered.value = timeRangeToArray(toValue(target).buffered),\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"seeking\",\n    () => seeking.value = true,\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"seeked\",\n    () => seeking.value = false,\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    [\"waiting\", \"loadstart\"],\n    () => {\n      waiting.value = true;\n      ignorePlayingUpdates(() => playing.value = false);\n    },\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"loadeddata\",\n    () => waiting.value = false,\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"playing\",\n    () => {\n      waiting.value = false;\n      ended.value = false;\n      ignorePlayingUpdates(() => playing.value = true);\n    },\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"ratechange\",\n    () => rate.value = toValue(target).playbackRate,\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"stalled\",\n    () => stalled.value = true,\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"ended\",\n    () => ended.value = true,\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"pause\",\n    () => ignorePlayingUpdates(() => playing.value = false),\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"play\",\n    () => ignorePlayingUpdates(() => playing.value = true),\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"enterpictureinpicture\",\n    () => isPictureInPicture.value = true,\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"leavepictureinpicture\",\n    () => isPictureInPicture.value = false,\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"volumechange\",\n    () => {\n      const el = toValue(target);\n      if (!el)\n        return;\n      volume.value = el.volume;\n      muted.value = el.muted;\n    },\n    listenerOptions\n  );\n  const listeners = [];\n  const stop = watch([target], () => {\n    const el = toValue(target);\n    if (!el)\n      return;\n    stop();\n    listeners[0] = useEventListener(el.textTracks, \"addtrack\", () => tracks.value = tracksToArray(el.textTracks), listenerOptions);\n    listeners[1] = useEventListener(el.textTracks, \"removetrack\", () => tracks.value = tracksToArray(el.textTracks), listenerOptions);\n    listeners[2] = useEventListener(el.textTracks, \"change\", () => tracks.value = tracksToArray(el.textTracks), listenerOptions);\n  });\n  tryOnScopeDispose(() => listeners.forEach((listener) => listener()));\n  return {\n    currentTime,\n    duration,\n    waiting,\n    seeking,\n    ended,\n    stalled,\n    buffered,\n    playing,\n    rate,\n    // Volume\n    volume,\n    muted,\n    // Tracks\n    tracks,\n    selectedTrack,\n    enableTrack,\n    disableTrack,\n    // Picture in Picture\n    supportsPictureInPicture,\n    togglePictureInPicture,\n    isPictureInPicture,\n    // Events\n    onSourceError: sourceErrorEvent.on,\n    onPlaybackError: playbackErrorEvent.on\n  };\n}\n\nfunction useMemoize(resolver, options) {\n  const initCache = () => {\n    if (options == null ? void 0 : options.cache)\n      return shallowReactive(options.cache);\n    return shallowReactive(/* @__PURE__ */ new Map());\n  };\n  const cache = initCache();\n  const generateKey = (...args) => (options == null ? void 0 : options.getKey) ? options.getKey(...args) : JSON.stringify(args);\n  const _loadData = (key, ...args) => {\n    cache.set(key, resolver(...args));\n    return cache.get(key);\n  };\n  const loadData = (...args) => _loadData(generateKey(...args), ...args);\n  const deleteData = (...args) => {\n    cache.delete(generateKey(...args));\n  };\n  const clearData = () => {\n    cache.clear();\n  };\n  const memoized = (...args) => {\n    const key = generateKey(...args);\n    if (cache.has(key))\n      return cache.get(key);\n    return _loadData(key, ...args);\n  };\n  memoized.load = loadData;\n  memoized.delete = deleteData;\n  memoized.clear = clearData;\n  memoized.generateKey = generateKey;\n  memoized.cache = cache;\n  return memoized;\n}\n\nfunction useMemory(options = {}) {\n  const memory = ref();\n  const isSupported = useSupported(() => typeof performance !== \"undefined\" && \"memory\" in performance);\n  if (isSupported.value) {\n    const { interval = 1e3 } = options;\n    useIntervalFn(() => {\n      memory.value = performance.memory;\n    }, interval, { immediate: options.immediate, immediateCallback: options.immediateCallback });\n  }\n  return { isSupported, memory };\n}\n\nconst UseMouseBuiltinExtractors = {\n  page: (event) => [event.pageX, event.pageY],\n  client: (event) => [event.clientX, event.clientY],\n  screen: (event) => [event.screenX, event.screenY],\n  movement: (event) => event instanceof MouseEvent ? [event.movementX, event.movementY] : null\n};\nfunction useMouse(options = {}) {\n  const {\n    type = \"page\",\n    touch = true,\n    resetOnTouchEnds = false,\n    initialValue = { x: 0, y: 0 },\n    window = defaultWindow,\n    target = window,\n    scroll = true,\n    eventFilter\n  } = options;\n  let _prevMouseEvent = null;\n  let _prevScrollX = 0;\n  let _prevScrollY = 0;\n  const x = shallowRef(initialValue.x);\n  const y = shallowRef(initialValue.y);\n  const sourceType = shallowRef(null);\n  const extractor = typeof type === \"function\" ? type : UseMouseBuiltinExtractors[type];\n  const mouseHandler = (event) => {\n    const result = extractor(event);\n    _prevMouseEvent = event;\n    if (result) {\n      [x.value, y.value] = result;\n      sourceType.value = \"mouse\";\n    }\n    if (window) {\n      _prevScrollX = window.scrollX;\n      _prevScrollY = window.scrollY;\n    }\n  };\n  const touchHandler = (event) => {\n    if (event.touches.length > 0) {\n      const result = extractor(event.touches[0]);\n      if (result) {\n        [x.value, y.value] = result;\n        sourceType.value = \"touch\";\n      }\n    }\n  };\n  const scrollHandler = () => {\n    if (!_prevMouseEvent || !window)\n      return;\n    const pos = extractor(_prevMouseEvent);\n    if (_prevMouseEvent instanceof MouseEvent && pos) {\n      x.value = pos[0] + window.scrollX - _prevScrollX;\n      y.value = pos[1] + window.scrollY - _prevScrollY;\n    }\n  };\n  const reset = () => {\n    x.value = initialValue.x;\n    y.value = initialValue.y;\n  };\n  const mouseHandlerWrapper = eventFilter ? (event) => eventFilter(() => mouseHandler(event), {}) : (event) => mouseHandler(event);\n  const touchHandlerWrapper = eventFilter ? (event) => eventFilter(() => touchHandler(event), {}) : (event) => touchHandler(event);\n  const scrollHandlerWrapper = eventFilter ? () => eventFilter(() => scrollHandler(), {}) : () => scrollHandler();\n  if (target) {\n    const listenerOptions = { passive: true };\n    useEventListener(target, [\"mousemove\", \"dragover\"], mouseHandlerWrapper, listenerOptions);\n    if (touch && type !== \"movement\") {\n      useEventListener(target, [\"touchstart\", \"touchmove\"], touchHandlerWrapper, listenerOptions);\n      if (resetOnTouchEnds)\n        useEventListener(target, \"touchend\", reset, listenerOptions);\n    }\n    if (scroll && type === \"page\")\n      useEventListener(window, \"scroll\", scrollHandlerWrapper, listenerOptions);\n  }\n  return {\n    x,\n    y,\n    sourceType\n  };\n}\n\nfunction useMouseInElement(target, options = {}) {\n  const {\n    handleOutside = true,\n    window = defaultWindow\n  } = options;\n  const type = options.type || \"page\";\n  const { x, y, sourceType } = useMouse(options);\n  const targetRef = shallowRef(target != null ? target : window == null ? void 0 : window.document.body);\n  const elementX = shallowRef(0);\n  const elementY = shallowRef(0);\n  const elementPositionX = shallowRef(0);\n  const elementPositionY = shallowRef(0);\n  const elementHeight = shallowRef(0);\n  const elementWidth = shallowRef(0);\n  const isOutside = shallowRef(true);\n  let stop = () => {\n  };\n  if (window) {\n    stop = watch(\n      [targetRef, x, y],\n      () => {\n        const el = unrefElement(targetRef);\n        if (!el || !(el instanceof Element))\n          return;\n        const {\n          left,\n          top,\n          width,\n          height\n        } = el.getBoundingClientRect();\n        elementPositionX.value = left + (type === \"page\" ? window.pageXOffset : 0);\n        elementPositionY.value = top + (type === \"page\" ? window.pageYOffset : 0);\n        elementHeight.value = height;\n        elementWidth.value = width;\n        const elX = x.value - elementPositionX.value;\n        const elY = y.value - elementPositionY.value;\n        isOutside.value = width === 0 || height === 0 || elX < 0 || elY < 0 || elX > width || elY > height;\n        if (handleOutside || !isOutside.value) {\n          elementX.value = elX;\n          elementY.value = elY;\n        }\n      },\n      { immediate: true }\n    );\n    useEventListener(\n      document,\n      \"mouseleave\",\n      () => isOutside.value = true,\n      { passive: true }\n    );\n  }\n  return {\n    x,\n    y,\n    sourceType,\n    elementX,\n    elementY,\n    elementPositionX,\n    elementPositionY,\n    elementHeight,\n    elementWidth,\n    isOutside,\n    stop\n  };\n}\n\nfunction useMousePressed(options = {}) {\n  const {\n    touch = true,\n    drag = true,\n    capture = false,\n    initialValue = false,\n    window = defaultWindow\n  } = options;\n  const pressed = shallowRef(initialValue);\n  const sourceType = shallowRef(null);\n  if (!window) {\n    return {\n      pressed,\n      sourceType\n    };\n  }\n  const onPressed = (srcType) => (event) => {\n    var _a;\n    pressed.value = true;\n    sourceType.value = srcType;\n    (_a = options.onPressed) == null ? void 0 : _a.call(options, event);\n  };\n  const onReleased = (event) => {\n    var _a;\n    pressed.value = false;\n    sourceType.value = null;\n    (_a = options.onReleased) == null ? void 0 : _a.call(options, event);\n  };\n  const target = computed(() => unrefElement(options.target) || window);\n  const listenerOptions = { passive: true, capture };\n  useEventListener(target, \"mousedown\", onPressed(\"mouse\"), listenerOptions);\n  useEventListener(window, \"mouseleave\", onReleased, listenerOptions);\n  useEventListener(window, \"mouseup\", onReleased, listenerOptions);\n  if (drag) {\n    useEventListener(target, \"dragstart\", onPressed(\"mouse\"), listenerOptions);\n    useEventListener(window, \"drop\", onReleased, listenerOptions);\n    useEventListener(window, \"dragend\", onReleased, listenerOptions);\n  }\n  if (touch) {\n    useEventListener(target, \"touchstart\", onPressed(\"touch\"), listenerOptions);\n    useEventListener(window, \"touchend\", onReleased, listenerOptions);\n    useEventListener(window, \"touchcancel\", onReleased, listenerOptions);\n  }\n  return {\n    pressed,\n    sourceType\n  };\n}\n\nfunction useNavigatorLanguage(options = {}) {\n  const { window = defaultWindow } = options;\n  const navigator = window == null ? void 0 : window.navigator;\n  const isSupported = useSupported(() => navigator && \"language\" in navigator);\n  const language = shallowRef(navigator == null ? void 0 : navigator.language);\n  useEventListener(window, \"languagechange\", () => {\n    if (navigator)\n      language.value = navigator.language;\n  }, { passive: true });\n  return {\n    isSupported,\n    language\n  };\n}\n\nfunction useNetwork(options = {}) {\n  const { window = defaultWindow } = options;\n  const navigator = window == null ? void 0 : window.navigator;\n  const isSupported = useSupported(() => navigator && \"connection\" in navigator);\n  const isOnline = shallowRef(true);\n  const saveData = shallowRef(false);\n  const offlineAt = shallowRef(void 0);\n  const onlineAt = shallowRef(void 0);\n  const downlink = shallowRef(void 0);\n  const downlinkMax = shallowRef(void 0);\n  const rtt = shallowRef(void 0);\n  const effectiveType = shallowRef(void 0);\n  const type = shallowRef(\"unknown\");\n  const connection = isSupported.value && navigator.connection;\n  function updateNetworkInformation() {\n    if (!navigator)\n      return;\n    isOnline.value = navigator.onLine;\n    offlineAt.value = isOnline.value ? void 0 : Date.now();\n    onlineAt.value = isOnline.value ? Date.now() : void 0;\n    if (connection) {\n      downlink.value = connection.downlink;\n      downlinkMax.value = connection.downlinkMax;\n      effectiveType.value = connection.effectiveType;\n      rtt.value = connection.rtt;\n      saveData.value = connection.saveData;\n      type.value = connection.type;\n    }\n  }\n  const listenerOptions = { passive: true };\n  if (window) {\n    useEventListener(window, \"offline\", () => {\n      isOnline.value = false;\n      offlineAt.value = Date.now();\n    }, listenerOptions);\n    useEventListener(window, \"online\", () => {\n      isOnline.value = true;\n      onlineAt.value = Date.now();\n    }, listenerOptions);\n  }\n  if (connection)\n    useEventListener(connection, \"change\", updateNetworkInformation, listenerOptions);\n  updateNetworkInformation();\n  return {\n    isSupported,\n    isOnline: readonly(isOnline),\n    saveData: readonly(saveData),\n    offlineAt: readonly(offlineAt),\n    onlineAt: readonly(onlineAt),\n    downlink: readonly(downlink),\n    downlinkMax: readonly(downlinkMax),\n    effectiveType: readonly(effectiveType),\n    rtt: readonly(rtt),\n    type: readonly(type)\n  };\n}\n\nfunction useNow(options = {}) {\n  const {\n    controls: exposeControls = false,\n    interval = \"requestAnimationFrame\"\n  } = options;\n  const now = ref(/* @__PURE__ */ new Date());\n  const update = () => now.value = /* @__PURE__ */ new Date();\n  const controls = interval === \"requestAnimationFrame\" ? useRafFn(update, { immediate: true }) : useIntervalFn(update, interval, { immediate: true });\n  if (exposeControls) {\n    return {\n      now,\n      ...controls\n    };\n  } else {\n    return now;\n  }\n}\n\nfunction useObjectUrl(object) {\n  const url = shallowRef();\n  const release = () => {\n    if (url.value)\n      URL.revokeObjectURL(url.value);\n    url.value = void 0;\n  };\n  watch(\n    () => toValue(object),\n    (newObject) => {\n      release();\n      if (newObject)\n        url.value = URL.createObjectURL(newObject);\n    },\n    { immediate: true }\n  );\n  tryOnScopeDispose(release);\n  return readonly(url);\n}\n\nfunction useClamp(value, min, max) {\n  if (typeof value === \"function\" || isReadonly(value))\n    return computed(() => clamp(toValue(value), toValue(min), toValue(max)));\n  const _value = ref(value);\n  return computed({\n    get() {\n      return _value.value = clamp(_value.value, toValue(min), toValue(max));\n    },\n    set(value2) {\n      _value.value = clamp(value2, toValue(min), toValue(max));\n    }\n  });\n}\n\nfunction useOffsetPagination(options) {\n  const {\n    total = Number.POSITIVE_INFINITY,\n    pageSize = 10,\n    page = 1,\n    onPageChange = noop,\n    onPageSizeChange = noop,\n    onPageCountChange = noop\n  } = options;\n  const currentPageSize = useClamp(pageSize, 1, Number.POSITIVE_INFINITY);\n  const pageCount = computed(() => Math.max(\n    1,\n    Math.ceil(toValue(total) / toValue(currentPageSize))\n  ));\n  const currentPage = useClamp(page, 1, pageCount);\n  const isFirstPage = computed(() => currentPage.value === 1);\n  const isLastPage = computed(() => currentPage.value === pageCount.value);\n  if (isRef(page)) {\n    syncRef(page, currentPage, {\n      direction: isReadonly(page) ? \"ltr\" : \"both\"\n    });\n  }\n  if (isRef(pageSize)) {\n    syncRef(pageSize, currentPageSize, {\n      direction: isReadonly(pageSize) ? \"ltr\" : \"both\"\n    });\n  }\n  function prev() {\n    currentPage.value--;\n  }\n  function next() {\n    currentPage.value++;\n  }\n  const returnValue = {\n    currentPage,\n    currentPageSize,\n    pageCount,\n    isFirstPage,\n    isLastPage,\n    prev,\n    next\n  };\n  watch(currentPage, () => {\n    onPageChange(reactive(returnValue));\n  });\n  watch(currentPageSize, () => {\n    onPageSizeChange(reactive(returnValue));\n  });\n  watch(pageCount, () => {\n    onPageCountChange(reactive(returnValue));\n  });\n  return returnValue;\n}\n\nfunction useOnline(options = {}) {\n  const { isOnline } = useNetwork(options);\n  return isOnline;\n}\n\nfunction usePageLeave(options = {}) {\n  const { window = defaultWindow } = options;\n  const isLeft = shallowRef(false);\n  const handler = (event) => {\n    if (!window)\n      return;\n    event = event || window.event;\n    const from = event.relatedTarget || event.toElement;\n    isLeft.value = !from;\n  };\n  if (window) {\n    const listenerOptions = { passive: true };\n    useEventListener(window, \"mouseout\", handler, listenerOptions);\n    useEventListener(window.document, \"mouseleave\", handler, listenerOptions);\n    useEventListener(window.document, \"mouseenter\", handler, listenerOptions);\n  }\n  return isLeft;\n}\n\nfunction useScreenOrientation(options = {}) {\n  const {\n    window = defaultWindow\n  } = options;\n  const isSupported = useSupported(() => window && \"screen\" in window && \"orientation\" in window.screen);\n  const screenOrientation = isSupported.value ? window.screen.orientation : {};\n  const orientation = ref(screenOrientation.type);\n  const angle = shallowRef(screenOrientation.angle || 0);\n  if (isSupported.value) {\n    useEventListener(window, \"orientationchange\", () => {\n      orientation.value = screenOrientation.type;\n      angle.value = screenOrientation.angle;\n    }, { passive: true });\n  }\n  const lockOrientation = (type) => {\n    if (isSupported.value && typeof screenOrientation.lock === \"function\")\n      return screenOrientation.lock(type);\n    return Promise.reject(new Error(\"Not supported\"));\n  };\n  const unlockOrientation = () => {\n    if (isSupported.value && typeof screenOrientation.unlock === \"function\")\n      screenOrientation.unlock();\n  };\n  return {\n    isSupported,\n    orientation,\n    angle,\n    lockOrientation,\n    unlockOrientation\n  };\n}\n\nfunction useParallax(target, options = {}) {\n  const {\n    deviceOrientationTiltAdjust = (i) => i,\n    deviceOrientationRollAdjust = (i) => i,\n    mouseTiltAdjust = (i) => i,\n    mouseRollAdjust = (i) => i,\n    window = defaultWindow\n  } = options;\n  const orientation = reactive(useDeviceOrientation({ window }));\n  const screenOrientation = reactive(useScreenOrientation({ window }));\n  const {\n    elementX: x,\n    elementY: y,\n    elementWidth: width,\n    elementHeight: height\n  } = useMouseInElement(target, { handleOutside: false, window });\n  const source = computed(() => {\n    if (orientation.isSupported && (orientation.alpha != null && orientation.alpha !== 0 || orientation.gamma != null && orientation.gamma !== 0)) {\n      return \"deviceOrientation\";\n    }\n    return \"mouse\";\n  });\n  const roll = computed(() => {\n    if (source.value === \"deviceOrientation\") {\n      let value;\n      switch (screenOrientation.orientation) {\n        case \"landscape-primary\":\n          value = orientation.gamma / 90;\n          break;\n        case \"landscape-secondary\":\n          value = -orientation.gamma / 90;\n          break;\n        case \"portrait-primary\":\n          value = -orientation.beta / 90;\n          break;\n        case \"portrait-secondary\":\n          value = orientation.beta / 90;\n          break;\n        default:\n          value = -orientation.beta / 90;\n      }\n      return deviceOrientationRollAdjust(value);\n    } else {\n      const value = -(y.value - height.value / 2) / height.value;\n      return mouseRollAdjust(value);\n    }\n  });\n  const tilt = computed(() => {\n    if (source.value === \"deviceOrientation\") {\n      let value;\n      switch (screenOrientation.orientation) {\n        case \"landscape-primary\":\n          value = orientation.beta / 90;\n          break;\n        case \"landscape-secondary\":\n          value = -orientation.beta / 90;\n          break;\n        case \"portrait-primary\":\n          value = orientation.gamma / 90;\n          break;\n        case \"portrait-secondary\":\n          value = -orientation.gamma / 90;\n          break;\n        default:\n          value = orientation.gamma / 90;\n      }\n      return deviceOrientationTiltAdjust(value);\n    } else {\n      const value = (x.value - width.value / 2) / width.value;\n      return mouseTiltAdjust(value);\n    }\n  });\n  return { roll, tilt, source };\n}\n\nfunction useParentElement(element = useCurrentElement()) {\n  const parentElement = shallowRef();\n  const update = () => {\n    const el = unrefElement(element);\n    if (el)\n      parentElement.value = el.parentElement;\n  };\n  tryOnMounted(update);\n  watch(() => toValue(element), update);\n  return parentElement;\n}\n\nfunction usePerformanceObserver(options, callback) {\n  const {\n    window = defaultWindow,\n    immediate = true,\n    ...performanceOptions\n  } = options;\n  const isSupported = useSupported(() => window && \"PerformanceObserver\" in window);\n  let observer;\n  const stop = () => {\n    observer == null ? void 0 : observer.disconnect();\n  };\n  const start = () => {\n    if (isSupported.value) {\n      stop();\n      observer = new PerformanceObserver(callback);\n      observer.observe(performanceOptions);\n    }\n  };\n  tryOnScopeDispose(stop);\n  if (immediate)\n    start();\n  return {\n    isSupported,\n    start,\n    stop\n  };\n}\n\nconst defaultState = {\n  x: 0,\n  y: 0,\n  pointerId: 0,\n  pressure: 0,\n  tiltX: 0,\n  tiltY: 0,\n  width: 0,\n  height: 0,\n  twist: 0,\n  pointerType: null\n};\nconst keys = /* @__PURE__ */ Object.keys(defaultState);\nfunction usePointer(options = {}) {\n  const {\n    target = defaultWindow\n  } = options;\n  const isInside = shallowRef(false);\n  const state = ref(options.initialValue || {});\n  Object.assign(state.value, defaultState, state.value);\n  const handler = (event) => {\n    isInside.value = true;\n    if (options.pointerTypes && !options.pointerTypes.includes(event.pointerType))\n      return;\n    state.value = objectPick(event, keys, false);\n  };\n  if (target) {\n    const listenerOptions = { passive: true };\n    useEventListener(target, [\"pointerdown\", \"pointermove\", \"pointerup\"], handler, listenerOptions);\n    useEventListener(target, \"pointerleave\", () => isInside.value = false, listenerOptions);\n  }\n  return {\n    ...toRefs(state),\n    isInside\n  };\n}\n\nfunction usePointerLock(target, options = {}) {\n  const { document = defaultDocument } = options;\n  const isSupported = useSupported(() => document && \"pointerLockElement\" in document);\n  const element = shallowRef();\n  const triggerElement = shallowRef();\n  let targetElement;\n  if (isSupported.value) {\n    const listenerOptions = { passive: true };\n    useEventListener(document, \"pointerlockchange\", () => {\n      var _a;\n      const currentElement = (_a = document.pointerLockElement) != null ? _a : element.value;\n      if (targetElement && currentElement === targetElement) {\n        element.value = document.pointerLockElement;\n        if (!element.value)\n          targetElement = triggerElement.value = null;\n      }\n    }, listenerOptions);\n    useEventListener(document, \"pointerlockerror\", () => {\n      var _a;\n      const currentElement = (_a = document.pointerLockElement) != null ? _a : element.value;\n      if (targetElement && currentElement === targetElement) {\n        const action = document.pointerLockElement ? \"release\" : \"acquire\";\n        throw new Error(`Failed to ${action} pointer lock.`);\n      }\n    }, listenerOptions);\n  }\n  async function lock(e) {\n    var _a;\n    if (!isSupported.value)\n      throw new Error(\"Pointer Lock API is not supported by your browser.\");\n    triggerElement.value = e instanceof Event ? e.currentTarget : null;\n    targetElement = e instanceof Event ? (_a = unrefElement(target)) != null ? _a : triggerElement.value : unrefElement(e);\n    if (!targetElement)\n      throw new Error(\"Target element undefined.\");\n    targetElement.requestPointerLock();\n    return await until(element).toBe(targetElement);\n  }\n  async function unlock() {\n    if (!element.value)\n      return false;\n    document.exitPointerLock();\n    await until(element).toBeNull();\n    return true;\n  }\n  return {\n    isSupported,\n    element,\n    triggerElement,\n    lock,\n    unlock\n  };\n}\n\nfunction usePointerSwipe(target, options = {}) {\n  const targetRef = toRef(target);\n  const {\n    threshold = 50,\n    onSwipe,\n    onSwipeEnd,\n    onSwipeStart,\n    disableTextSelect = false\n  } = options;\n  const posStart = reactive({ x: 0, y: 0 });\n  const updatePosStart = (x, y) => {\n    posStart.x = x;\n    posStart.y = y;\n  };\n  const posEnd = reactive({ x: 0, y: 0 });\n  const updatePosEnd = (x, y) => {\n    posEnd.x = x;\n    posEnd.y = y;\n  };\n  const distanceX = computed(() => posStart.x - posEnd.x);\n  const distanceY = computed(() => posStart.y - posEnd.y);\n  const { max, abs } = Math;\n  const isThresholdExceeded = computed(() => max(abs(distanceX.value), abs(distanceY.value)) >= threshold);\n  const isSwiping = shallowRef(false);\n  const isPointerDown = shallowRef(false);\n  const direction = computed(() => {\n    if (!isThresholdExceeded.value)\n      return \"none\";\n    if (abs(distanceX.value) > abs(distanceY.value)) {\n      return distanceX.value > 0 ? \"left\" : \"right\";\n    } else {\n      return distanceY.value > 0 ? \"up\" : \"down\";\n    }\n  });\n  const eventIsAllowed = (e) => {\n    var _a, _b, _c;\n    const isReleasingButton = e.buttons === 0;\n    const isPrimaryButton = e.buttons === 1;\n    return (_c = (_b = (_a = options.pointerTypes) == null ? void 0 : _a.includes(e.pointerType)) != null ? _b : isReleasingButton || isPrimaryButton) != null ? _c : true;\n  };\n  const listenerOptions = { passive: true };\n  const stops = [\n    useEventListener(target, \"pointerdown\", (e) => {\n      if (!eventIsAllowed(e))\n        return;\n      isPointerDown.value = true;\n      const eventTarget = e.target;\n      eventTarget == null ? void 0 : eventTarget.setPointerCapture(e.pointerId);\n      const { clientX: x, clientY: y } = e;\n      updatePosStart(x, y);\n      updatePosEnd(x, y);\n      onSwipeStart == null ? void 0 : onSwipeStart(e);\n    }, listenerOptions),\n    useEventListener(target, \"pointermove\", (e) => {\n      if (!eventIsAllowed(e))\n        return;\n      if (!isPointerDown.value)\n        return;\n      const { clientX: x, clientY: y } = e;\n      updatePosEnd(x, y);\n      if (!isSwiping.value && isThresholdExceeded.value)\n        isSwiping.value = true;\n      if (isSwiping.value)\n        onSwipe == null ? void 0 : onSwipe(e);\n    }, listenerOptions),\n    useEventListener(target, \"pointerup\", (e) => {\n      if (!eventIsAllowed(e))\n        return;\n      if (isSwiping.value)\n        onSwipeEnd == null ? void 0 : onSwipeEnd(e, direction.value);\n      isPointerDown.value = false;\n      isSwiping.value = false;\n    }, listenerOptions)\n  ];\n  tryOnMounted(() => {\n    var _a, _b, _c, _d, _e, _f, _g, _h;\n    (_b = (_a = targetRef.value) == null ? void 0 : _a.style) == null ? void 0 : _b.setProperty(\"touch-action\", \"none\");\n    if (disableTextSelect) {\n      (_d = (_c = targetRef.value) == null ? void 0 : _c.style) == null ? void 0 : _d.setProperty(\"-webkit-user-select\", \"none\");\n      (_f = (_e = targetRef.value) == null ? void 0 : _e.style) == null ? void 0 : _f.setProperty(\"-ms-user-select\", \"none\");\n      (_h = (_g = targetRef.value) == null ? void 0 : _g.style) == null ? void 0 : _h.setProperty(\"user-select\", \"none\");\n    }\n  });\n  const stop = () => stops.forEach((s) => s());\n  return {\n    isSwiping: readonly(isSwiping),\n    direction: readonly(direction),\n    posStart: readonly(posStart),\n    posEnd: readonly(posEnd),\n    distanceX,\n    distanceY,\n    stop\n  };\n}\n\nfunction usePreferredColorScheme(options) {\n  const isLight = useMediaQuery(\"(prefers-color-scheme: light)\", options);\n  const isDark = useMediaQuery(\"(prefers-color-scheme: dark)\", options);\n  return computed(() => {\n    if (isDark.value)\n      return \"dark\";\n    if (isLight.value)\n      return \"light\";\n    return \"no-preference\";\n  });\n}\n\nfunction usePreferredContrast(options) {\n  const isMore = useMediaQuery(\"(prefers-contrast: more)\", options);\n  const isLess = useMediaQuery(\"(prefers-contrast: less)\", options);\n  const isCustom = useMediaQuery(\"(prefers-contrast: custom)\", options);\n  return computed(() => {\n    if (isMore.value)\n      return \"more\";\n    if (isLess.value)\n      return \"less\";\n    if (isCustom.value)\n      return \"custom\";\n    return \"no-preference\";\n  });\n}\n\nfunction usePreferredLanguages(options = {}) {\n  const { window = defaultWindow } = options;\n  if (!window)\n    return ref([\"en\"]);\n  const navigator = window.navigator;\n  const value = ref(navigator.languages);\n  useEventListener(window, \"languagechange\", () => {\n    value.value = navigator.languages;\n  }, { passive: true });\n  return value;\n}\n\nfunction usePreferredReducedMotion(options) {\n  const isReduced = useMediaQuery(\"(prefers-reduced-motion: reduce)\", options);\n  return computed(() => {\n    if (isReduced.value)\n      return \"reduce\";\n    return \"no-preference\";\n  });\n}\n\nfunction usePreferredReducedTransparency(options) {\n  const isReduced = useMediaQuery(\"(prefers-reduced-transparency: reduce)\", options);\n  return computed(() => {\n    if (isReduced.value)\n      return \"reduce\";\n    return \"no-preference\";\n  });\n}\n\nfunction usePrevious(value, initialValue) {\n  const previous = shallowRef(initialValue);\n  watch(\n    toRef(value),\n    (_, oldValue) => {\n      previous.value = oldValue;\n    },\n    { flush: \"sync\" }\n  );\n  return readonly(previous);\n}\n\nconst topVarName = \"--vueuse-safe-area-top\";\nconst rightVarName = \"--vueuse-safe-area-right\";\nconst bottomVarName = \"--vueuse-safe-area-bottom\";\nconst leftVarName = \"--vueuse-safe-area-left\";\nfunction useScreenSafeArea() {\n  const top = shallowRef(\"\");\n  const right = shallowRef(\"\");\n  const bottom = shallowRef(\"\");\n  const left = shallowRef(\"\");\n  if (isClient) {\n    const topCssVar = useCssVar(topVarName);\n    const rightCssVar = useCssVar(rightVarName);\n    const bottomCssVar = useCssVar(bottomVarName);\n    const leftCssVar = useCssVar(leftVarName);\n    topCssVar.value = \"env(safe-area-inset-top, 0px)\";\n    rightCssVar.value = \"env(safe-area-inset-right, 0px)\";\n    bottomCssVar.value = \"env(safe-area-inset-bottom, 0px)\";\n    leftCssVar.value = \"env(safe-area-inset-left, 0px)\";\n    update();\n    useEventListener(\"resize\", useDebounceFn(update), { passive: true });\n  }\n  function update() {\n    top.value = getValue(topVarName);\n    right.value = getValue(rightVarName);\n    bottom.value = getValue(bottomVarName);\n    left.value = getValue(leftVarName);\n  }\n  return {\n    top,\n    right,\n    bottom,\n    left,\n    update\n  };\n}\nfunction getValue(position) {\n  return getComputedStyle(document.documentElement).getPropertyValue(position);\n}\n\nfunction useScriptTag(src, onLoaded = noop, options = {}) {\n  const {\n    immediate = true,\n    manual = false,\n    type = \"text/javascript\",\n    async = true,\n    crossOrigin,\n    referrerPolicy,\n    noModule,\n    defer,\n    document = defaultDocument,\n    attrs = {}\n  } = options;\n  const scriptTag = shallowRef(null);\n  let _promise = null;\n  const loadScript = (waitForScriptLoad) => new Promise((resolve, reject) => {\n    const resolveWithElement = (el2) => {\n      scriptTag.value = el2;\n      resolve(el2);\n      return el2;\n    };\n    if (!document) {\n      resolve(false);\n      return;\n    }\n    let shouldAppend = false;\n    let el = document.querySelector(`script[src=\"${toValue(src)}\"]`);\n    if (!el) {\n      el = document.createElement(\"script\");\n      el.type = type;\n      el.async = async;\n      el.src = toValue(src);\n      if (defer)\n        el.defer = defer;\n      if (crossOrigin)\n        el.crossOrigin = crossOrigin;\n      if (noModule)\n        el.noModule = noModule;\n      if (referrerPolicy)\n        el.referrerPolicy = referrerPolicy;\n      Object.entries(attrs).forEach(([name, value]) => el == null ? void 0 : el.setAttribute(name, value));\n      shouldAppend = true;\n    } else if (el.hasAttribute(\"data-loaded\")) {\n      resolveWithElement(el);\n    }\n    const listenerOptions = {\n      passive: true\n    };\n    useEventListener(el, \"error\", (event) => reject(event), listenerOptions);\n    useEventListener(el, \"abort\", (event) => reject(event), listenerOptions);\n    useEventListener(el, \"load\", () => {\n      el.setAttribute(\"data-loaded\", \"true\");\n      onLoaded(el);\n      resolveWithElement(el);\n    }, listenerOptions);\n    if (shouldAppend)\n      el = document.head.appendChild(el);\n    if (!waitForScriptLoad)\n      resolveWithElement(el);\n  });\n  const load = (waitForScriptLoad = true) => {\n    if (!_promise)\n      _promise = loadScript(waitForScriptLoad);\n    return _promise;\n  };\n  const unload = () => {\n    if (!document)\n      return;\n    _promise = null;\n    if (scriptTag.value)\n      scriptTag.value = null;\n    const el = document.querySelector(`script[src=\"${toValue(src)}\"]`);\n    if (el)\n      document.head.removeChild(el);\n  };\n  if (immediate && !manual)\n    tryOnMounted(load);\n  if (!manual)\n    tryOnUnmounted(unload);\n  return { scriptTag, load, unload };\n}\n\nfunction checkOverflowScroll(ele) {\n  const style = window.getComputedStyle(ele);\n  if (style.overflowX === \"scroll\" || style.overflowY === \"scroll\" || style.overflowX === \"auto\" && ele.clientWidth < ele.scrollWidth || style.overflowY === \"auto\" && ele.clientHeight < ele.scrollHeight) {\n    return true;\n  } else {\n    const parent = ele.parentNode;\n    if (!parent || parent.tagName === \"BODY\")\n      return false;\n    return checkOverflowScroll(parent);\n  }\n}\nfunction preventDefault(rawEvent) {\n  const e = rawEvent || window.event;\n  const _target = e.target;\n  if (checkOverflowScroll(_target))\n    return false;\n  if (e.touches.length > 1)\n    return true;\n  if (e.preventDefault)\n    e.preventDefault();\n  return false;\n}\nconst elInitialOverflow = /* @__PURE__ */ new WeakMap();\nfunction useScrollLock(element, initialState = false) {\n  const isLocked = shallowRef(initialState);\n  let stopTouchMoveListener = null;\n  let initialOverflow = \"\";\n  watch(toRef(element), (el) => {\n    const target = resolveElement(toValue(el));\n    if (target) {\n      const ele = target;\n      if (!elInitialOverflow.get(ele))\n        elInitialOverflow.set(ele, ele.style.overflow);\n      if (ele.style.overflow !== \"hidden\")\n        initialOverflow = ele.style.overflow;\n      if (ele.style.overflow === \"hidden\")\n        return isLocked.value = true;\n      if (isLocked.value)\n        return ele.style.overflow = \"hidden\";\n    }\n  }, {\n    immediate: true\n  });\n  const lock = () => {\n    const el = resolveElement(toValue(element));\n    if (!el || isLocked.value)\n      return;\n    if (isIOS) {\n      stopTouchMoveListener = useEventListener(\n        el,\n        \"touchmove\",\n        (e) => {\n          preventDefault(e);\n        },\n        { passive: false }\n      );\n    }\n    el.style.overflow = \"hidden\";\n    isLocked.value = true;\n  };\n  const unlock = () => {\n    const el = resolveElement(toValue(element));\n    if (!el || !isLocked.value)\n      return;\n    if (isIOS)\n      stopTouchMoveListener == null ? void 0 : stopTouchMoveListener();\n    el.style.overflow = initialOverflow;\n    elInitialOverflow.delete(el);\n    isLocked.value = false;\n  };\n  tryOnScopeDispose(unlock);\n  return computed({\n    get() {\n      return isLocked.value;\n    },\n    set(v) {\n      if (v)\n        lock();\n      else unlock();\n    }\n  });\n}\n\nfunction useSessionStorage(key, initialValue, options = {}) {\n  const { window = defaultWindow } = options;\n  return useStorage(key, initialValue, window == null ? void 0 : window.sessionStorage, options);\n}\n\nfunction useShare(shareOptions = {}, options = {}) {\n  const { navigator = defaultNavigator } = options;\n  const _navigator = navigator;\n  const isSupported = useSupported(() => _navigator && \"canShare\" in _navigator);\n  const share = async (overrideOptions = {}) => {\n    if (isSupported.value) {\n      const data = {\n        ...toValue(shareOptions),\n        ...toValue(overrideOptions)\n      };\n      let granted = true;\n      if (data.files && _navigator.canShare)\n        granted = _navigator.canShare({ files: data.files });\n      if (granted)\n        return _navigator.share(data);\n    }\n  };\n  return {\n    isSupported,\n    share\n  };\n}\n\nconst defaultSortFn = (source, compareFn) => source.sort(compareFn);\nconst defaultCompare = (a, b) => a - b;\nfunction useSorted(...args) {\n  var _a, _b, _c, _d;\n  const [source] = args;\n  let compareFn = defaultCompare;\n  let options = {};\n  if (args.length === 2) {\n    if (typeof args[1] === \"object\") {\n      options = args[1];\n      compareFn = (_a = options.compareFn) != null ? _a : defaultCompare;\n    } else {\n      compareFn = (_b = args[1]) != null ? _b : defaultCompare;\n    }\n  } else if (args.length > 2) {\n    compareFn = (_c = args[1]) != null ? _c : defaultCompare;\n    options = (_d = args[2]) != null ? _d : {};\n  }\n  const {\n    dirty = false,\n    sortFn = defaultSortFn\n  } = options;\n  if (!dirty)\n    return computed(() => sortFn([...toValue(source)], compareFn));\n  watchEffect(() => {\n    const result = sortFn(toValue(source), compareFn);\n    if (isRef(source))\n      source.value = result;\n    else\n      source.splice(0, source.length, ...result);\n  });\n  return source;\n}\n\nfunction useSpeechRecognition(options = {}) {\n  const {\n    interimResults = true,\n    continuous = true,\n    maxAlternatives = 1,\n    window = defaultWindow\n  } = options;\n  const lang = toRef(options.lang || \"en-US\");\n  const isListening = shallowRef(false);\n  const isFinal = shallowRef(false);\n  const result = shallowRef(\"\");\n  const error = shallowRef(void 0);\n  let recognition;\n  const start = () => {\n    isListening.value = true;\n  };\n  const stop = () => {\n    isListening.value = false;\n  };\n  const toggle = (value = !isListening.value) => {\n    if (value) {\n      start();\n    } else {\n      stop();\n    }\n  };\n  const SpeechRecognition = window && (window.SpeechRecognition || window.webkitSpeechRecognition);\n  const isSupported = useSupported(() => SpeechRecognition);\n  if (isSupported.value) {\n    recognition = new SpeechRecognition();\n    recognition.continuous = continuous;\n    recognition.interimResults = interimResults;\n    recognition.lang = toValue(lang);\n    recognition.maxAlternatives = maxAlternatives;\n    recognition.onstart = () => {\n      isListening.value = true;\n      isFinal.value = false;\n    };\n    watch(lang, (lang2) => {\n      if (recognition && !isListening.value)\n        recognition.lang = lang2;\n    });\n    recognition.onresult = (event) => {\n      const currentResult = event.results[event.resultIndex];\n      const { transcript } = currentResult[0];\n      isFinal.value = currentResult.isFinal;\n      result.value = transcript;\n      error.value = void 0;\n    };\n    recognition.onerror = (event) => {\n      error.value = event;\n    };\n    recognition.onend = () => {\n      isListening.value = false;\n      recognition.lang = toValue(lang);\n    };\n    watch(isListening, (newValue, oldValue) => {\n      if (newValue === oldValue)\n        return;\n      if (newValue)\n        recognition.start();\n      else\n        recognition.stop();\n    });\n  }\n  tryOnScopeDispose(() => {\n    stop();\n  });\n  return {\n    isSupported,\n    isListening,\n    isFinal,\n    recognition,\n    result,\n    error,\n    toggle,\n    start,\n    stop\n  };\n}\n\nfunction useSpeechSynthesis(text, options = {}) {\n  const {\n    pitch = 1,\n    rate = 1,\n    volume = 1,\n    window = defaultWindow\n  } = options;\n  const synth = window && window.speechSynthesis;\n  const isSupported = useSupported(() => synth);\n  const isPlaying = shallowRef(false);\n  const status = shallowRef(\"init\");\n  const spokenText = toRef(text || \"\");\n  const lang = toRef(options.lang || \"en-US\");\n  const error = shallowRef(void 0);\n  const toggle = (value = !isPlaying.value) => {\n    isPlaying.value = value;\n  };\n  const bindEventsForUtterance = (utterance2) => {\n    utterance2.lang = toValue(lang);\n    utterance2.voice = toValue(options.voice) || null;\n    utterance2.pitch = toValue(pitch);\n    utterance2.rate = toValue(rate);\n    utterance2.volume = volume;\n    utterance2.onstart = () => {\n      isPlaying.value = true;\n      status.value = \"play\";\n    };\n    utterance2.onpause = () => {\n      isPlaying.value = false;\n      status.value = \"pause\";\n    };\n    utterance2.onresume = () => {\n      isPlaying.value = true;\n      status.value = \"play\";\n    };\n    utterance2.onend = () => {\n      isPlaying.value = false;\n      status.value = \"end\";\n    };\n    utterance2.onerror = (event) => {\n      error.value = event;\n    };\n  };\n  const utterance = computed(() => {\n    isPlaying.value = false;\n    status.value = \"init\";\n    const newUtterance = new SpeechSynthesisUtterance(spokenText.value);\n    bindEventsForUtterance(newUtterance);\n    return newUtterance;\n  });\n  const speak = () => {\n    synth.cancel();\n    if (utterance)\n      synth.speak(utterance.value);\n  };\n  const stop = () => {\n    synth.cancel();\n    isPlaying.value = false;\n  };\n  if (isSupported.value) {\n    bindEventsForUtterance(utterance.value);\n    watch(lang, (lang2) => {\n      if (utterance.value && !isPlaying.value)\n        utterance.value.lang = lang2;\n    });\n    if (options.voice) {\n      watch(options.voice, () => {\n        synth.cancel();\n      });\n    }\n    watch(isPlaying, () => {\n      if (isPlaying.value)\n        synth.resume();\n      else\n        synth.pause();\n    });\n  }\n  tryOnScopeDispose(() => {\n    isPlaying.value = false;\n  });\n  return {\n    isSupported,\n    isPlaying,\n    status,\n    utterance,\n    error,\n    stop,\n    toggle,\n    speak\n  };\n}\n\nfunction useStepper(steps, initialStep) {\n  const stepsRef = ref(steps);\n  const stepNames = computed(() => Array.isArray(stepsRef.value) ? stepsRef.value : Object.keys(stepsRef.value));\n  const index = ref(stepNames.value.indexOf(initialStep != null ? initialStep : stepNames.value[0]));\n  const current = computed(() => at(index.value));\n  const isFirst = computed(() => index.value === 0);\n  const isLast = computed(() => index.value === stepNames.value.length - 1);\n  const next = computed(() => stepNames.value[index.value + 1]);\n  const previous = computed(() => stepNames.value[index.value - 1]);\n  function at(index2) {\n    if (Array.isArray(stepsRef.value))\n      return stepsRef.value[index2];\n    return stepsRef.value[stepNames.value[index2]];\n  }\n  function get(step) {\n    if (!stepNames.value.includes(step))\n      return;\n    return at(stepNames.value.indexOf(step));\n  }\n  function goTo(step) {\n    if (stepNames.value.includes(step))\n      index.value = stepNames.value.indexOf(step);\n  }\n  function goToNext() {\n    if (isLast.value)\n      return;\n    index.value++;\n  }\n  function goToPrevious() {\n    if (isFirst.value)\n      return;\n    index.value--;\n  }\n  function goBackTo(step) {\n    if (isAfter(step))\n      goTo(step);\n  }\n  function isNext(step) {\n    return stepNames.value.indexOf(step) === index.value + 1;\n  }\n  function isPrevious(step) {\n    return stepNames.value.indexOf(step) === index.value - 1;\n  }\n  function isCurrent(step) {\n    return stepNames.value.indexOf(step) === index.value;\n  }\n  function isBefore(step) {\n    return index.value < stepNames.value.indexOf(step);\n  }\n  function isAfter(step) {\n    return index.value > stepNames.value.indexOf(step);\n  }\n  return {\n    steps: stepsRef,\n    stepNames,\n    index,\n    current,\n    next,\n    previous,\n    isFirst,\n    isLast,\n    at,\n    get,\n    goTo,\n    goToNext,\n    goToPrevious,\n    goBackTo,\n    isNext,\n    isPrevious,\n    isCurrent,\n    isBefore,\n    isAfter\n  };\n}\n\nfunction useStorageAsync(key, initialValue, storage, options = {}) {\n  var _a;\n  const {\n    flush = \"pre\",\n    deep = true,\n    listenToStorageChanges = true,\n    writeDefaults = true,\n    mergeDefaults = false,\n    shallow,\n    window = defaultWindow,\n    eventFilter,\n    onError = (e) => {\n      console.error(e);\n    }\n  } = options;\n  const rawInit = toValue(initialValue);\n  const type = guessSerializerType(rawInit);\n  const data = (shallow ? shallowRef : ref)(toValue(initialValue));\n  const serializer = (_a = options.serializer) != null ? _a : StorageSerializers[type];\n  if (!storage) {\n    try {\n      storage = getSSRHandler(\"getDefaultStorageAsync\", () => {\n        var _a2;\n        return (_a2 = defaultWindow) == null ? void 0 : _a2.localStorage;\n      })();\n    } catch (e) {\n      onError(e);\n    }\n  }\n  async function read(event) {\n    if (!storage || event && event.key !== key)\n      return;\n    try {\n      const rawValue = event ? event.newValue : await storage.getItem(key);\n      if (rawValue == null) {\n        data.value = rawInit;\n        if (writeDefaults && rawInit !== null)\n          await storage.setItem(key, await serializer.write(rawInit));\n      } else if (mergeDefaults) {\n        const value = await serializer.read(rawValue);\n        if (typeof mergeDefaults === \"function\")\n          data.value = mergeDefaults(value, rawInit);\n        else if (type === \"object\" && !Array.isArray(value))\n          data.value = { ...rawInit, ...value };\n        else data.value = value;\n      } else {\n        data.value = await serializer.read(rawValue);\n      }\n    } catch (e) {\n      onError(e);\n    }\n  }\n  read();\n  if (window && listenToStorageChanges)\n    useEventListener(window, \"storage\", (e) => Promise.resolve().then(() => read(e)), { passive: true });\n  if (storage) {\n    watchWithFilter(\n      data,\n      async () => {\n        try {\n          if (data.value == null)\n            await storage.removeItem(key);\n          else\n            await storage.setItem(key, await serializer.write(data.value));\n        } catch (e) {\n          onError(e);\n        }\n      },\n      {\n        flush,\n        deep,\n        eventFilter\n      }\n    );\n  }\n  return data;\n}\n\nlet _id = 0;\nfunction useStyleTag(css, options = {}) {\n  const isLoaded = shallowRef(false);\n  const {\n    document = defaultDocument,\n    immediate = true,\n    manual = false,\n    id = `vueuse_styletag_${++_id}`\n  } = options;\n  const cssRef = shallowRef(css);\n  let stop = () => {\n  };\n  const load = () => {\n    if (!document)\n      return;\n    const el = document.getElementById(id) || document.createElement(\"style\");\n    if (!el.isConnected) {\n      el.id = id;\n      if (options.media)\n        el.media = options.media;\n      document.head.appendChild(el);\n    }\n    if (isLoaded.value)\n      return;\n    stop = watch(\n      cssRef,\n      (value) => {\n        el.textContent = value;\n      },\n      { immediate: true }\n    );\n    isLoaded.value = true;\n  };\n  const unload = () => {\n    if (!document || !isLoaded.value)\n      return;\n    stop();\n    document.head.removeChild(document.getElementById(id));\n    isLoaded.value = false;\n  };\n  if (immediate && !manual)\n    tryOnMounted(load);\n  if (!manual)\n    tryOnScopeDispose(unload);\n  return {\n    id,\n    css: cssRef,\n    unload,\n    load,\n    isLoaded: readonly(isLoaded)\n  };\n}\n\nfunction useSwipe(target, options = {}) {\n  const {\n    threshold = 50,\n    onSwipe,\n    onSwipeEnd,\n    onSwipeStart,\n    passive = true\n  } = options;\n  const coordsStart = reactive({ x: 0, y: 0 });\n  const coordsEnd = reactive({ x: 0, y: 0 });\n  const diffX = computed(() => coordsStart.x - coordsEnd.x);\n  const diffY = computed(() => coordsStart.y - coordsEnd.y);\n  const { max, abs } = Math;\n  const isThresholdExceeded = computed(() => max(abs(diffX.value), abs(diffY.value)) >= threshold);\n  const isSwiping = shallowRef(false);\n  const direction = computed(() => {\n    if (!isThresholdExceeded.value)\n      return \"none\";\n    if (abs(diffX.value) > abs(diffY.value)) {\n      return diffX.value > 0 ? \"left\" : \"right\";\n    } else {\n      return diffY.value > 0 ? \"up\" : \"down\";\n    }\n  });\n  const getTouchEventCoords = (e) => [e.touches[0].clientX, e.touches[0].clientY];\n  const updateCoordsStart = (x, y) => {\n    coordsStart.x = x;\n    coordsStart.y = y;\n  };\n  const updateCoordsEnd = (x, y) => {\n    coordsEnd.x = x;\n    coordsEnd.y = y;\n  };\n  const listenerOptions = { passive, capture: !passive };\n  const onTouchEnd = (e) => {\n    if (isSwiping.value)\n      onSwipeEnd == null ? void 0 : onSwipeEnd(e, direction.value);\n    isSwiping.value = false;\n  };\n  const stops = [\n    useEventListener(target, \"touchstart\", (e) => {\n      if (e.touches.length !== 1)\n        return;\n      const [x, y] = getTouchEventCoords(e);\n      updateCoordsStart(x, y);\n      updateCoordsEnd(x, y);\n      onSwipeStart == null ? void 0 : onSwipeStart(e);\n    }, listenerOptions),\n    useEventListener(target, \"touchmove\", (e) => {\n      if (e.touches.length !== 1)\n        return;\n      const [x, y] = getTouchEventCoords(e);\n      updateCoordsEnd(x, y);\n      if (listenerOptions.capture && !listenerOptions.passive && Math.abs(diffX.value) > Math.abs(diffY.value))\n        e.preventDefault();\n      if (!isSwiping.value && isThresholdExceeded.value)\n        isSwiping.value = true;\n      if (isSwiping.value)\n        onSwipe == null ? void 0 : onSwipe(e);\n    }, listenerOptions),\n    useEventListener(target, [\"touchend\", \"touchcancel\"], onTouchEnd, listenerOptions)\n  ];\n  const stop = () => stops.forEach((s) => s());\n  return {\n    isSwiping,\n    direction,\n    coordsStart,\n    coordsEnd,\n    lengthX: diffX,\n    lengthY: diffY,\n    stop,\n    // TODO: Remove in the next major version\n    isPassiveEventSupported: true\n  };\n}\n\nfunction useTemplateRefsList() {\n  const refs = ref([]);\n  refs.value.set = (el) => {\n    if (el)\n      refs.value.push(el);\n  };\n  onBeforeUpdate(() => {\n    refs.value.length = 0;\n  });\n  return refs;\n}\n\nfunction useTextDirection(options = {}) {\n  const {\n    document = defaultDocument,\n    selector = \"html\",\n    observe = false,\n    initialValue = \"ltr\"\n  } = options;\n  function getValue() {\n    var _a, _b;\n    return (_b = (_a = document == null ? void 0 : document.querySelector(selector)) == null ? void 0 : _a.getAttribute(\"dir\")) != null ? _b : initialValue;\n  }\n  const dir = ref(getValue());\n  tryOnMounted(() => dir.value = getValue());\n  if (observe && document) {\n    useMutationObserver(\n      document.querySelector(selector),\n      () => dir.value = getValue(),\n      { attributes: true }\n    );\n  }\n  return computed({\n    get() {\n      return dir.value;\n    },\n    set(v) {\n      var _a, _b;\n      dir.value = v;\n      if (!document)\n        return;\n      if (dir.value)\n        (_a = document.querySelector(selector)) == null ? void 0 : _a.setAttribute(\"dir\", dir.value);\n      else\n        (_b = document.querySelector(selector)) == null ? void 0 : _b.removeAttribute(\"dir\");\n    }\n  });\n}\n\nfunction getRangesFromSelection(selection) {\n  var _a;\n  const rangeCount = (_a = selection.rangeCount) != null ? _a : 0;\n  return Array.from({ length: rangeCount }, (_, i) => selection.getRangeAt(i));\n}\nfunction useTextSelection(options = {}) {\n  const {\n    window = defaultWindow\n  } = options;\n  const selection = ref(null);\n  const text = computed(() => {\n    var _a, _b;\n    return (_b = (_a = selection.value) == null ? void 0 : _a.toString()) != null ? _b : \"\";\n  });\n  const ranges = computed(() => selection.value ? getRangesFromSelection(selection.value) : []);\n  const rects = computed(() => ranges.value.map((range) => range.getBoundingClientRect()));\n  function onSelectionChange() {\n    selection.value = null;\n    if (window)\n      selection.value = window.getSelection();\n  }\n  if (window)\n    useEventListener(window.document, \"selectionchange\", onSelectionChange, { passive: true });\n  return {\n    text,\n    rects,\n    ranges,\n    selection\n  };\n}\n\nfunction tryRequestAnimationFrame(window = defaultWindow, fn) {\n  if (window && typeof window.requestAnimationFrame === \"function\") {\n    window.requestAnimationFrame(fn);\n  } else {\n    fn();\n  }\n}\nfunction useTextareaAutosize(options = {}) {\n  var _a, _b;\n  const { window = defaultWindow } = options;\n  const textarea = toRef(options == null ? void 0 : options.element);\n  const input = toRef((_a = options == null ? void 0 : options.input) != null ? _a : \"\");\n  const styleProp = (_b = options == null ? void 0 : options.styleProp) != null ? _b : \"height\";\n  const textareaScrollHeight = shallowRef(1);\n  const textareaOldWidth = shallowRef(0);\n  function triggerResize() {\n    var _a2;\n    if (!textarea.value)\n      return;\n    let height = \"\";\n    textarea.value.style[styleProp] = \"1px\";\n    textareaScrollHeight.value = (_a2 = textarea.value) == null ? void 0 : _a2.scrollHeight;\n    const _styleTarget = toValue(options == null ? void 0 : options.styleTarget);\n    if (_styleTarget)\n      _styleTarget.style[styleProp] = `${textareaScrollHeight.value}px`;\n    else\n      height = `${textareaScrollHeight.value}px`;\n    textarea.value.style[styleProp] = height;\n  }\n  watch([input, textarea], () => nextTick(triggerResize), { immediate: true });\n  watch(textareaScrollHeight, () => {\n    var _a2;\n    return (_a2 = options == null ? void 0 : options.onResize) == null ? void 0 : _a2.call(options);\n  });\n  useResizeObserver(textarea, ([{ contentRect }]) => {\n    if (textareaOldWidth.value === contentRect.width)\n      return;\n    tryRequestAnimationFrame(window, () => {\n      textareaOldWidth.value = contentRect.width;\n      triggerResize();\n    });\n  });\n  if (options == null ? void 0 : options.watch)\n    watch(options.watch, triggerResize, { immediate: true, deep: true });\n  return {\n    textarea,\n    input,\n    triggerResize\n  };\n}\n\nfunction useThrottledRefHistory(source, options = {}) {\n  const { throttle = 200, trailing = true } = options;\n  const filter = throttleFilter(throttle, trailing);\n  const history = useRefHistory(source, { ...options, eventFilter: filter });\n  return {\n    ...history\n  };\n}\n\nconst DEFAULT_UNITS = [\n  { max: 6e4, value: 1e3, name: \"second\" },\n  { max: 276e4, value: 6e4, name: \"minute\" },\n  { max: 72e6, value: 36e5, name: \"hour\" },\n  { max: 5184e5, value: 864e5, name: \"day\" },\n  { max: 24192e5, value: 6048e5, name: \"week\" },\n  { max: 28512e6, value: 2592e6, name: \"month\" },\n  { max: Number.POSITIVE_INFINITY, value: 31536e6, name: \"year\" }\n];\nconst DEFAULT_MESSAGES = {\n  justNow: \"just now\",\n  past: (n) => n.match(/\\d/) ? `${n} ago` : n,\n  future: (n) => n.match(/\\d/) ? `in ${n}` : n,\n  month: (n, past) => n === 1 ? past ? \"last month\" : \"next month\" : `${n} month${n > 1 ? \"s\" : \"\"}`,\n  year: (n, past) => n === 1 ? past ? \"last year\" : \"next year\" : `${n} year${n > 1 ? \"s\" : \"\"}`,\n  day: (n, past) => n === 1 ? past ? \"yesterday\" : \"tomorrow\" : `${n} day${n > 1 ? \"s\" : \"\"}`,\n  week: (n, past) => n === 1 ? past ? \"last week\" : \"next week\" : `${n} week${n > 1 ? \"s\" : \"\"}`,\n  hour: (n) => `${n} hour${n > 1 ? \"s\" : \"\"}`,\n  minute: (n) => `${n} minute${n > 1 ? \"s\" : \"\"}`,\n  second: (n) => `${n} second${n > 1 ? \"s\" : \"\"}`,\n  invalid: \"\"\n};\nfunction DEFAULT_FORMATTER(date) {\n  return date.toISOString().slice(0, 10);\n}\nfunction useTimeAgo(time, options = {}) {\n  const {\n    controls: exposeControls = false,\n    updateInterval = 3e4\n  } = options;\n  const { now, ...controls } = useNow({ interval: updateInterval, controls: true });\n  const timeAgo = computed(() => formatTimeAgo(new Date(toValue(time)), options, toValue(now)));\n  if (exposeControls) {\n    return {\n      timeAgo,\n      ...controls\n    };\n  } else {\n    return timeAgo;\n  }\n}\nfunction formatTimeAgo(from, options = {}, now = Date.now()) {\n  var _a;\n  const {\n    max,\n    messages = DEFAULT_MESSAGES,\n    fullDateFormatter = DEFAULT_FORMATTER,\n    units = DEFAULT_UNITS,\n    showSecond = false,\n    rounding = \"round\"\n  } = options;\n  const roundFn = typeof rounding === \"number\" ? (n) => +n.toFixed(rounding) : Math[rounding];\n  const diff = +now - +from;\n  const absDiff = Math.abs(diff);\n  function getValue(diff2, unit) {\n    return roundFn(Math.abs(diff2) / unit.value);\n  }\n  function format(diff2, unit) {\n    const val = getValue(diff2, unit);\n    const past = diff2 > 0;\n    const str = applyFormat(unit.name, val, past);\n    return applyFormat(past ? \"past\" : \"future\", str, past);\n  }\n  function applyFormat(name, val, isPast) {\n    const formatter = messages[name];\n    if (typeof formatter === \"function\")\n      return formatter(val, isPast);\n    return formatter.replace(\"{0}\", val.toString());\n  }\n  if (absDiff < 6e4 && !showSecond)\n    return messages.justNow;\n  if (typeof max === \"number\" && absDiff > max)\n    return fullDateFormatter(new Date(from));\n  if (typeof max === \"string\") {\n    const unitMax = (_a = units.find((i) => i.name === max)) == null ? void 0 : _a.max;\n    if (unitMax && absDiff > unitMax)\n      return fullDateFormatter(new Date(from));\n  }\n  for (const [idx, unit] of units.entries()) {\n    const val = getValue(diff, unit);\n    if (val <= 0 && units[idx - 1])\n      return format(diff, units[idx - 1]);\n    if (absDiff < unit.max)\n      return format(diff, unit);\n  }\n  return messages.invalid;\n}\n\nfunction useTimeoutPoll(fn, interval, options = {}) {\n  const {\n    immediate = true,\n    immediateCallback = false\n  } = options;\n  const { start } = useTimeoutFn(loop, interval, { immediate });\n  const isActive = shallowRef(false);\n  async function loop() {\n    if (!isActive.value)\n      return;\n    await fn();\n    start();\n  }\n  function resume() {\n    if (!isActive.value) {\n      isActive.value = true;\n      if (immediateCallback)\n        fn();\n      start();\n    }\n  }\n  function pause() {\n    isActive.value = false;\n  }\n  if (immediate && isClient)\n    resume();\n  tryOnScopeDispose(pause);\n  return {\n    isActive,\n    pause,\n    resume\n  };\n}\n\nfunction useTimestamp(options = {}) {\n  const {\n    controls: exposeControls = false,\n    offset = 0,\n    immediate = true,\n    interval = \"requestAnimationFrame\",\n    callback\n  } = options;\n  const ts = shallowRef(timestamp() + offset);\n  const update = () => ts.value = timestamp() + offset;\n  const cb = callback ? () => {\n    update();\n    callback(ts.value);\n  } : update;\n  const controls = interval === \"requestAnimationFrame\" ? useRafFn(cb, { immediate }) : useIntervalFn(cb, interval, { immediate });\n  if (exposeControls) {\n    return {\n      timestamp: ts,\n      ...controls\n    };\n  } else {\n    return ts;\n  }\n}\n\nfunction useTitle(newTitle = null, options = {}) {\n  var _a, _b, _c;\n  const {\n    document = defaultDocument,\n    restoreOnUnmount = (t) => t\n  } = options;\n  const originalTitle = (_a = document == null ? void 0 : document.title) != null ? _a : \"\";\n  const title = toRef((_b = newTitle != null ? newTitle : document == null ? void 0 : document.title) != null ? _b : null);\n  const isReadonly = !!(newTitle && typeof newTitle === \"function\");\n  function format(t) {\n    if (!(\"titleTemplate\" in options))\n      return t;\n    const template = options.titleTemplate || \"%s\";\n    return typeof template === \"function\" ? template(t) : toValue(template).replace(/%s/g, t);\n  }\n  watch(\n    title,\n    (newValue, oldValue) => {\n      if (newValue !== oldValue && document)\n        document.title = format(newValue != null ? newValue : \"\");\n    },\n    { immediate: true }\n  );\n  if (options.observe && !options.titleTemplate && document && !isReadonly) {\n    useMutationObserver(\n      (_c = document.head) == null ? void 0 : _c.querySelector(\"title\"),\n      () => {\n        if (document && document.title !== title.value)\n          title.value = format(document.title);\n      },\n      { childList: true }\n    );\n  }\n  tryOnScopeDispose(() => {\n    if (restoreOnUnmount) {\n      const restoredTitle = restoreOnUnmount(originalTitle, title.value || \"\");\n      if (restoredTitle != null && document)\n        document.title = restoredTitle;\n    }\n  });\n  return title;\n}\n\nconst _TransitionPresets = {\n  easeInSine: [0.12, 0, 0.39, 0],\n  easeOutSine: [0.61, 1, 0.88, 1],\n  easeInOutSine: [0.37, 0, 0.63, 1],\n  easeInQuad: [0.11, 0, 0.5, 0],\n  easeOutQuad: [0.5, 1, 0.89, 1],\n  easeInOutQuad: [0.45, 0, 0.55, 1],\n  easeInCubic: [0.32, 0, 0.67, 0],\n  easeOutCubic: [0.33, 1, 0.68, 1],\n  easeInOutCubic: [0.65, 0, 0.35, 1],\n  easeInQuart: [0.5, 0, 0.75, 0],\n  easeOutQuart: [0.25, 1, 0.5, 1],\n  easeInOutQuart: [0.76, 0, 0.24, 1],\n  easeInQuint: [0.64, 0, 0.78, 0],\n  easeOutQuint: [0.22, 1, 0.36, 1],\n  easeInOutQuint: [0.83, 0, 0.17, 1],\n  easeInExpo: [0.7, 0, 0.84, 0],\n  easeOutExpo: [0.16, 1, 0.3, 1],\n  easeInOutExpo: [0.87, 0, 0.13, 1],\n  easeInCirc: [0.55, 0, 1, 0.45],\n  easeOutCirc: [0, 0.55, 0.45, 1],\n  easeInOutCirc: [0.85, 0, 0.15, 1],\n  easeInBack: [0.36, 0, 0.66, -0.56],\n  easeOutBack: [0.34, 1.56, 0.64, 1],\n  easeInOutBack: [0.68, -0.6, 0.32, 1.6]\n};\nconst TransitionPresets = /* @__PURE__ */ Object.assign({}, { linear: identity }, _TransitionPresets);\nfunction createEasingFunction([p0, p1, p2, p3]) {\n  const a = (a1, a2) => 1 - 3 * a2 + 3 * a1;\n  const b = (a1, a2) => 3 * a2 - 6 * a1;\n  const c = (a1) => 3 * a1;\n  const calcBezier = (t, a1, a2) => ((a(a1, a2) * t + b(a1, a2)) * t + c(a1)) * t;\n  const getSlope = (t, a1, a2) => 3 * a(a1, a2) * t * t + 2 * b(a1, a2) * t + c(a1);\n  const getTforX = (x) => {\n    let aGuessT = x;\n    for (let i = 0; i < 4; ++i) {\n      const currentSlope = getSlope(aGuessT, p0, p2);\n      if (currentSlope === 0)\n        return aGuessT;\n      const currentX = calcBezier(aGuessT, p0, p2) - x;\n      aGuessT -= currentX / currentSlope;\n    }\n    return aGuessT;\n  };\n  return (x) => p0 === p1 && p2 === p3 ? x : calcBezier(getTforX(x), p1, p3);\n}\nfunction lerp(a, b, alpha) {\n  return a + alpha * (b - a);\n}\nfunction toVec(t) {\n  return (typeof t === \"number\" ? [t] : t) || [];\n}\nfunction executeTransition(source, from, to, options = {}) {\n  var _a, _b;\n  const fromVal = toValue(from);\n  const toVal = toValue(to);\n  const v1 = toVec(fromVal);\n  const v2 = toVec(toVal);\n  const duration = (_a = toValue(options.duration)) != null ? _a : 1e3;\n  const startedAt = Date.now();\n  const endAt = Date.now() + duration;\n  const trans = typeof options.transition === \"function\" ? options.transition : (_b = toValue(options.transition)) != null ? _b : identity;\n  const ease = typeof trans === \"function\" ? trans : createEasingFunction(trans);\n  return new Promise((resolve) => {\n    source.value = fromVal;\n    const tick = () => {\n      var _a2;\n      if ((_a2 = options.abort) == null ? void 0 : _a2.call(options)) {\n        resolve();\n        return;\n      }\n      const now = Date.now();\n      const alpha = ease((now - startedAt) / duration);\n      const arr = toVec(source.value).map((n, i) => lerp(v1[i], v2[i], alpha));\n      if (Array.isArray(source.value))\n        source.value = arr.map((n, i) => {\n          var _a3, _b2;\n          return lerp((_a3 = v1[i]) != null ? _a3 : 0, (_b2 = v2[i]) != null ? _b2 : 0, alpha);\n        });\n      else if (typeof source.value === \"number\")\n        source.value = arr[0];\n      if (now < endAt) {\n        requestAnimationFrame(tick);\n      } else {\n        source.value = toVal;\n        resolve();\n      }\n    };\n    tick();\n  });\n}\nfunction useTransition(source, options = {}) {\n  let currentId = 0;\n  const sourceVal = () => {\n    const v = toValue(source);\n    return typeof v === \"number\" ? v : v.map(toValue);\n  };\n  const outputRef = ref(sourceVal());\n  watch(sourceVal, async (to) => {\n    var _a, _b;\n    if (toValue(options.disabled))\n      return;\n    const id = ++currentId;\n    if (options.delay)\n      await promiseTimeout(toValue(options.delay));\n    if (id !== currentId)\n      return;\n    const toVal = Array.isArray(to) ? to.map(toValue) : toValue(to);\n    (_a = options.onStarted) == null ? void 0 : _a.call(options);\n    await executeTransition(outputRef, outputRef.value, toVal, {\n      ...options,\n      abort: () => {\n        var _a2;\n        return id !== currentId || ((_a2 = options.abort) == null ? void 0 : _a2.call(options));\n      }\n    });\n    (_b = options.onFinished) == null ? void 0 : _b.call(options);\n  }, { deep: true });\n  watch(() => toValue(options.disabled), (disabled) => {\n    if (disabled) {\n      currentId++;\n      outputRef.value = sourceVal();\n    }\n  });\n  tryOnScopeDispose(() => {\n    currentId++;\n  });\n  return computed(() => toValue(options.disabled) ? sourceVal() : outputRef.value);\n}\n\nfunction useUrlSearchParams(mode = \"history\", options = {}) {\n  const {\n    initialValue = {},\n    removeNullishValues = true,\n    removeFalsyValues = false,\n    write: enableWrite = true,\n    writeMode = \"replace\",\n    window = defaultWindow\n  } = options;\n  if (!window)\n    return reactive(initialValue);\n  const state = reactive({});\n  function getRawParams() {\n    if (mode === \"history\") {\n      return window.location.search || \"\";\n    } else if (mode === \"hash\") {\n      const hash = window.location.hash || \"\";\n      const index = hash.indexOf(\"?\");\n      return index > 0 ? hash.slice(index) : \"\";\n    } else {\n      return (window.location.hash || \"\").replace(/^#/, \"\");\n    }\n  }\n  function constructQuery(params) {\n    const stringified = params.toString();\n    if (mode === \"history\")\n      return `${stringified ? `?${stringified}` : \"\"}${window.location.hash || \"\"}`;\n    if (mode === \"hash-params\")\n      return `${window.location.search || \"\"}${stringified ? `#${stringified}` : \"\"}`;\n    const hash = window.location.hash || \"#\";\n    const index = hash.indexOf(\"?\");\n    if (index > 0)\n      return `${window.location.search || \"\"}${hash.slice(0, index)}${stringified ? `?${stringified}` : \"\"}`;\n    return `${window.location.search || \"\"}${hash}${stringified ? `?${stringified}` : \"\"}`;\n  }\n  function read() {\n    return new URLSearchParams(getRawParams());\n  }\n  function updateState(params) {\n    const unusedKeys = new Set(Object.keys(state));\n    for (const key of params.keys()) {\n      const paramsForKey = params.getAll(key);\n      state[key] = paramsForKey.length > 1 ? paramsForKey : params.get(key) || \"\";\n      unusedKeys.delete(key);\n    }\n    Array.from(unusedKeys).forEach((key) => delete state[key]);\n  }\n  const { pause, resume } = pausableWatch(\n    state,\n    () => {\n      const params = new URLSearchParams(\"\");\n      Object.keys(state).forEach((key) => {\n        const mapEntry = state[key];\n        if (Array.isArray(mapEntry))\n          mapEntry.forEach((value) => params.append(key, value));\n        else if (removeNullishValues && mapEntry == null)\n          params.delete(key);\n        else if (removeFalsyValues && !mapEntry)\n          params.delete(key);\n        else\n          params.set(key, mapEntry);\n      });\n      write(params, false);\n    },\n    { deep: true }\n  );\n  function write(params, shouldUpdate) {\n    pause();\n    if (shouldUpdate)\n      updateState(params);\n    if (writeMode === \"replace\") {\n      window.history.replaceState(\n        window.history.state,\n        window.document.title,\n        window.location.pathname + constructQuery(params)\n      );\n    } else {\n      window.history.pushState(\n        window.history.state,\n        window.document.title,\n        window.location.pathname + constructQuery(params)\n      );\n    }\n    resume();\n  }\n  function onChanged() {\n    if (!enableWrite)\n      return;\n    write(read(), true);\n  }\n  const listenerOptions = { passive: true };\n  useEventListener(window, \"popstate\", onChanged, listenerOptions);\n  if (mode !== \"history\")\n    useEventListener(window, \"hashchange\", onChanged, listenerOptions);\n  const initial = read();\n  if (initial.keys().next().value)\n    updateState(initial);\n  else\n    Object.assign(state, initialValue);\n  return state;\n}\n\nfunction useUserMedia(options = {}) {\n  var _a, _b;\n  const enabled = shallowRef((_a = options.enabled) != null ? _a : false);\n  const autoSwitch = shallowRef((_b = options.autoSwitch) != null ? _b : true);\n  const constraints = ref(options.constraints);\n  const { navigator = defaultNavigator } = options;\n  const isSupported = useSupported(() => {\n    var _a2;\n    return (_a2 = navigator == null ? void 0 : navigator.mediaDevices) == null ? void 0 : _a2.getUserMedia;\n  });\n  const stream = shallowRef();\n  function getDeviceOptions(type) {\n    switch (type) {\n      case \"video\": {\n        if (constraints.value)\n          return constraints.value.video || false;\n        break;\n      }\n      case \"audio\": {\n        if (constraints.value)\n          return constraints.value.audio || false;\n        break;\n      }\n    }\n  }\n  async function _start() {\n    if (!isSupported.value || stream.value)\n      return;\n    stream.value = await navigator.mediaDevices.getUserMedia({\n      video: getDeviceOptions(\"video\"),\n      audio: getDeviceOptions(\"audio\")\n    });\n    return stream.value;\n  }\n  function _stop() {\n    var _a2;\n    (_a2 = stream.value) == null ? void 0 : _a2.getTracks().forEach((t) => t.stop());\n    stream.value = void 0;\n  }\n  function stop() {\n    _stop();\n    enabled.value = false;\n  }\n  async function start() {\n    await _start();\n    if (stream.value)\n      enabled.value = true;\n    return stream.value;\n  }\n  async function restart() {\n    _stop();\n    return await start();\n  }\n  watch(\n    enabled,\n    (v) => {\n      if (v)\n        _start();\n      else _stop();\n    },\n    { immediate: true }\n  );\n  watch(\n    constraints,\n    () => {\n      if (autoSwitch.value && stream.value)\n        restart();\n    },\n    { immediate: true }\n  );\n  tryOnScopeDispose(() => {\n    stop();\n  });\n  return {\n    isSupported,\n    stream,\n    start,\n    stop,\n    restart,\n    constraints,\n    enabled,\n    autoSwitch\n  };\n}\n\nfunction useVModel(props, key, emit, options = {}) {\n  var _a, _b, _c;\n  const {\n    clone = false,\n    passive = false,\n    eventName,\n    deep = false,\n    defaultValue,\n    shouldEmit\n  } = options;\n  const vm = getCurrentInstance();\n  const _emit = emit || (vm == null ? void 0 : vm.emit) || ((_a = vm == null ? void 0 : vm.$emit) == null ? void 0 : _a.bind(vm)) || ((_c = (_b = vm == null ? void 0 : vm.proxy) == null ? void 0 : _b.$emit) == null ? void 0 : _c.bind(vm == null ? void 0 : vm.proxy));\n  let event = eventName;\n  if (!key) {\n    key = \"modelValue\";\n  }\n  event = event || `update:${key.toString()}`;\n  const cloneFn = (val) => !clone ? val : typeof clone === \"function\" ? clone(val) : cloneFnJSON(val);\n  const getValue = () => isDef(props[key]) ? cloneFn(props[key]) : defaultValue;\n  const triggerEmit = (value) => {\n    if (shouldEmit) {\n      if (shouldEmit(value))\n        _emit(event, value);\n    } else {\n      _emit(event, value);\n    }\n  };\n  if (passive) {\n    const initialValue = getValue();\n    const proxy = ref(initialValue);\n    let isUpdating = false;\n    watch(\n      () => props[key],\n      (v) => {\n        if (!isUpdating) {\n          isUpdating = true;\n          proxy.value = cloneFn(v);\n          nextTick(() => isUpdating = false);\n        }\n      }\n    );\n    watch(\n      proxy,\n      (v) => {\n        if (!isUpdating && (v !== props[key] || deep))\n          triggerEmit(v);\n      },\n      { deep }\n    );\n    return proxy;\n  } else {\n    return computed({\n      get() {\n        return getValue();\n      },\n      set(value) {\n        triggerEmit(value);\n      }\n    });\n  }\n}\n\nfunction useVModels(props, emit, options = {}) {\n  const ret = {};\n  for (const key in props) {\n    ret[key] = useVModel(\n      props,\n      key,\n      emit,\n      options\n    );\n  }\n  return ret;\n}\n\nfunction useVibrate(options) {\n  const {\n    pattern = [],\n    interval = 0,\n    navigator = defaultNavigator\n  } = options || {};\n  const isSupported = useSupported(() => typeof navigator !== \"undefined\" && \"vibrate\" in navigator);\n  const patternRef = toRef(pattern);\n  let intervalControls;\n  const vibrate = (pattern2 = patternRef.value) => {\n    if (isSupported.value)\n      navigator.vibrate(pattern2);\n  };\n  const stop = () => {\n    if (isSupported.value)\n      navigator.vibrate(0);\n    intervalControls == null ? void 0 : intervalControls.pause();\n  };\n  if (interval > 0) {\n    intervalControls = useIntervalFn(\n      vibrate,\n      interval,\n      {\n        immediate: false,\n        immediateCallback: false\n      }\n    );\n  }\n  return {\n    isSupported,\n    pattern,\n    intervalControls,\n    vibrate,\n    stop\n  };\n}\n\nfunction useVirtualList(list, options) {\n  const { containerStyle, wrapperProps, scrollTo, calculateRange, currentList, containerRef } = \"itemHeight\" in options ? useVerticalVirtualList(options, list) : useHorizontalVirtualList(options, list);\n  return {\n    list: currentList,\n    scrollTo,\n    containerProps: {\n      ref: containerRef,\n      onScroll: () => {\n        calculateRange();\n      },\n      style: containerStyle\n    },\n    wrapperProps\n  };\n}\nfunction useVirtualListResources(list) {\n  const containerRef = shallowRef(null);\n  const size = useElementSize(containerRef);\n  const currentList = ref([]);\n  const source = shallowRef(list);\n  const state = ref({ start: 0, end: 10 });\n  return { state, source, currentList, size, containerRef };\n}\nfunction createGetViewCapacity(state, source, itemSize) {\n  return (containerSize) => {\n    if (typeof itemSize === \"number\")\n      return Math.ceil(containerSize / itemSize);\n    const { start = 0 } = state.value;\n    let sum = 0;\n    let capacity = 0;\n    for (let i = start; i < source.value.length; i++) {\n      const size = itemSize(i);\n      sum += size;\n      capacity = i;\n      if (sum > containerSize)\n        break;\n    }\n    return capacity - start;\n  };\n}\nfunction createGetOffset(source, itemSize) {\n  return (scrollDirection) => {\n    if (typeof itemSize === \"number\")\n      return Math.floor(scrollDirection / itemSize) + 1;\n    let sum = 0;\n    let offset = 0;\n    for (let i = 0; i < source.value.length; i++) {\n      const size = itemSize(i);\n      sum += size;\n      if (sum >= scrollDirection) {\n        offset = i;\n        break;\n      }\n    }\n    return offset + 1;\n  };\n}\nfunction createCalculateRange(type, overscan, getOffset, getViewCapacity, { containerRef, state, currentList, source }) {\n  return () => {\n    const element = containerRef.value;\n    if (element) {\n      const offset = getOffset(type === \"vertical\" ? element.scrollTop : element.scrollLeft);\n      const viewCapacity = getViewCapacity(type === \"vertical\" ? element.clientHeight : element.clientWidth);\n      const from = offset - overscan;\n      const to = offset + viewCapacity + overscan;\n      state.value = {\n        start: from < 0 ? 0 : from,\n        end: to > source.value.length ? source.value.length : to\n      };\n      currentList.value = source.value.slice(state.value.start, state.value.end).map((ele, index) => ({\n        data: ele,\n        index: index + state.value.start\n      }));\n    }\n  };\n}\nfunction createGetDistance(itemSize, source) {\n  return (index) => {\n    if (typeof itemSize === \"number\") {\n      const size2 = index * itemSize;\n      return size2;\n    }\n    const size = source.value.slice(0, index).reduce((sum, _, i) => sum + itemSize(i), 0);\n    return size;\n  };\n}\nfunction useWatchForSizes(size, list, containerRef, calculateRange) {\n  watch([size.width, size.height, list, containerRef], () => {\n    calculateRange();\n  });\n}\nfunction createComputedTotalSize(itemSize, source) {\n  return computed(() => {\n    if (typeof itemSize === \"number\")\n      return source.value.length * itemSize;\n    return source.value.reduce((sum, _, index) => sum + itemSize(index), 0);\n  });\n}\nconst scrollToDictionaryForElementScrollKey = {\n  horizontal: \"scrollLeft\",\n  vertical: \"scrollTop\"\n};\nfunction createScrollTo(type, calculateRange, getDistance, containerRef) {\n  return (index) => {\n    if (containerRef.value) {\n      containerRef.value[scrollToDictionaryForElementScrollKey[type]] = getDistance(index);\n      calculateRange();\n    }\n  };\n}\nfunction useHorizontalVirtualList(options, list) {\n  const resources = useVirtualListResources(list);\n  const { state, source, currentList, size, containerRef } = resources;\n  const containerStyle = { overflowX: \"auto\" };\n  const { itemWidth, overscan = 5 } = options;\n  const getViewCapacity = createGetViewCapacity(state, source, itemWidth);\n  const getOffset = createGetOffset(source, itemWidth);\n  const calculateRange = createCalculateRange(\"horizontal\", overscan, getOffset, getViewCapacity, resources);\n  const getDistanceLeft = createGetDistance(itemWidth, source);\n  const offsetLeft = computed(() => getDistanceLeft(state.value.start));\n  const totalWidth = createComputedTotalSize(itemWidth, source);\n  useWatchForSizes(size, list, containerRef, calculateRange);\n  const scrollTo = createScrollTo(\"horizontal\", calculateRange, getDistanceLeft, containerRef);\n  const wrapperProps = computed(() => {\n    return {\n      style: {\n        height: \"100%\",\n        width: `${totalWidth.value - offsetLeft.value}px`,\n        marginLeft: `${offsetLeft.value}px`,\n        display: \"flex\"\n      }\n    };\n  });\n  return {\n    scrollTo,\n    calculateRange,\n    wrapperProps,\n    containerStyle,\n    currentList,\n    containerRef\n  };\n}\nfunction useVerticalVirtualList(options, list) {\n  const resources = useVirtualListResources(list);\n  const { state, source, currentList, size, containerRef } = resources;\n  const containerStyle = { overflowY: \"auto\" };\n  const { itemHeight, overscan = 5 } = options;\n  const getViewCapacity = createGetViewCapacity(state, source, itemHeight);\n  const getOffset = createGetOffset(source, itemHeight);\n  const calculateRange = createCalculateRange(\"vertical\", overscan, getOffset, getViewCapacity, resources);\n  const getDistanceTop = createGetDistance(itemHeight, source);\n  const offsetTop = computed(() => getDistanceTop(state.value.start));\n  const totalHeight = createComputedTotalSize(itemHeight, source);\n  useWatchForSizes(size, list, containerRef, calculateRange);\n  const scrollTo = createScrollTo(\"vertical\", calculateRange, getDistanceTop, containerRef);\n  const wrapperProps = computed(() => {\n    return {\n      style: {\n        width: \"100%\",\n        height: `${totalHeight.value - offsetTop.value}px`,\n        marginTop: `${offsetTop.value}px`\n      }\n    };\n  });\n  return {\n    calculateRange,\n    scrollTo,\n    containerStyle,\n    wrapperProps,\n    currentList,\n    containerRef\n  };\n}\n\nfunction useWakeLock(options = {}) {\n  const {\n    navigator = defaultNavigator,\n    document = defaultDocument\n  } = options;\n  const requestedType = shallowRef(false);\n  const sentinel = shallowRef(null);\n  const documentVisibility = useDocumentVisibility({ document });\n  const isSupported = useSupported(() => navigator && \"wakeLock\" in navigator);\n  const isActive = computed(() => !!sentinel.value && documentVisibility.value === \"visible\");\n  if (isSupported.value) {\n    useEventListener(sentinel, \"release\", () => {\n      var _a, _b;\n      requestedType.value = (_b = (_a = sentinel.value) == null ? void 0 : _a.type) != null ? _b : false;\n    }, { passive: true });\n    whenever(\n      () => documentVisibility.value === \"visible\" && (document == null ? void 0 : document.visibilityState) === \"visible\" && requestedType.value,\n      (type) => {\n        requestedType.value = false;\n        forceRequest(type);\n      }\n    );\n  }\n  async function forceRequest(type) {\n    var _a;\n    await ((_a = sentinel.value) == null ? void 0 : _a.release());\n    sentinel.value = isSupported.value ? await navigator.wakeLock.request(type) : null;\n  }\n  async function request(type) {\n    if (documentVisibility.value === \"visible\")\n      await forceRequest(type);\n    else\n      requestedType.value = type;\n  }\n  async function release() {\n    requestedType.value = false;\n    const s = sentinel.value;\n    sentinel.value = null;\n    await (s == null ? void 0 : s.release());\n  }\n  return {\n    sentinel,\n    isSupported,\n    isActive,\n    request,\n    forceRequest,\n    release\n  };\n}\n\nfunction useWebNotification(options = {}) {\n  const {\n    window = defaultWindow,\n    requestPermissions: _requestForPermissions = true\n  } = options;\n  const defaultWebNotificationOptions = options;\n  const isSupported = useSupported(() => {\n    if (!window || !(\"Notification\" in window))\n      return false;\n    if (Notification.permission === \"granted\")\n      return true;\n    try {\n      const notification2 = new Notification(\"\");\n      notification2.onshow = () => {\n        notification2.close();\n      };\n    } catch (e) {\n      if (e.name === \"TypeError\")\n        return false;\n    }\n    return true;\n  });\n  const permissionGranted = shallowRef(isSupported.value && \"permission\" in Notification && Notification.permission === \"granted\");\n  const notification = ref(null);\n  const ensurePermissions = async () => {\n    if (!isSupported.value)\n      return;\n    if (!permissionGranted.value && Notification.permission !== \"denied\") {\n      const result = await Notification.requestPermission();\n      if (result === \"granted\")\n        permissionGranted.value = true;\n    }\n    return permissionGranted.value;\n  };\n  const { on: onClick, trigger: clickTrigger } = createEventHook();\n  const { on: onShow, trigger: showTrigger } = createEventHook();\n  const { on: onError, trigger: errorTrigger } = createEventHook();\n  const { on: onClose, trigger: closeTrigger } = createEventHook();\n  const show = async (overrides) => {\n    if (!isSupported.value || !permissionGranted.value)\n      return;\n    const options2 = Object.assign({}, defaultWebNotificationOptions, overrides);\n    notification.value = new Notification(options2.title || \"\", options2);\n    notification.value.onclick = clickTrigger;\n    notification.value.onshow = showTrigger;\n    notification.value.onerror = errorTrigger;\n    notification.value.onclose = closeTrigger;\n    return notification.value;\n  };\n  const close = () => {\n    if (notification.value)\n      notification.value.close();\n    notification.value = null;\n  };\n  if (_requestForPermissions)\n    tryOnMounted(ensurePermissions);\n  tryOnScopeDispose(close);\n  if (isSupported.value && window) {\n    const document = window.document;\n    useEventListener(document, \"visibilitychange\", (e) => {\n      e.preventDefault();\n      if (document.visibilityState === \"visible\") {\n        close();\n      }\n    });\n  }\n  return {\n    isSupported,\n    notification,\n    ensurePermissions,\n    permissionGranted,\n    show,\n    close,\n    onClick,\n    onShow,\n    onError,\n    onClose\n  };\n}\n\nconst DEFAULT_PING_MESSAGE = \"ping\";\nfunction resolveNestedOptions(options) {\n  if (options === true)\n    return {};\n  return options;\n}\nfunction useWebSocket(url, options = {}) {\n  const {\n    onConnected,\n    onDisconnected,\n    onError,\n    onMessage,\n    immediate = true,\n    autoConnect = true,\n    autoClose = true,\n    protocols = []\n  } = options;\n  const data = ref(null);\n  const status = shallowRef(\"CLOSED\");\n  const wsRef = ref();\n  const urlRef = toRef(url);\n  let heartbeatPause;\n  let heartbeatResume;\n  let explicitlyClosed = false;\n  let retried = 0;\n  let bufferedData = [];\n  let retryTimeout;\n  let pongTimeoutWait;\n  const _sendBuffer = () => {\n    if (bufferedData.length && wsRef.value && status.value === \"OPEN\") {\n      for (const buffer of bufferedData)\n        wsRef.value.send(buffer);\n      bufferedData = [];\n    }\n  };\n  const resetRetry = () => {\n    if (retryTimeout != null) {\n      clearTimeout(retryTimeout);\n      retryTimeout = void 0;\n    }\n  };\n  const resetHeartbeat = () => {\n    clearTimeout(pongTimeoutWait);\n    pongTimeoutWait = void 0;\n  };\n  const close = (code = 1e3, reason) => {\n    resetRetry();\n    if (!isClient && !isWorker || !wsRef.value)\n      return;\n    explicitlyClosed = true;\n    resetHeartbeat();\n    heartbeatPause == null ? void 0 : heartbeatPause();\n    wsRef.value.close(code, reason);\n    wsRef.value = void 0;\n  };\n  const send = (data2, useBuffer = true) => {\n    if (!wsRef.value || status.value !== \"OPEN\") {\n      if (useBuffer)\n        bufferedData.push(data2);\n      return false;\n    }\n    _sendBuffer();\n    wsRef.value.send(data2);\n    return true;\n  };\n  const _init = () => {\n    if (explicitlyClosed || typeof urlRef.value === \"undefined\")\n      return;\n    const ws = new WebSocket(urlRef.value, protocols);\n    wsRef.value = ws;\n    status.value = \"CONNECTING\";\n    ws.onopen = () => {\n      status.value = \"OPEN\";\n      retried = 0;\n      onConnected == null ? void 0 : onConnected(ws);\n      heartbeatResume == null ? void 0 : heartbeatResume();\n      _sendBuffer();\n    };\n    ws.onclose = (ev) => {\n      status.value = \"CLOSED\";\n      resetHeartbeat();\n      heartbeatPause == null ? void 0 : heartbeatPause();\n      onDisconnected == null ? void 0 : onDisconnected(ws, ev);\n      if (!explicitlyClosed && options.autoReconnect && (wsRef.value == null || ws === wsRef.value)) {\n        const {\n          retries = -1,\n          delay = 1e3,\n          onFailed\n        } = resolveNestedOptions(options.autoReconnect);\n        const checkRetires = typeof retries === \"function\" ? retries : () => typeof retries === \"number\" && (retries < 0 || retried < retries);\n        if (checkRetires(retried)) {\n          retried += 1;\n          retryTimeout = setTimeout(_init, delay);\n        } else {\n          onFailed == null ? void 0 : onFailed();\n        }\n      }\n    };\n    ws.onerror = (e) => {\n      onError == null ? void 0 : onError(ws, e);\n    };\n    ws.onmessage = (e) => {\n      if (options.heartbeat) {\n        resetHeartbeat();\n        const {\n          message = DEFAULT_PING_MESSAGE,\n          responseMessage = message\n        } = resolveNestedOptions(options.heartbeat);\n        if (e.data === toValue(responseMessage))\n          return;\n      }\n      data.value = e.data;\n      onMessage == null ? void 0 : onMessage(ws, e);\n    };\n  };\n  if (options.heartbeat) {\n    const {\n      message = DEFAULT_PING_MESSAGE,\n      interval = 1e3,\n      pongTimeout = 1e3\n    } = resolveNestedOptions(options.heartbeat);\n    const { pause, resume } = useIntervalFn(\n      () => {\n        send(toValue(message), false);\n        if (pongTimeoutWait != null)\n          return;\n        pongTimeoutWait = setTimeout(() => {\n          close();\n          explicitlyClosed = false;\n        }, pongTimeout);\n      },\n      interval,\n      { immediate: false }\n    );\n    heartbeatPause = pause;\n    heartbeatResume = resume;\n  }\n  if (autoClose) {\n    if (isClient)\n      useEventListener(\"beforeunload\", () => close(), { passive: true });\n    tryOnScopeDispose(close);\n  }\n  const open = () => {\n    if (!isClient && !isWorker)\n      return;\n    close();\n    explicitlyClosed = false;\n    retried = 0;\n    _init();\n  };\n  if (immediate)\n    open();\n  if (autoConnect)\n    watch(urlRef, open);\n  return {\n    data,\n    status,\n    close,\n    send,\n    open,\n    ws: wsRef\n  };\n}\n\nfunction useWebWorker(arg0, workerOptions, options) {\n  const {\n    window = defaultWindow\n  } = options != null ? options : {};\n  const data = ref(null);\n  const worker = shallowRef();\n  const post = (...args) => {\n    if (!worker.value)\n      return;\n    worker.value.postMessage(...args);\n  };\n  const terminate = function terminate2() {\n    if (!worker.value)\n      return;\n    worker.value.terminate();\n  };\n  if (window) {\n    if (typeof arg0 === \"string\")\n      worker.value = new Worker(arg0, workerOptions);\n    else if (typeof arg0 === \"function\")\n      worker.value = arg0();\n    else\n      worker.value = arg0;\n    worker.value.onmessage = (e) => {\n      data.value = e.data;\n    };\n    tryOnScopeDispose(() => {\n      if (worker.value)\n        worker.value.terminate();\n    });\n  }\n  return {\n    data,\n    post,\n    terminate,\n    worker\n  };\n}\n\nfunction depsParser(deps, localDeps) {\n  if (deps.length === 0 && localDeps.length === 0)\n    return \"\";\n  const depsString = deps.map((dep) => `'${dep}'`).toString();\n  const depsFunctionString = localDeps.filter((dep) => typeof dep === \"function\").map((fn) => {\n    const str = fn.toString();\n    if (str.trim().startsWith(\"function\")) {\n      return str;\n    } else {\n      const name = fn.name;\n      return `const ${name} = ${str}`;\n    }\n  }).join(\";\");\n  const importString = `importScripts(${depsString});`;\n  return `${depsString.trim() === \"\" ? \"\" : importString} ${depsFunctionString}`;\n}\n\nfunction jobRunner(userFunc) {\n  return (e) => {\n    const userFuncArgs = e.data[0];\n    return Promise.resolve(userFunc.apply(void 0, userFuncArgs)).then((result) => {\n      postMessage([\"SUCCESS\", result]);\n    }).catch((error) => {\n      postMessage([\"ERROR\", error]);\n    });\n  };\n}\n\nfunction createWorkerBlobUrl(fn, deps, localDeps) {\n  const blobCode = `${depsParser(deps, localDeps)}; onmessage=(${jobRunner})(${fn})`;\n  const blob = new Blob([blobCode], { type: \"text/javascript\" });\n  const url = URL.createObjectURL(blob);\n  return url;\n}\n\nfunction useWebWorkerFn(fn, options = {}) {\n  const {\n    dependencies = [],\n    localDependencies = [],\n    timeout,\n    window = defaultWindow\n  } = options;\n  const worker = ref();\n  const workerStatus = shallowRef(\"PENDING\");\n  const promise = ref({});\n  const timeoutId = shallowRef();\n  const workerTerminate = (status = \"PENDING\") => {\n    if (worker.value && worker.value._url && window) {\n      worker.value.terminate();\n      URL.revokeObjectURL(worker.value._url);\n      promise.value = {};\n      worker.value = void 0;\n      window.clearTimeout(timeoutId.value);\n      workerStatus.value = status;\n    }\n  };\n  workerTerminate();\n  tryOnScopeDispose(workerTerminate);\n  const generateWorker = () => {\n    const blobUrl = createWorkerBlobUrl(fn, dependencies, localDependencies);\n    const newWorker = new Worker(blobUrl);\n    newWorker._url = blobUrl;\n    newWorker.onmessage = (e) => {\n      const { resolve = () => {\n      }, reject = () => {\n      } } = promise.value;\n      const [status, result] = e.data;\n      switch (status) {\n        case \"SUCCESS\":\n          resolve(result);\n          workerTerminate(status);\n          break;\n        default:\n          reject(result);\n          workerTerminate(\"ERROR\");\n          break;\n      }\n    };\n    newWorker.onerror = (e) => {\n      const { reject = () => {\n      } } = promise.value;\n      e.preventDefault();\n      reject(e);\n      workerTerminate(\"ERROR\");\n    };\n    if (timeout) {\n      timeoutId.value = setTimeout(\n        () => workerTerminate(\"TIMEOUT_EXPIRED\"),\n        timeout\n      );\n    }\n    return newWorker;\n  };\n  const callWorker = (...fnArgs) => new Promise((resolve, reject) => {\n    var _a;\n    promise.value = {\n      resolve,\n      reject\n    };\n    (_a = worker.value) == null ? void 0 : _a.postMessage([[...fnArgs]]);\n    workerStatus.value = \"RUNNING\";\n  });\n  const workerFn = (...fnArgs) => {\n    if (workerStatus.value === \"RUNNING\") {\n      console.error(\n        \"[useWebWorkerFn] You can only run one instance of the worker at a time.\"\n      );\n      return Promise.reject();\n    }\n    worker.value = generateWorker();\n    return callWorker(...fnArgs);\n  };\n  return {\n    workerFn,\n    workerStatus,\n    workerTerminate\n  };\n}\n\nfunction useWindowFocus(options = {}) {\n  const { window = defaultWindow } = options;\n  if (!window)\n    return shallowRef(false);\n  const focused = shallowRef(window.document.hasFocus());\n  const listenerOptions = { passive: true };\n  useEventListener(window, \"blur\", () => {\n    focused.value = false;\n  }, listenerOptions);\n  useEventListener(window, \"focus\", () => {\n    focused.value = true;\n  }, listenerOptions);\n  return focused;\n}\n\nfunction useWindowScroll(options = {}) {\n  const { window = defaultWindow, ...rest } = options;\n  return useScroll(window, rest);\n}\n\nfunction useWindowSize(options = {}) {\n  const {\n    window = defaultWindow,\n    initialWidth = Number.POSITIVE_INFINITY,\n    initialHeight = Number.POSITIVE_INFINITY,\n    listenOrientation = true,\n    includeScrollbar = true,\n    type = \"inner\"\n  } = options;\n  const width = shallowRef(initialWidth);\n  const height = shallowRef(initialHeight);\n  const update = () => {\n    if (window) {\n      if (type === \"outer\") {\n        width.value = window.outerWidth;\n        height.value = window.outerHeight;\n      } else if (type === \"visual\" && window.visualViewport) {\n        const { width: visualViewportWidth, height: visualViewportHeight, scale } = window.visualViewport;\n        width.value = Math.round(visualViewportWidth * scale);\n        height.value = Math.round(visualViewportHeight * scale);\n      } else if (includeScrollbar) {\n        width.value = window.innerWidth;\n        height.value = window.innerHeight;\n      } else {\n        width.value = window.document.documentElement.clientWidth;\n        height.value = window.document.documentElement.clientHeight;\n      }\n    }\n  };\n  update();\n  tryOnMounted(update);\n  const listenerOptions = { passive: true };\n  useEventListener(\"resize\", update, listenerOptions);\n  if (window && type === \"visual\" && window.visualViewport) {\n    useEventListener(window.visualViewport, \"resize\", update, listenerOptions);\n  }\n  if (listenOrientation) {\n    const matches = useMediaQuery(\"(orientation: portrait)\");\n    watch(matches, () => update());\n  }\n  return { width, height };\n}\n\nexport { DefaultMagicKeysAliasMap, StorageSerializers, TransitionPresets, computedAsync as asyncComputed, breakpointsAntDesign, breakpointsBootstrapV5, breakpointsElement, breakpointsMasterCss, breakpointsPrimeFlex, breakpointsQuasar, breakpointsSematic, breakpointsTailwind, breakpointsVuetify, breakpointsVuetifyV2, breakpointsVuetifyV3, cloneFnJSON, computedAsync, computedInject, createFetch, createReusableTemplate, createTemplatePromise, createUnrefFn, customStorageEventName, defaultDocument, defaultLocation, defaultNavigator, defaultWindow, executeTransition, formatTimeAgo, getSSRHandler, mapGamepadToXbox360Controller, onClickOutside, onElementRemoval, onKeyDown, onKeyPressed, onKeyStroke, onKeyUp, onLongPress, onStartTyping, provideSSRWidth, setSSRHandler, templateRef, unrefElement, useActiveElement, useAnimate, useAsyncQueue, useAsyncState, useBase64, useBattery, useBluetooth, useBreakpoints, useBroadcastChannel, useBrowserLocation, useCached, useClipboard, useClipboardItems, useCloned, useColorMode, useConfirmDialog, useCountdown, useCssVar, useCurrentElement, useCycleList, useDark, useDebouncedRefHistory, useDeviceMotion, useDeviceOrientation, useDevicePixelRatio, useDevicesList, useDisplayMedia, useDocumentVisibility, useDraggable, useDropZone, useElementBounding, useElementByPoint, useElementHover, useElementSize, useElementVisibility, useEventBus, useEventListener, useEventSource, useEyeDropper, useFavicon, useFetch, useFileDialog, useFileSystemAccess, useFocus, useFocusWithin, useFps, useFullscreen, useGamepad, useGeolocation, useIdle, useImage, useInfiniteScroll, useIntersectionObserver, useKeyModifier, useLocalStorage, useMagicKeys, useManualRefHistory, useMediaControls, useMediaQuery, useMemoize, useMemory, useMounted, useMouse, useMouseInElement, useMousePressed, useMutationObserver, useNavigatorLanguage, useNetwork, useNow, useObjectUrl, useOffsetPagination, useOnline, usePageLeave, useParallax, useParentElement, usePerformanceObserver, usePermission, usePointer, usePointerLock, usePointerSwipe, usePreferredColorScheme, usePreferredContrast, usePreferredDark, usePreferredLanguages, usePreferredReducedMotion, usePreferredReducedTransparency, usePrevious, useRafFn, useRefHistory, useResizeObserver, useSSRWidth, useScreenOrientation, useScreenSafeArea, useScriptTag, useScroll, useScrollLock, useSessionStorage, useShare, useSorted, useSpeechRecognition, useSpeechSynthesis, useStepper, useStorage, useStorageAsync, useStyleTag, useSupported, useSwipe, useTemplateRefsList, useTextDirection, useTextSelection, useTextareaAutosize, useThrottledRefHistory, useTimeAgo, useTimeoutPoll, useTimestamp, useTitle, useTransition, useUrlSearchParams, useUserMedia, useVModel, useVModels, useVibrate, useVirtualList, useWakeLock, useWebNotification, useWebSocket, useWebWorker, useWebWorkerFn, useWindowFocus, useWindowScroll, useWindowSize };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,SAAS,cAAc,IAAI,SAAS;AAClC,MAAI;AACJ,QAAM,SAAS,WAAW;AAC1B,cAAY,MAAM;AAChB,WAAO,QAAQ,GAAG;AAAA,EACpB,GAAG;AAAA,IACD,GAAG;AAAA,IACH,QAAQ,KAAK,WAAW,OAAO,SAAS,QAAQ,UAAU,OAAO,KAAK;AAAA,EACxE,CAAC;AACD,SAAO,SAAS,MAAM;AACxB;AAEA,SAAS,oBAAoB,QAAQ,IAAI;AACvC,MAAI,IAAI;AACR,MAAI;AACJ,MAAI;AACJ,QAAM,QAAQ,WAAW,IAAI;AAC7B,QAAM,SAAS,MAAM;AACnB,UAAM,QAAQ;AACd,YAAQ;AAAA,EACV;AACA,QAAM,QAAQ,QAAQ,EAAE,OAAO,OAAO,CAAC;AACvC,QAAMA,OAAM,OAAO,OAAO,aAAa,KAAK,GAAG;AAC/C,QAAMC,OAAM,OAAO,OAAO,aAAa,SAAS,GAAG;AACnD,QAAM,SAAS,UAAU,CAAC,QAAQ,aAAa;AAC7C,YAAQ;AACR,cAAU;AACV,WAAO;AAAA,MACL,MAAM;AACJ,YAAI,MAAM,OAAO;AACf,cAAID,KAAI,CAAC;AACT,gBAAM,QAAQ;AAAA,QAChB;AACA,cAAM;AACN,eAAO;AAAA,MACT;AAAA,MACA,IAAI,IAAI;AACN,QAAAC,QAAO,OAAO,SAASA,KAAI,EAAE;AAAA,MAC/B;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAI,OAAO,aAAa,MAAM;AAC5B,WAAO,UAAU;AACnB,SAAO;AACT;AAEA,SAAS,kBAAkB,IAAI;AAC7B,MAAI,gBAAgB,GAAG;AACrB,mBAAe,EAAE;AACjB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,kBAAkB;AACzB,QAAM,MAAsB,oBAAI,IAAI;AACpC,QAAM,MAAM,CAAC,OAAO;AAClB,QAAI,OAAO,EAAE;AAAA,EACf;AACA,QAAM,QAAQ,MAAM;AAClB,QAAI,MAAM;AAAA,EACZ;AACA,QAAM,KAAK,CAAC,OAAO;AACjB,QAAI,IAAI,EAAE;AACV,UAAM,QAAQ,MAAM,IAAI,EAAE;AAC1B,sBAAkB,KAAK;AACvB,WAAO;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACA,QAAM,UAAU,IAAI,SAAS;AAC3B,WAAO,QAAQ,IAAI,MAAM,KAAK,GAAG,EAAE,IAAI,CAAC,OAAO,GAAG,GAAG,IAAI,CAAC,CAAC;AAAA,EAC7D;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,kBAAkB,cAAc;AACvC,MAAI,cAAc;AAClB,MAAI;AACJ,QAAM,QAAQ,YAAY,IAAI;AAC9B,SAAO,IAAI,SAAS;AAClB,QAAI,CAAC,aAAa;AAChB,cAAQ,MAAM,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC;AAC7C,oBAAc;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AACF;AAEA,IAAM,wBAAwC,oBAAI,QAAQ;AAE1D,IAAM,cAAc,IAAI,SAAS;AAC/B,MAAI;AACJ,QAAM,MAAM,KAAK,CAAC;AAClB,QAAM,YAAY,KAAK,mBAAmB,MAAM,OAAO,SAAS,GAAG;AACnE,MAAI,YAAY,QAAQ,CAAC,oBAAoB;AAC3C,UAAM,IAAI,MAAM,qCAAqC;AACvD,MAAI,YAAY,sBAAsB,IAAI,QAAQ,KAAK,OAAO,sBAAsB,IAAI,QAAQ;AAC9F,WAAO,sBAAsB,IAAI,QAAQ,EAAE,GAAG;AAChD,SAAO,OAAO,GAAG,IAAI;AACvB;AAEA,IAAM,eAAe,CAAC,KAAK,UAAU;AACnC,MAAI;AACJ,QAAM,YAAY,KAAK,mBAAmB,MAAM,OAAO,SAAS,GAAG;AACnE,MAAI,YAAY;AACd,UAAM,IAAI,MAAM,sCAAsC;AACxD,MAAI,CAAC,sBAAsB,IAAI,QAAQ;AACrC,0BAAsB,IAAI,UAA0B,uBAAO,OAAO,IAAI,CAAC;AACzE,QAAM,qBAAqB,sBAAsB,IAAI,QAAQ;AAC7D,qBAAmB,GAAG,IAAI;AAC1B,UAAQ,KAAK,KAAK;AACpB;AAEA,SAAS,qBAAqB,YAAY,SAAS;AACjD,QAAM,OAAO,WAAW,OAAO,SAAS,QAAQ,iBAAiB,OAAO,WAAW,QAAQ,gBAAgB;AAC3G,QAAM,eAAe,WAAW,OAAO,SAAS,QAAQ;AACxD,QAAM,oBAAoB,IAAI,SAAS;AACrC,UAAM,QAAQ,WAAW,GAAG,IAAI;AAChC,iBAAa,KAAK,KAAK;AACvB,WAAO;AAAA,EACT;AACA,QAAM,mBAAmB,MAAM,YAAY,KAAK,YAAY;AAC5D,SAAO,CAAC,mBAAmB,gBAAgB;AAC7C;AAEA,SAAS,UAAU,OAAO,MAAM;AAC9B,MAAI,SAAS,MAAM;AACjB,WAAO,IAAI,KAAK;AAAA,EAClB,OAAO;AACL,WAAO,WAAW,KAAK;AAAA,EACzB;AACF;AAEA,SAAS,uBAAuB,YAAY;AAC1C,MAAI,cAAc;AAClB,MAAI;AACJ,MAAI;AACJ,QAAM,UAAU,MAAM;AACpB,mBAAe;AACf,QAAI,SAAS,eAAe,GAAG;AAC7B,YAAM,KAAK;AACX,cAAQ;AACR,cAAQ;AAAA,IACV;AAAA,EACF;AACA,SAAO,IAAI,SAAS;AAClB,mBAAe;AACf,QAAI,CAAC,OAAO;AACV,cAAQ,YAAY,IAAI;AACxB,cAAQ,MAAM,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC;AAAA,IAC7C;AACA,sBAAkB,OAAO;AACzB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,UAAUC,MAAK,QAAQ,EAAE,aAAa,OAAO,SAAS,KAAK,IAAI,CAAC,GAAG;AAC1E,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AACjD,QAAI,QAAQ;AACV;AACF,QAAI,MAAM,KAAK,KAAK,QAAQ;AAC1B,aAAO,eAAeA,MAAK,KAAK;AAAA,QAC9B,MAAM;AACJ,iBAAO,MAAM;AAAA,QACf;AAAA,QACA,IAAI,GAAG;AACL,gBAAM,QAAQ;AAAA,QAChB;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,aAAO,eAAeA,MAAK,KAAK,EAAE,OAAO,WAAW,CAAC;AAAA,IACvD;AAAA,EACF;AACA,SAAOA;AACT;AAEA,SAAS,IAAI,KAAK,KAAK;AACrB,MAAI,OAAO;AACT,WAAO,MAAM,GAAG;AAClB,SAAO,MAAM,GAAG,EAAE,GAAG;AACvB;AAEA,SAAS,UAAU,GAAG;AACpB,SAAO,MAAM,CAAC,KAAK;AACrB;AAEA,SAAS,mBAAmB,KAAK,KAAK;AACpC,MAAI,OAAO,WAAW,aAAa;AACjC,UAAM,QAAQ,EAAE,GAAG,IAAI;AACvB,WAAO,eAAe,OAAO,OAAO,UAAU;AAAA,MAC5C,YAAY;AAAA,MACZ,QAAQ;AACN,YAAI,QAAQ;AACZ,eAAO;AAAA,UACL,MAAM,OAAO;AAAA,YACX,OAAO,IAAI,OAAO;AAAA,YAClB,MAAM,QAAQ,IAAI;AAAA,UACpB;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT,OAAO;AACL,WAAO,OAAO,OAAO,CAAC,GAAG,GAAG,GAAG,GAAG;AAAA,EACpC;AACF;AAEA,SAAS,SAAS,IAAI,SAAS;AAC7B,QAAM,WAAW,WAAW,OAAO,SAAS,QAAQ,oBAAoB,QAAQ,QAAQ;AACxF,SAAO,YAAY,MAAM;AACvB,WAAO,SAAS,MAAM,GAAG,MAAM,MAAM,KAAK,IAAI,CAAC,MAAM,QAAQ,CAAC,CAAC,CAAC,CAAC;AAAA,EACnE;AACF;AAEA,SAAS,eAAe,KAAK,gBAAgB,CAAC,GAAG;AAC/C,MAAIC,QAAO,CAAC;AACZ,MAAI;AACJ,MAAI,MAAM,QAAQ,aAAa,GAAG;AAChC,IAAAA,QAAO;AAAA,EACT,OAAO;AACL,cAAU;AACV,UAAM,EAAE,uBAAuB,KAAK,IAAI;AACxC,IAAAA,MAAK,KAAK,GAAG,OAAO,KAAK,GAAG,CAAC;AAC7B,QAAI;AACF,MAAAA,MAAK,KAAK,GAAG,OAAO,oBAAoB,GAAG,CAAC;AAAA,EAChD;AACA,SAAO,OAAO;AAAA,IACZA,MAAK,IAAI,CAAC,QAAQ;AAChB,YAAM,QAAQ,IAAI,GAAG;AACrB,aAAO;AAAA,QACL;AAAA,QACA,OAAO,UAAU,aAAa,SAAS,MAAM,KAAK,GAAG,GAAG,OAAO,IAAI;AAAA,MACrE;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEA,SAAS,WAAW,WAAW;AAC7B,MAAI,CAAC,MAAM,SAAS;AAClB,WAAO,SAAS,SAAS;AAC3B,QAAM,QAAQ,IAAI,MAAM,CAAC,GAAG;AAAA,IAC1B,IAAI,GAAG,GAAG,UAAU;AAClB,aAAO,MAAM,QAAQ,IAAI,UAAU,OAAO,GAAG,QAAQ,CAAC;AAAA,IACxD;AAAA,IACA,IAAI,GAAG,GAAG,OAAO;AACf,UAAI,MAAM,UAAU,MAAM,CAAC,CAAC,KAAK,CAAC,MAAM,KAAK;AAC3C,kBAAU,MAAM,CAAC,EAAE,QAAQ;AAAA;AAE3B,kBAAU,MAAM,CAAC,IAAI;AACvB,aAAO;AAAA,IACT;AAAA,IACA,eAAe,GAAG,GAAG;AACnB,aAAO,QAAQ,eAAe,UAAU,OAAO,CAAC;AAAA,IAClD;AAAA,IACA,IAAI,GAAG,GAAG;AACR,aAAO,QAAQ,IAAI,UAAU,OAAO,CAAC;AAAA,IACvC;AAAA,IACA,UAAU;AACR,aAAO,OAAO,KAAK,UAAU,KAAK;AAAA,IACpC;AAAA,IACA,2BAA2B;AACzB,aAAO;AAAA,QACL,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO,SAAS,KAAK;AACvB;AAEA,SAAS,iBAAiB,IAAI;AAC5B,SAAO,WAAW,SAAS,EAAE,CAAC;AAChC;AAEA,SAAS,aAAa,QAAQA,OAAM;AAClC,QAAM,WAAWA,MAAK,KAAK;AAC3B,QAAM,YAAY,SAAS,CAAC;AAC5B,SAAO,iBAAiB,MAAM,OAAO,cAAc,aAAa,OAAO,YAAY,OAAO,QAAQ,OAAS,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,QAAU,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,OAAO,YAAY,OAAO,QAAQ,OAAS,GAAG,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,SAAS,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/P;AAEA,IAAM,WAAW,OAAO,WAAW,eAAe,OAAO,aAAa;AACtE,IAAM,WAAW,OAAO,sBAAsB,eAAe,sBAAsB;AACnF,IAAM,QAAQ,CAAC,QAAQ,OAAO,QAAQ;AACtC,IAAM,aAAa,CAAC,QAAQ,OAAO;AACnC,IAAM,SAAS,CAAC,cAAc,UAAU;AACtC,MAAI,CAAC;AACH,YAAQ,KAAK,GAAG,KAAK;AACzB;AACA,IAAM,WAAW,OAAO,UAAU;AAClC,IAAM,WAAW,CAAC,QAAQ,SAAS,KAAK,GAAG,MAAM;AACjD,IAAM,MAAM,MAAM,KAAK,IAAI;AAC3B,IAAM,YAAY,MAAM,CAAC,KAAK,IAAI;AAClC,IAAM,QAAQ,CAAC,GAAG,KAAK,QAAQ,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,CAAC,CAAC;AAC7D,IAAM,OAAO,MAAM;AACnB;AACA,IAAM,OAAO,CAAC,KAAK,QAAQ;AACzB,QAAM,KAAK,KAAK,GAAG;AACnB,QAAM,KAAK,MAAM,GAAG;AACpB,SAAO,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,MAAM,EAAE,IAAI;AACvD;AACA,IAAM,SAAS,CAAC,KAAK,QAAQ,OAAO,UAAU,eAAe,KAAK,KAAK,GAAG;AAC1E,IAAM,QAAwB,SAAS;AACvC,SAAS,WAAW;AAClB,MAAI,IAAI;AACR,SAAO,cAAc,KAAK,UAAU,OAAO,SAAS,OAAO,cAAc,OAAO,SAAS,GAAG,eAAe,mBAAmB,KAAK,OAAO,UAAU,SAAS,OAAO,KAAK,UAAU,OAAO,SAAS,OAAO,cAAc,OAAO,SAAS,GAAG,kBAAkB,KAAK,iBAAiB,KAAK,UAAU,OAAO,SAAS,OAAO,UAAU,SAAS;AAC9U;AAEA,SAAS,oBAAoB,QAAQ,IAAI;AACvC,WAAS,WAAW,MAAM;AACxB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,cAAQ,QAAQ,OAAO,MAAM,GAAG,MAAM,MAAM,IAAI,GAAG,EAAE,IAAI,SAAS,MAAM,KAAK,CAAC,CAAC,EAAE,KAAK,OAAO,EAAE,MAAM,MAAM;AAAA,IAC7G,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,IAAM,eAAe,CAACC,YAAW;AAC/B,SAAOA,QAAO;AAChB;AACA,SAAS,eAAe,IAAI,UAAU,CAAC,GAAG;AACxC,MAAI;AACJ,MAAI;AACJ,MAAI,eAAe;AACnB,QAAM,gBAAgB,CAAC,WAAW;AAChC,iBAAa,MAAM;AACnB,iBAAa;AACb,mBAAe;AAAA,EACjB;AACA,MAAI;AACJ,QAAM,SAAS,CAACA,YAAW;AACzB,UAAM,WAAW,QAAU,EAAE;AAC7B,UAAM,cAAc,QAAU,QAAQ,OAAO;AAC7C,QAAI;AACF,oBAAc,KAAK;AACrB,QAAI,YAAY,KAAK,gBAAgB,UAAU,eAAe,GAAG;AAC/D,UAAI,UAAU;AACZ,sBAAc,QAAQ;AACtB,mBAAW;AAAA,MACb;AACA,aAAO,QAAQ,QAAQA,QAAO,CAAC;AAAA,IACjC;AACA,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,qBAAe,QAAQ,iBAAiB,SAAS;AACjD,oBAAcA;AACd,UAAI,eAAe,CAAC,UAAU;AAC5B,mBAAW,WAAW,MAAM;AAC1B,cAAI;AACF,0BAAc,KAAK;AACrB,qBAAW;AACX,kBAAQ,YAAY,CAAC;AAAA,QACvB,GAAG,WAAW;AAAA,MAChB;AACA,cAAQ,WAAW,MAAM;AACvB,YAAI;AACF,wBAAc,QAAQ;AACxB,mBAAW;AACX,gBAAQA,QAAO,CAAC;AAAA,MAClB,GAAG,QAAQ;AAAA,IACb,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,kBAAkB,MAAM;AAC/B,MAAI,WAAW;AACf,MAAI;AACJ,MAAI,YAAY;AAChB,MAAI,eAAe;AACnB,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,CAAC,MAAM,KAAK,CAAC,CAAC,KAAK,OAAO,KAAK,CAAC,MAAM;AACxC,KAAC,EAAE,OAAO,IAAI,WAAW,MAAM,UAAU,MAAM,iBAAiB,MAAM,IAAI,KAAK,CAAC;AAAA;AAEhF,KAAC,IAAI,WAAW,MAAM,UAAU,MAAM,iBAAiB,KAAK,IAAI;AAClE,QAAM,QAAQ,MAAM;AAClB,QAAI,OAAO;AACT,mBAAa,KAAK;AAClB,cAAQ;AACR,mBAAa;AACb,qBAAe;AAAA,IACjB;AAAA,EACF;AACA,QAAM,SAAS,CAAC,YAAY;AAC1B,UAAM,WAAW,QAAU,EAAE;AAC7B,UAAM,UAAU,KAAK,IAAI,IAAI;AAC7B,UAAMA,UAAS,MAAM;AACnB,aAAO,YAAY,QAAQ;AAAA,IAC7B;AACA,UAAM;AACN,QAAI,YAAY,GAAG;AACjB,iBAAW,KAAK,IAAI;AACpB,aAAOA,QAAO;AAAA,IAChB;AACA,QAAI,UAAU,aAAa,WAAW,CAAC,YAAY;AACjD,iBAAW,KAAK,IAAI;AACpB,MAAAA,QAAO;AAAA,IACT,WAAW,UAAU;AACnB,kBAAY,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC3C,uBAAe,iBAAiB,SAAS;AACzC,gBAAQ,WAAW,MAAM;AACvB,qBAAW,KAAK,IAAI;AACpB,sBAAY;AACZ,kBAAQA,QAAO,CAAC;AAChB,gBAAM;AAAA,QACR,GAAG,KAAK,IAAI,GAAG,WAAW,OAAO,CAAC;AAAA,MACpC,CAAC;AAAA,IACH;AACA,QAAI,CAAC,WAAW,CAAC;AACf,cAAQ,WAAW,MAAM,YAAY,MAAM,QAAQ;AACrD,gBAAY;AACZ,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,eAAe,eAAe,cAAc,UAAU,CAAC,GAAG;AACjE,QAAM;AAAA,IACJ,eAAe;AAAA,EACjB,IAAI;AACJ,QAAM,WAAWC,OAAM,iBAAiB,QAAQ;AAChD,WAAS,QAAQ;AACf,aAAS,QAAQ;AAAA,EACnB;AACA,WAAS,SAAS;AAChB,aAAS,QAAQ;AAAA,EACnB;AACA,QAAM,cAAc,IAAI,SAAS;AAC/B,QAAI,SAAS;AACX,mBAAa,GAAG,IAAI;AAAA,EACxB;AACA,SAAO,EAAE,UAAU,SAAS,QAAQ,GAAG,OAAO,QAAQ,YAAY;AACpE;AAEA,SAAS,oBAAoB,IAAI;AAC/B,QAAM,QAAwB,uBAAO,OAAO,IAAI;AAChD,SAAO,CAAC,QAAQ;AACd,UAAM,MAAM,MAAM,GAAG;AACrB,WAAO,QAAQ,MAAM,GAAG,IAAI,GAAG,GAAG;AAAA,EACpC;AACF;AACA,IAAM,cAAc;AACpB,IAAM,YAAY,oBAAoB,CAAC,QAAQ,IAAI,QAAQ,aAAa,KAAK,EAAE,YAAY,CAAC;AAC5F,IAAM,aAAa;AACnB,IAAM,WAAW,oBAAoB,CAAC,QAAQ;AAC5C,SAAO,IAAI,QAAQ,YAAY,CAAC,GAAG,MAAM,IAAI,EAAE,YAAY,IAAI,EAAE;AACnE,CAAC;AAED,SAAS,eAAe,IAAI,iBAAiB,OAAO,SAAS,WAAW;AACtE,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,QAAI;AACF,iBAAW,MAAM,OAAO,MAAM,GAAG,EAAE;AAAA;AAEnC,iBAAW,SAAS,EAAE;AAAA,EAC1B,CAAC;AACH;AACA,SAAS,SAAS,KAAK;AACrB,SAAO;AACT;AACA,SAAS,uBAAuB,IAAI;AAClC,MAAI;AACJ,WAAS,UAAU;AACjB,QAAI,CAAC;AACH,iBAAW,GAAG;AAChB,WAAO;AAAA,EACT;AACA,UAAQ,QAAQ,YAAY;AAC1B,UAAM,QAAQ;AACd,eAAW;AACX,QAAI;AACF,YAAM;AAAA,EACV;AACA,SAAO;AACT;AACA,SAAS,OAAO,IAAI;AAClB,SAAO,GAAG;AACZ;AACA,SAAS,aAAa,QAAQ,OAAO;AACnC,SAAO,MAAM,KAAK,CAAC,MAAM,KAAK,GAAG;AACnC;AACA,SAAS,iBAAiB,QAAQ,OAAO;AACvC,MAAI;AACJ,MAAI,OAAO,WAAW;AACpB,WAAO,SAAS;AAClB,QAAM,UAAU,KAAK,OAAO,MAAM,cAAc,MAAM,OAAO,SAAS,GAAG,CAAC,MAAM;AAChF,QAAM,OAAO,OAAO,MAAM,MAAM,MAAM;AACtC,QAAM,SAAS,OAAO,WAAW,KAAK,IAAI;AAC1C,MAAI,OAAO,MAAM,MAAM;AACrB,WAAO;AACT,SAAO,SAAS;AAClB;AACA,SAAS,QAAQ,IAAI;AACnB,SAAO,GAAG,SAAS,KAAK,IAAI,OAAO,WAAW,EAAE,IAAI,KAAK,OAAO,WAAW,EAAE;AAC/E;AACA,SAAS,WAAW,KAAKF,OAAM,gBAAgB,OAAO;AACpD,SAAOA,MAAK,OAAO,CAAC,GAAG,MAAM;AAC3B,QAAI,KAAK,KAAK;AACZ,UAAI,CAAC,iBAAiB,IAAI,CAAC,MAAM;AAC/B,UAAE,CAAC,IAAI,IAAI,CAAC;AAAA,IAChB;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AACA,SAAS,WAAW,KAAKA,OAAM,gBAAgB,OAAO;AACpD,SAAO,OAAO,YAAY,OAAO,QAAQ,GAAG,EAAE,OAAO,CAAC,CAAC,KAAK,KAAK,MAAM;AACrE,YAAQ,CAAC,iBAAiB,UAAU,WAAW,CAACA,MAAK,SAAS,GAAG;AAAA,EACnE,CAAC,CAAC;AACJ;AACA,SAAS,cAAc,KAAK;AAC1B,SAAO,OAAO,QAAQ,GAAG;AAC3B;AACA,SAAS,mBAAmB,QAAQ;AAClC,SAAO,UAAU,mBAAmB;AACtC;AACA,SAAS,QAAQ,OAAO;AACtB,SAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AAC9C;AAEA,SAASE,UAAS,MAAM;AACtB,MAAI,KAAK,WAAW;AAClB,WAAO,MAAQ,GAAG,IAAI;AACxB,QAAM,IAAI,KAAK,CAAC;AAChB,SAAO,OAAO,MAAM,aAAa,SAAS,UAAU,OAAO,EAAE,KAAK,GAAG,KAAK,KAAK,EAAE,CAAC,IAAI,IAAI,CAAC;AAC7F;AACA,IAAM,aAAaA;AAEnB,SAAS,aAAa,QAAQF,OAAM;AAClC,QAAM,WAAWA,MAAK,KAAK;AAC3B,QAAM,YAAY,SAAS,CAAC;AAC5B,SAAO,iBAAiB,MAAM,OAAO,cAAc,aAAa,OAAO,YAAY,OAAO,QAAQ,OAAS,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,MAAM,UAAU,QAAU,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,OAAO,YAAY,SAAS,IAAI,CAAC,MAAM,CAAC,GAAGE,OAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAChO;AAEA,SAAS,aAAa,cAAc,UAAU,KAAK;AACjD,SAAO,UAAU,CAAC,OAAO,YAAY;AACnC,QAAI,QAAQ,QAAU,YAAY;AAClC,QAAI;AACJ,UAAM,aAAa,MAAM,WAAW,MAAM;AACxC,cAAQ,QAAU,YAAY;AAC9B,cAAQ;AAAA,IACV,GAAG,QAAU,OAAO,CAAC;AACrB,sBAAkB,MAAM;AACtB,mBAAa,KAAK;AAAA,IACpB,CAAC;AACD,WAAO;AAAA,MACL,MAAM;AACJ,cAAM;AACN,eAAO;AAAA,MACT;AAAA,MACA,IAAI,UAAU;AACZ,gBAAQ;AACR,gBAAQ;AACR,qBAAa,KAAK;AAClB,gBAAQ,WAAW;AAAA,MACrB;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAEA,SAAS,cAAc,IAAI,KAAK,KAAK,UAAU,CAAC,GAAG;AACjD,SAAO;AAAA,IACL,eAAe,IAAI,OAAO;AAAA,IAC1B;AAAA,EACF;AACF;AAEA,SAAS,aAAa,OAAO,KAAK,KAAK,UAAU,CAAC,GAAG;AACnD,QAAM,YAAY,IAAI,MAAM,KAAK;AACjC,QAAM,UAAU,cAAc,MAAM;AAClC,cAAU,QAAQ,MAAM;AAAA,EAC1B,GAAG,IAAI,OAAO;AACd,QAAM,OAAO,MAAM,QAAQ,CAAC;AAC5B,SAAO;AACT;AAEA,SAAS,WAAW,QAAQ,cAAc;AACxC,SAAO,SAAS;AAAA,IACd,MAAM;AACJ,UAAI;AACJ,cAAQ,KAAK,OAAO,UAAU,OAAO,KAAK;AAAA,IAC5C;AAAA,IACA,IAAI,OAAO;AACT,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF,CAAC;AACH;AAEA,SAAS,cAAc,IAAI,KAAK,KAAK,WAAW,OAAO,UAAU,MAAM,iBAAiB,OAAO;AAC7F,SAAO;AAAA,IACL,eAAe,IAAI,UAAU,SAAS,cAAc;AAAA,IACpD;AAAA,EACF;AACF;AAEA,SAAS,aAAa,OAAO,QAAQ,KAAK,WAAW,MAAM,UAAU,MAAM;AACzE,MAAI,SAAS;AACX,WAAO;AACT,QAAM,YAAY,IAAI,MAAM,KAAK;AACjC,QAAM,UAAU,cAAc,MAAM;AAClC,cAAU,QAAQ,MAAM;AAAA,EAC1B,GAAG,OAAO,UAAU,OAAO;AAC3B,QAAM,OAAO,MAAM,QAAQ,CAAC;AAC5B,SAAO;AACT;AAEA,SAAS,eAAe,SAAS,UAAU,CAAC,GAAG;AAC7C,MAAI,SAAS;AACb,MAAI;AACJ,MAAI;AACJ,QAAMH,OAAM,UAAU,CAAC,QAAQ,aAAa;AAC1C,YAAQ;AACR,cAAU;AACV,WAAO;AAAA,MACL,MAAM;AACJ,eAAOF,KAAI;AAAA,MACb;AAAA,MACA,IAAI,GAAG;AACL,QAAAC,KAAI,CAAC;AAAA,MACP;AAAA,IACF;AAAA,EACF,CAAC;AACD,WAASD,KAAI,WAAW,MAAM;AAC5B,QAAI;AACF,YAAM;AACR,WAAO;AAAA,EACT;AACA,WAASC,KAAI,OAAO,aAAa,MAAM;AACrC,QAAI,IAAI;AACR,QAAI,UAAU;AACZ;AACF,UAAM,MAAM;AACZ,UAAM,KAAK,QAAQ,mBAAmB,OAAO,SAAS,GAAG,KAAK,SAAS,OAAO,GAAG,OAAO;AACtF;AACF,aAAS;AACT,KAAC,KAAK,QAAQ,cAAc,OAAO,SAAS,GAAG,KAAK,SAAS,OAAO,GAAG;AACvE,QAAI;AACF,cAAQ;AAAA,EACZ;AACA,QAAM,eAAe,MAAMD,KAAI,KAAK;AACpC,QAAM,YAAY,CAAC,MAAMC,KAAI,GAAG,KAAK;AACrC,QAAM,OAAO,MAAMD,KAAI,KAAK;AAC5B,QAAM,MAAM,CAAC,MAAMC,KAAI,GAAG,KAAK;AAC/B,SAAO;AAAA,IACLC;AAAA,IACA;AAAA,MACE,KAAAF;AAAA,MACA,KAAAC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,EAAE,YAAY,KAAK;AAAA,EACrB;AACF;AACA,IAAM,gBAAgB;AAEtB,SAAS,OAAO,MAAM;AACpB,MAAI,KAAK,WAAW,GAAG;AACrB,UAAM,CAACC,MAAK,KAAK,IAAI;AACrB,IAAAA,KAAI,QAAQ;AAAA,EACd;AACA,MAAI,KAAK,WAAW,GAAG;AACrB,UAAM,CAAC,QAAQ,KAAK,KAAK,IAAI;AAC7B,WAAO,GAAG,IAAI;AAAA,EAChB;AACF;AAEA,SAAS,gBAAgB,QAAQ,IAAI,UAAU,CAAC,GAAG;AACjD,QAAM;AAAA,IACJ,cAAc;AAAA,IACd,GAAG;AAAA,EACL,IAAI;AACJ,SAAO;AAAA,IACL;AAAA,IACA;AAAA,MACE;AAAA,MACA;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,cAAc,QAAQ,IAAI,UAAU,CAAC,GAAG;AAC/C,QAAM;AAAA,IACJ,aAAa;AAAA,IACb,eAAe;AAAA,IACf,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,EAAE,aAAa,OAAO,QAAQ,SAAS,IAAI,eAAe,QAAQ,EAAE,aAAa,CAAC;AACxF,QAAM,OAAO;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACA,SAAO,EAAE,MAAM,OAAO,QAAQ,SAAS;AACzC;AAEA,SAAS,QAAQ,MAAM,UAAU,CAAC,OAAO,GAAG;AAC1C,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY,CAAC;AAAA,EACf,IAAI,WAAW,CAAC;AAChB,QAAM,WAAW,CAAC;AAClB,QAAM,eAAe,SAAS,aAAa,UAAU,QAAQ,CAAC,MAAM;AACpE,QAAM,eAAe,SAAS,aAAa,UAAU,QAAQ,CAAC,MAAM;AACpE,MAAI,cAAc,UAAU,cAAc,OAAO;AAC/C,aAAS,KAAK;AAAA,MACZ;AAAA,MACA,CAAC,aAAa;AACZ,iBAAS,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC;AACjC,cAAM,QAAQ,aAAa,QAAQ;AACnC,iBAAS,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC;AAAA,MACpC;AAAA,MACA,EAAE,OAAO,MAAM,UAAU;AAAA,IAC3B,CAAC;AAAA,EACH;AACA,MAAI,cAAc,UAAU,cAAc,OAAO;AAC/C,aAAS,KAAK;AAAA,MACZ;AAAA,MACA,CAAC,aAAa;AACZ,iBAAS,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC;AACjC,aAAK,QAAQ,aAAa,QAAQ;AAClC,iBAAS,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC;AAAA,MACpC;AAAA,MACA,EAAE,OAAO,MAAM,UAAU;AAAA,IAC3B,CAAC;AAAA,EACH;AACA,QAAM,OAAO,MAAM;AACjB,aAAS,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC;AAAA,EAClC;AACA,SAAO;AACT;AAEA,SAAS,SAAS,QAAQ,SAAS,UAAU,CAAC,GAAG;AAC/C,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,YAAY;AAAA,EACd,IAAI;AACJ,QAAM,eAAe,QAAQ,OAAO;AACpC,SAAO;AAAA,IACL;AAAA,IACA,CAAC,aAAa,aAAa,QAAQ,CAAC,WAAW,OAAO,QAAQ,QAAQ;AAAA,IACtE,EAAE,OAAO,MAAM,UAAU;AAAA,EAC3B;AACF;AAEA,SAASI,QAAO,WAAW,UAAU,CAAC,GAAG;AACvC,MAAI,CAAC,MAAM,SAAS;AAClB,WAAO,OAAS,SAAS;AAC3B,QAAM,SAAS,MAAM,QAAQ,UAAU,KAAK,IAAI,MAAM,KAAK,EAAE,QAAQ,UAAU,MAAM,OAAO,CAAC,IAAI,CAAC;AAClG,aAAW,OAAO,UAAU,OAAO;AACjC,WAAO,GAAG,IAAI,UAAU,OAAO;AAAA,MAC7B,MAAM;AACJ,eAAO,UAAU,MAAM,GAAG;AAAA,MAC5B;AAAA,MACA,IAAI,GAAG;AACL,YAAI;AACJ,cAAM,cAAc,KAAK,QAAU,QAAQ,UAAU,MAAM,OAAO,KAAK;AACvE,YAAI,YAAY;AACd,cAAI,MAAM,QAAQ,UAAU,KAAK,GAAG;AAClC,kBAAM,OAAO,CAAC,GAAG,UAAU,KAAK;AAChC,iBAAK,GAAG,IAAI;AACZ,sBAAU,QAAQ;AAAA,UACpB,OAAO;AACL,kBAAM,YAAY,EAAE,GAAG,UAAU,OAAO,CAAC,GAAG,GAAG,EAAE;AACjD,mBAAO,eAAe,WAAW,OAAO,eAAe,UAAU,KAAK,CAAC;AACvE,sBAAU,QAAQ;AAAA,UACpB;AAAA,QACF,OAAO;AACL,oBAAU,MAAM,GAAG,IAAI;AAAA,QACzB;AAAA,MACF;AAAA,IACF,EAAE;AAAA,EACJ;AACA,SAAO;AACT;AAEA,IAAMC,WAAU;AAChB,IAAM,eAAe;AAErB,SAAS,iBAAiB,IAAI,OAAO,MAAM,QAAQ;AACjD,QAAM,WAAW,mBAAmB,MAAM;AAC1C,MAAI;AACF,kBAAc,IAAI,MAAM;AAAA,WACjB;AACP,OAAG;AAAA;AAEH,aAAS,EAAE;AACf;AAEA,SAAS,mBAAmB,IAAI,QAAQ;AACtC,QAAM,WAAW,mBAAmB,MAAM;AAC1C,MAAI;AACF,oBAAgB,IAAI,MAAM;AAC9B;AAEA,SAAS,aAAa,IAAI,OAAO,MAAM,QAAQ;AAC7C,QAAM,WAAW,mBAAmB;AACpC,MAAI;AACF,cAAU,IAAI,MAAM;AAAA,WACb;AACP,OAAG;AAAA;AAEH,aAAS,EAAE;AACf;AAEA,SAAS,eAAe,IAAI,QAAQ;AAClC,QAAM,WAAW,mBAAmB,MAAM;AAC1C,MAAI;AACF,gBAAY,IAAI,MAAM;AAC1B;AAEA,SAAS,YAAY,GAAG,QAAQ,OAAO;AACrC,WAAS,QAAQ,WAAW,EAAE,QAAQ,QAAQ,OAAO,OAAO,SAAS,eAAe,IAAI,CAAC,GAAG;AAC1F,QAAI,OAAO;AACX,UAAM,UAAU,IAAI,QAAQ,CAAC,YAAY;AACvC,aAAO;AAAA,QACL;AAAA,QACA,CAAC,MAAM;AACL,cAAI,UAAU,CAAC,MAAM,OAAO;AAC1B,gBAAI;AACF,mBAAK;AAAA;AAEL,uBAAS,MAAM,QAAQ,OAAO,SAAS,KAAK,CAAC;AAC/C,oBAAQ,CAAC;AAAA,UACX;AAAA,QACF;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,UACA,WAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW,CAAC,OAAO;AACzB,QAAI,WAAW,MAAM;AACnB,eAAS;AAAA,QACP,eAAe,SAAS,cAAc,EAAE,KAAK,MAAM,QAAU,CAAC,CAAC,EAAE,QAAQ,MAAM,QAAQ,OAAO,SAAS,KAAK,CAAC;AAAA,MAC/G;AAAA,IACF;AACA,WAAO,QAAQ,KAAK,QAAQ;AAAA,EAC9B;AACA,WAAS,KAAK,OAAO,SAAS;AAC5B,QAAI,CAAC,MAAM,KAAK;AACd,aAAO,QAAQ,CAAC,MAAM,MAAM,OAAO,OAAO;AAC5C,UAAM,EAAE,QAAQ,QAAQ,OAAO,OAAO,SAAS,eAAe,IAAI,WAAW,OAAO,UAAU,CAAC;AAC/F,QAAI,OAAO;AACX,UAAM,UAAU,IAAI,QAAQ,CAAC,YAAY;AACvC,aAAO;AAAA,QACL,CAAC,GAAG,KAAK;AAAA,QACT,CAAC,CAAC,IAAI,EAAE,MAAM;AACZ,cAAI,WAAW,OAAO,KAAK;AACzB,gBAAI;AACF,mBAAK;AAAA;AAEL,uBAAS,MAAM,QAAQ,OAAO,SAAS,KAAK,CAAC;AAC/C,oBAAQ,EAAE;AAAA,UACZ;AAAA,QACF;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,UACA,WAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW,CAAC,OAAO;AACzB,QAAI,WAAW,MAAM;AACnB,eAAS;AAAA,QACP,eAAe,SAAS,cAAc,EAAE,KAAK,MAAM,QAAU,CAAC,CAAC,EAAE,QAAQ,MAAM;AAC7E,kBAAQ,OAAO,SAAS,KAAK;AAC7B,iBAAO,QAAU,CAAC;AAAA,QACpB,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO,QAAQ,KAAK,QAAQ;AAAA,EAC9B;AACA,WAAS,WAAW,SAAS;AAC3B,WAAO,QAAQ,CAAC,MAAM,QAAQ,CAAC,GAAG,OAAO;AAAA,EAC3C;AACA,WAAS,SAAS,SAAS;AACzB,WAAO,KAAK,MAAM,OAAO;AAAA,EAC3B;AACA,WAAS,cAAc,SAAS;AAC9B,WAAO,KAAK,QAAQ,OAAO;AAAA,EAC7B;AACA,WAAS,QAAQ,SAAS;AACxB,WAAO,QAAQ,OAAO,OAAO,OAAO;AAAA,EACtC;AACA,WAAS,WAAW,OAAO,SAAS;AAClC,WAAO,QAAQ,CAAC,MAAM;AACpB,YAAM,QAAQ,MAAM,KAAK,CAAC;AAC1B,aAAO,MAAM,SAAS,KAAK,KAAK,MAAM,SAAS,QAAU,KAAK,CAAC;AAAA,IACjE,GAAG,OAAO;AAAA,EACZ;AACA,WAAS,QAAQ,SAAS;AACxB,WAAO,aAAa,GAAG,OAAO;AAAA,EAChC;AACA,WAAS,aAAa,IAAI,GAAG,SAAS;AACpC,QAAI,QAAQ;AACZ,WAAO,QAAQ,MAAM;AACnB,eAAS;AACT,aAAO,SAAS;AAAA,IAClB,GAAG,OAAO;AAAA,EACZ;AACA,MAAI,MAAM,QAAQ,QAAU,CAAC,CAAC,GAAG;AAC/B,UAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,IAAI,MAAM;AACR,eAAO,YAAY,GAAG,CAAC,KAAK;AAAA,MAC9B;AAAA,IACF;AACA,WAAO;AAAA,EACT,OAAO;AACL,UAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,IAAI,MAAM;AACR,eAAO,YAAY,GAAG,CAAC,KAAK;AAAA,MAC9B;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,MAAM,GAAG;AAChB,SAAO,YAAY,CAAC;AACtB;AAEA,SAAS,kBAAkB,OAAO,QAAQ;AACxC,SAAO,UAAU;AACnB;AACA,SAAS,sBAAsB,MAAM;AACnC,MAAI,IAAI;AACR,QAAM,OAAO,KAAK,CAAC;AACnB,QAAM,SAAS,KAAK,CAAC;AACrB,MAAI,aAAa,KAAK,KAAK,CAAC,MAAM,OAAO,KAAK;AAC9C,QAAM;AAAA,IACJ,YAAY;AAAA,EACd,KAAK,KAAK,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC;AACnC,MAAI,OAAO,cAAc,UAAU;AACjC,UAAM,MAAM;AACZ,gBAAY,CAAC,OAAO,WAAW,MAAM,GAAG,MAAM,OAAO,GAAG;AAAA,EAC1D;AACA,QAAM,QAAQ,SAAS,MAAM,QAAU,IAAI,EAAE,OAAO,CAAC,MAAM,QAAU,MAAM,EAAE,UAAU,CAAC,MAAM,UAAU,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC;AACtH,MAAI,WAAW;AACb,UAAM,QAAQ,SAAS,MAAM,QAAU,MAAM,EAAE,OAAO,CAAC,MAAM,QAAU,IAAI,EAAE,UAAU,CAAC,MAAM,UAAU,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC;AACtH,WAAO,SAAS,MAAM,YAAY,CAAC,GAAG,QAAU,KAAK,GAAG,GAAG,QAAU,KAAK,CAAC,IAAI,QAAU,KAAK,CAAC;AAAA,EACjG,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,SAAS,cAAc,MAAM,IAAI;AAC/B,SAAO,SAAS,MAAM,QAAU,IAAI,EAAE,MAAM,CAAC,SAAS,OAAO,UAAU,GAAG,QAAU,OAAO,GAAG,OAAO,KAAK,CAAC,CAAC;AAC9G;AAEA,SAAS,eAAe,MAAM,IAAI;AAChC,SAAO,SAAS,MAAM,QAAU,IAAI,EAAE,IAAI,CAAC,MAAM,QAAU,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC;AAC3E;AAEA,SAAS,aAAa,MAAM,IAAI;AAC9B,SAAO,SAAS,MAAM;AAAA,IACpB,QAAU,IAAI,EAAE,KAAK,CAAC,SAAS,OAAO,UAAU,GAAG,QAAU,OAAO,GAAG,OAAO,KAAK,CAAC;AAAA,EACtF,CAAC;AACH;AAEA,SAAS,kBAAkB,MAAM,IAAI;AACnC,SAAO,SAAS,MAAM,QAAU,IAAI,EAAE,UAAU,CAAC,SAAS,OAAO,UAAU,GAAG,QAAU,OAAO,GAAG,OAAO,KAAK,CAAC,CAAC;AAClH;AAEA,SAAS,SAAS,KAAK,IAAI;AACzB,MAAI,QAAQ,IAAI;AAChB,SAAO,UAAU,GAAG;AAClB,QAAI,GAAG,IAAI,KAAK,GAAG,OAAO,GAAG;AAC3B,aAAO,IAAI,KAAK;AAAA,EACpB;AACA,SAAO;AACT;AACA,SAAS,iBAAiB,MAAM,IAAI;AAClC,SAAO,SAAS,MAAM;AAAA,IACpB,CAAC,MAAM,UAAU,WAAW,SAAS,QAAU,IAAI,GAAG,CAAC,SAAS,OAAO,UAAU,GAAG,QAAU,OAAO,GAAG,OAAO,KAAK,CAAC,IAAI,QAAU,IAAI,EAAE,SAAS,CAAC,SAAS,OAAO,UAAU,GAAG,QAAU,OAAO,GAAG,OAAO,KAAK,CAAC;AAAA,EACnN,CAAC;AACH;AAEA,SAAS,uBAAuB,KAAK;AACnC,SAAO,SAAS,GAAG,KAAK,aAAa,KAAK,aAAa,YAAY;AACrE;AACA,SAAS,oBAAoB,MAAM;AACjC,MAAI;AACJ,QAAM,OAAO,KAAK,CAAC;AACnB,QAAM,QAAQ,KAAK,CAAC;AACpB,MAAI,aAAa,KAAK,CAAC;AACvB,MAAI,YAAY;AAChB,MAAI,uBAAuB,UAAU,GAAG;AACtC,iBAAa,KAAK,WAAW,cAAc,OAAO,KAAK;AACvD,iBAAa,WAAW;AAAA,EAC1B;AACA,MAAI,OAAO,eAAe,UAAU;AAClC,UAAM,MAAM;AACZ,iBAAa,CAAC,SAAS,WAAW,QAAQ,GAAG,MAAM,QAAU,MAAM;AAAA,EACrE;AACA,eAAa,cAAc,OAAO,aAAa,CAAC,SAAS,WAAW,YAAY,QAAU,MAAM;AAChG,SAAO,SAAS,MAAM,QAAU,IAAI,EAAE,MAAM,SAAS,EAAE,KAAK,CAAC,SAAS,OAAO,UAAU;AAAA,IACrF,QAAU,OAAO;AAAA,IACjB,QAAU,KAAK;AAAA,IACf;AAAA,IACA,QAAU,KAAK;AAAA,EACjB,CAAC,CAAC;AACJ;AAEA,SAAS,aAAa,MAAM,WAAW;AACrC,SAAO,SAAS,MAAM,QAAU,IAAI,EAAE,IAAI,CAAC,MAAM,QAAU,CAAC,CAAC,EAAE,KAAK,QAAU,SAAS,CAAC,CAAC;AAC3F;AAEA,SAAS,YAAY,MAAM,IAAI;AAC7B,SAAO,SAAS,MAAM,QAAU,IAAI,EAAE,IAAI,CAAC,MAAM,QAAU,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;AACxE;AAEA,SAAS,eAAe,MAAM,YAAY,MAAM;AAC9C,QAAM,iBAAiB,CAAC,KAAK,OAAO,UAAU,QAAQ,QAAU,GAAG,GAAG,QAAU,KAAK,GAAG,KAAK;AAC7F,SAAO,SAAS,MAAM;AACpB,UAAM,WAAW,QAAU,IAAI;AAC/B,WAAO,KAAK,SAAS,SAAS,OAAO,gBAAgB,OAAO,KAAK,CAAC,MAAM,aAAa,QAAU,KAAK,CAAC,EAAE,CAAC,IAAI,QAAU,KAAK,CAAC,CAAC,CAAC,IAAI,SAAS,OAAO,cAAc;AAAA,EAClK,CAAC;AACH;AAEA,SAAS,aAAa,MAAM,IAAI;AAC9B,SAAO,SAAS,MAAM,QAAU,IAAI,EAAE,KAAK,CAAC,SAAS,OAAO,UAAU,GAAG,QAAU,OAAO,GAAG,OAAO,KAAK,CAAC,CAAC;AAC7G;AAEA,SAAS,KAAK,OAAO;AACnB,SAAO,MAAM,KAAK,IAAI,IAAI,KAAK,CAAC;AAClC;AACA,SAAS,iBAAiB,OAAO,IAAI;AACnC,SAAO,MAAM,OAAO,CAAC,KAAK,MAAM;AAC9B,QAAI,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC;AAClC,UAAI,KAAK,CAAC;AACZ,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AACA,SAAS,eAAe,MAAM,WAAW;AACvC,SAAO,SAAS,MAAM;AACpB,UAAM,eAAe,QAAU,IAAI,EAAE,IAAI,CAAC,YAAY,QAAU,OAAO,CAAC;AACxE,WAAO,YAAY,iBAAiB,cAAc,SAAS,IAAI,KAAK,YAAY;AAAA,EAClF,CAAC;AACH;AAEA,SAAS,WAAW,eAAe,GAAG,UAAU,CAAC,GAAG;AAClD,MAAI,gBAAgB,MAAM,YAAY;AACtC,QAAM,QAAQ,WAAW,YAAY;AACrC,QAAM;AAAA,IACJ,MAAM,OAAO;AAAA,IACb,MAAM,OAAO;AAAA,EACf,IAAI;AACJ,QAAM,MAAM,CAAC,QAAQ,MAAM,MAAM,QAAQ,KAAK,IAAI,KAAK,IAAI,KAAK,MAAM,QAAQ,KAAK,GAAG,GAAG;AACzF,QAAM,MAAM,CAAC,QAAQ,MAAM,MAAM,QAAQ,KAAK,IAAI,KAAK,IAAI,KAAK,MAAM,QAAQ,KAAK,GAAG,GAAG;AACzF,QAAMP,OAAM,MAAM,MAAM;AACxB,QAAMC,OAAM,CAAC,QAAQ,MAAM,QAAQ,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,GAAG,CAAC;AACnE,QAAM,QAAQ,CAAC,MAAM,kBAAkB;AACrC,oBAAgB;AAChB,WAAOA,KAAI,GAAG;AAAA,EAChB;AACA,SAAO,EAAE,OAAO,KAAK,KAAK,KAAAD,MAAK,KAAAC,MAAK,MAAM;AAC5C;AAEA,IAAM,cAAc;AACpB,IAAM,eAAe;AACrB,SAAS,gBAAgB,OAAO,SAAS,aAAa,WAAW;AAC/D,MAAI,IAAI,QAAQ,KAAK,OAAO;AAC5B,MAAI;AACF,QAAI,EAAE,MAAM,EAAE,EAAE,OAAO,CAAC,KAAK,SAAS,OAAO,GAAG,IAAI,KAAK,EAAE;AAC7D,SAAO,cAAc,EAAE,YAAY,IAAI;AACzC;AACA,SAAS,cAAc,KAAK;AAC1B,QAAM,WAAW,CAAC,MAAM,MAAM,MAAM,IAAI;AACxC,QAAM,IAAI,MAAM;AAChB,SAAO,OAAO,UAAU,IAAI,MAAM,EAAE,KAAK,SAAS,CAAC,KAAK,SAAS,CAAC;AACpE;AACA,SAAS,WAAW,MAAM,WAAW,UAAU,CAAC,GAAG;AACjD,MAAI;AACJ,QAAM,QAAQ,KAAK,YAAY;AAC/B,QAAM,QAAQ,KAAK,SAAS;AAC5B,QAAM,OAAO,KAAK,QAAQ;AAC1B,QAAM,QAAQ,KAAK,SAAS;AAC5B,QAAM,UAAU,KAAK,WAAW;AAChC,QAAM,UAAU,KAAK,WAAW;AAChC,QAAM,eAAe,KAAK,gBAAgB;AAC1C,QAAM,MAAM,KAAK,OAAO;AACxB,QAAM,YAAY,KAAK,QAAQ,mBAAmB,OAAO,KAAK;AAC9D,QAAM,gBAAgB,CAAC,eAAe;AACpC,QAAI;AACJ,YAAQ,MAAM,WAAW,MAAM,GAAG,EAAE,CAAC,MAAM,OAAO,MAAM;AAAA,EAC1D;AACA,QAAM,UAAU;AAAA,IACd,IAAI,MAAM,cAAc,KAAK;AAAA,IAC7B,IAAI,MAAM,OAAO,KAAK,EAAE,MAAM,EAAE;AAAA,IAChC,MAAM,MAAM;AAAA,IACZ,GAAG,MAAM,QAAQ;AAAA,IACjB,IAAI,MAAM,cAAc,QAAQ,CAAC;AAAA,IACjC,IAAI,MAAM,GAAG,QAAQ,CAAC,GAAG,SAAS,GAAG,GAAG;AAAA,IACxC,KAAK,MAAM,KAAK,mBAAmB,QAAU,QAAQ,OAAO,GAAG,EAAE,OAAO,QAAQ,CAAC;AAAA,IACjF,MAAM,MAAM,KAAK,mBAAmB,QAAU,QAAQ,OAAO,GAAG,EAAE,OAAO,OAAO,CAAC;AAAA,IACjF,GAAG,MAAM,OAAO,IAAI;AAAA,IACpB,IAAI,MAAM,cAAc,IAAI;AAAA,IAC5B,IAAI,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,GAAG;AAAA,IACnC,GAAG,MAAM,OAAO,KAAK;AAAA,IACrB,IAAI,MAAM,cAAc,KAAK;AAAA,IAC7B,IAAI,MAAM,GAAG,KAAK,GAAG,SAAS,GAAG,GAAG;AAAA,IACpC,GAAG,MAAM,GAAG,QAAQ,MAAM,EAAE,GAAG,SAAS,GAAG,GAAG;AAAA,IAC9C,IAAI,MAAM,cAAc,QAAQ,MAAM,EAAE;AAAA,IACxC,IAAI,MAAM,GAAG,QAAQ,MAAM,EAAE,GAAG,SAAS,GAAG,GAAG;AAAA,IAC/C,GAAG,MAAM,OAAO,OAAO;AAAA,IACvB,IAAI,MAAM,cAAc,OAAO;AAAA,IAC/B,IAAI,MAAM,GAAG,OAAO,GAAG,SAAS,GAAG,GAAG;AAAA,IACtC,GAAG,MAAM,OAAO,OAAO;AAAA,IACvB,IAAI,MAAM,cAAc,OAAO;AAAA,IAC/B,IAAI,MAAM,GAAG,OAAO,GAAG,SAAS,GAAG,GAAG;AAAA,IACtC,KAAK,MAAM,GAAG,YAAY,GAAG,SAAS,GAAG,GAAG;AAAA,IAC5C,GAAG,MAAM;AAAA,IACT,IAAI,MAAM,KAAK,mBAAmB,QAAU,QAAQ,OAAO,GAAG,EAAE,SAAS,SAAS,CAAC;AAAA,IACnF,KAAK,MAAM,KAAK,mBAAmB,QAAU,QAAQ,OAAO,GAAG,EAAE,SAAS,QAAQ,CAAC;AAAA,IACnF,MAAM,MAAM,KAAK,mBAAmB,QAAU,QAAQ,OAAO,GAAG,EAAE,SAAS,OAAO,CAAC;AAAA,IACnF,GAAG,MAAM,SAAS,OAAO,OAAO;AAAA,IAChC,IAAI,MAAM,SAAS,OAAO,SAAS,OAAO,IAAI;AAAA,IAC9C,GAAG,MAAM,SAAS,OAAO,SAAS,IAAI;AAAA,IACtC,IAAI,MAAM,SAAS,OAAO,SAAS,MAAM,IAAI;AAAA,IAC7C,GAAG,MAAM,cAAc,KAAK,mBAAmB,QAAU,QAAQ,OAAO,GAAG,EAAE,cAAc,cAAc,CAAC,CAAC;AAAA,IAC3G,IAAI,MAAM,cAAc,KAAK,mBAAmB,QAAU,QAAQ,OAAO,GAAG,EAAE,cAAc,cAAc,CAAC,CAAC;AAAA,IAC5G,KAAK,MAAM,cAAc,KAAK,mBAAmB,QAAU,QAAQ,OAAO,GAAG,EAAE,cAAc,cAAc,CAAC,CAAC;AAAA,IAC7G,MAAM,MAAM,cAAc,KAAK,mBAAmB,QAAU,QAAQ,OAAO,GAAG,EAAE,cAAc,aAAa,CAAC,CAAC;AAAA,EAC/G;AACA,SAAO,UAAU,QAAQ,cAAc,CAAC,OAAO,OAAO;AACpD,QAAI,KAAK;AACT,YAAQ,KAAK,MAAM,OAAO,MAAM,MAAM,QAAQ,KAAK,MAAM,OAAO,SAAS,IAAI,KAAK,OAAO,MAAM,OAAO,KAAK;AAAA,EAC7G,CAAC;AACH;AACA,SAAS,cAAc,MAAM;AAC3B,MAAI,SAAS;AACX,WAAO,IAAI,KAAK,OAAO,GAAG;AAC5B,MAAI,SAAS;AACX,WAAuB,oBAAI,KAAK;AAClC,MAAI,gBAAgB;AAClB,WAAO,IAAI,KAAK,IAAI;AACtB,MAAI,OAAO,SAAS,YAAY,CAAC,MAAM,KAAK,IAAI,GAAG;AACjD,UAAM,IAAI,KAAK,MAAM,WAAW;AAChC,QAAI,GAAG;AACL,YAAM,IAAI,EAAE,CAAC,IAAI,KAAK;AACtB,YAAM,MAAM,EAAE,CAAC,KAAK,KAAK,UAAU,GAAG,CAAC;AACvC,aAAO,IAAI,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,GAAG,EAAE;AAAA,IACzE;AAAA,EACF;AACA,SAAO,IAAI,KAAK,IAAI;AACtB;AACA,SAAS,cAAc,MAAM,YAAY,YAAY,UAAU,CAAC,GAAG;AACjE,SAAO,SAAS,MAAM,WAAW,cAAc,QAAU,IAAI,CAAC,GAAG,QAAU,SAAS,GAAG,OAAO,CAAC;AACjG;AAEA,SAAS,cAAc,IAAI,WAAW,KAAK,UAAU,CAAC,GAAG;AACvD,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,oBAAoB;AAAA,EACtB,IAAI;AACJ,MAAI,QAAQ;AACZ,QAAM,WAAW,WAAW,KAAK;AACjC,WAAS,QAAQ;AACf,QAAI,OAAO;AACT,oBAAc,KAAK;AACnB,cAAQ;AAAA,IACV;AAAA,EACF;AACA,WAAS,QAAQ;AACf,aAAS,QAAQ;AACjB,UAAM;AAAA,EACR;AACA,WAAS,SAAS;AAChB,UAAM,gBAAgB,QAAU,QAAQ;AACxC,QAAI,iBAAiB;AACnB;AACF,aAAS,QAAQ;AACjB,QAAI;AACF,SAAG;AACL,UAAM;AACN,QAAI,SAAS;AACX,cAAQ,YAAY,IAAI,aAAa;AAAA,EACzC;AACA,MAAI,aAAa;AACf,WAAO;AACT,MAAI,MAAM,QAAQ,KAAK,OAAO,aAAa,YAAY;AACrD,UAAM,YAAY,MAAM,UAAU,MAAM;AACtC,UAAI,SAAS,SAAS;AACpB,eAAO;AAAA,IACX,CAAC;AACD,sBAAkB,SAAS;AAAA,EAC7B;AACA,oBAAkB,KAAK;AACvB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,YAAY,WAAW,KAAK,UAAU,CAAC,GAAG;AACjD,QAAM;AAAA,IACJ,UAAU,iBAAiB;AAAA,IAC3B,YAAY;AAAA,IACZ;AAAA,EACF,IAAI;AACJ,QAAM,UAAU,WAAW,CAAC;AAC5B,QAAM,SAAS,MAAM,QAAQ,SAAS;AACtC,QAAM,QAAQ,MAAM;AAClB,YAAQ,QAAQ;AAAA,EAClB;AACA,QAAM,WAAW;AAAA,IACf,WAAW,MAAM;AACf,aAAO;AACP,eAAS,QAAQ,KAAK;AAAA,IACxB,IAAI;AAAA,IACJ;AAAA,IACA,EAAE,UAAU;AAAA,EACd;AACA,MAAI,gBAAgB;AAClB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACL;AAAA,EACF,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,SAAS,eAAe,QAAQ,UAAU,CAAC,GAAG;AAC5C,MAAI;AACJ,QAAM,KAAK,YAAY,KAAK,QAAQ,iBAAiB,OAAO,KAAK,IAAI;AACrE;AAAA,IACE;AAAA,IACA,MAAM,GAAG,QAAQ,UAAU;AAAA,IAC3B;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,aAAa,IAAI,UAAU,UAAU,CAAC,GAAG;AAChD,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,oBAAoB;AAAA,EACtB,IAAI;AACJ,QAAM,YAAY,WAAW,KAAK;AAClC,MAAI,QAAQ;AACZ,WAAS,QAAQ;AACf,QAAI,OAAO;AACT,mBAAa,KAAK;AAClB,cAAQ;AAAA,IACV;AAAA,EACF;AACA,WAAS,OAAO;AACd,cAAU,QAAQ;AAClB,UAAM;AAAA,EACR;AACA,WAAS,SAAS,MAAM;AACtB,QAAI;AACF,SAAG;AACL,UAAM;AACN,cAAU,QAAQ;AAClB,YAAQ,WAAW,MAAM;AACvB,gBAAU,QAAQ;AAClB,cAAQ;AACR,SAAG,GAAG,IAAI;AAAA,IACZ,GAAG,QAAU,QAAQ,CAAC;AAAA,EACxB;AACA,MAAI,WAAW;AACb,cAAU,QAAQ;AAClB,QAAI;AACF,YAAM;AAAA,EACV;AACA,oBAAkB,IAAI;AACtB,SAAO;AAAA,IACL,WAAW,SAAS,SAAS;AAAA,IAC7B;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,WAAW,WAAW,KAAK,UAAU,CAAC,GAAG;AAChD,QAAM;AAAA,IACJ,UAAU,iBAAiB;AAAA,IAC3B;AAAA,EACF,IAAI;AACJ,QAAM,WAAW;AAAA,IACf,YAAY,OAAO,WAAW;AAAA,IAC9B;AAAA,IACA;AAAA,EACF;AACA,QAAM,QAAQ,SAAS,MAAM,CAAC,SAAS,UAAU,KAAK;AACtD,MAAI,gBAAgB;AAClB,WAAO;AAAA,MACL;AAAA,MACA,GAAG;AAAA,IACL;AAAA,EACF,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,SAAS,YAAY,OAAO,UAAU,CAAC,GAAG;AACxC,QAAM;AAAA,IACJ,SAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO,SAAS,MAAM;AACpB,QAAI,WAAW,QAAU,KAAK;AAC9B,QAAI,OAAO,WAAW;AACpB,iBAAW,OAAO,QAAQ;AAAA,aACnB,OAAO,aAAa;AAC3B,iBAAW,OAAO,MAAM,EAAE,UAAU,KAAK;AAC3C,QAAI,aAAa,OAAO,MAAM,QAAQ;AACpC,iBAAW;AACb,WAAO;AAAA,EACT,CAAC;AACH;AAEA,SAAS,YAAY,OAAO;AAC1B,SAAO,SAAS,MAAM,GAAG,QAAU,KAAK,CAAC,EAAE;AAC7C;AAEA,SAAS,UAAU,eAAe,OAAO,UAAU,CAAC,GAAG;AACrD,QAAM;AAAA,IACJ,cAAc;AAAA,IACd,aAAa;AAAA,EACf,IAAI;AACJ,QAAM,aAAa,MAAM,YAAY;AACrC,QAAM,SAAS,WAAW,YAAY;AACtC,WAAS,OAAO,OAAO;AACrB,QAAI,UAAU,QAAQ;AACpB,aAAO,QAAQ;AACf,aAAO,OAAO;AAAA,IAChB,OAAO;AACL,YAAM,SAAS,QAAU,WAAW;AACpC,aAAO,QAAQ,OAAO,UAAU,SAAS,QAAU,UAAU,IAAI;AACjE,aAAO,OAAO;AAAA,IAChB;AAAA,EACF;AACA,MAAI;AACF,WAAO;AAAA;AAEP,WAAO,CAAC,QAAQ,MAAM;AAC1B;AAEA,SAAS,WAAW,QAAQ,IAAI,SAAS;AACvC,MAAI,WAAW,WAAW,OAAO,SAAS,QAAQ,aAAa,CAAC,IAAI,CAAC,GAAG,OAAO,WAAW,aAAa,OAAO,IAAI,MAAM,QAAQ,MAAM,IAAI,SAAS,QAAU,MAAM,CAAC;AACpK,SAAO,MAAM,QAAQ,CAAC,SAAS,GAAG,cAAc;AAC9C,UAAM,iBAAiB,MAAM,KAAK,EAAE,QAAQ,QAAQ,OAAO,CAAC;AAC5D,UAAM,QAAQ,CAAC;AACf,eAAW,OAAO,SAAS;AACzB,UAAI,QAAQ;AACZ,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,YAAI,CAAC,eAAe,CAAC,KAAK,QAAQ,QAAQ,CAAC,GAAG;AAC5C,yBAAe,CAAC,IAAI;AACpB,kBAAQ;AACR;AAAA,QACF;AAAA,MACF;AACA,UAAI,CAAC;AACH,cAAM,KAAK,GAAG;AAAA,IAClB;AACA,UAAM,UAAU,QAAQ,OAAO,CAAC,IAAI,MAAM,CAAC,eAAe,CAAC,CAAC;AAC5D,OAAG,SAAS,SAAS,OAAO,SAAS,SAAS;AAC9C,cAAU,CAAC,GAAG,OAAO;AAAA,EACvB,GAAG,OAAO;AACZ;AAEA,SAAS,YAAY,QAAQ,IAAI,SAAS;AACxC,QAAM;AAAA,IACJ;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,UAAU,WAAW,CAAC;AAC5B,QAAM,OAAO;AAAA,IACX;AAAA,IACA,IAAI,SAAS;AACX,cAAQ,SAAS;AACjB,UAAI,QAAQ,SAAS,QAAU,KAAK;AAClC,iBAAS,MAAM,KAAK,CAAC;AACvB,SAAG,GAAG,IAAI;AAAA,IACZ;AAAA,IACA;AAAA,EACF;AACA,SAAO,EAAE,OAAO,SAAS,KAAK;AAChC;AAEA,SAAS,eAAe,QAAQ,IAAI,UAAU,CAAC,GAAG;AAChD,QAAM;AAAA,IACJ,WAAW;AAAA,IACX,UAAU;AAAA,IACV,GAAG;AAAA,EACL,IAAI;AACJ,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH,aAAa,eAAe,UAAU,EAAE,QAAQ,CAAC;AAAA,IACnD;AAAA,EACF;AACF;AAEA,SAAS,UAAU,QAAQ,IAAI,SAAS;AACtC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH,MAAM;AAAA,IACR;AAAA,EACF;AACF;AAEA,SAAS,eAAe,QAAQ,IAAI,UAAU,CAAC,GAAG;AAChD,QAAM;AAAA,IACJ,cAAc;AAAA,IACd,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,aAAa;AAAA,IACjB;AAAA,IACA;AAAA,EACF;AACA,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,aAAa,UAAU,QAAQ;AACjC,UAAM,SAAS,WAAW,KAAK;AAC/B,6BAAyB,MAAM;AAAA,IAC/B;AACA,oBAAgB,CAAC,YAAY;AAC3B,aAAO,QAAQ;AACf,cAAQ;AACR,aAAO,QAAQ;AAAA,IACjB;AACA,WAAO;AAAA,MACL;AAAA,MACA,IAAI,SAAS;AACX,YAAI,CAAC,OAAO;AACV,qBAAW,GAAG,IAAI;AAAA,MACtB;AAAA,MACA;AAAA,IACF;AAAA,EACF,OAAO;AACL,UAAM,cAAc,CAAC;AACrB,UAAM,gBAAgB,WAAW,CAAC;AAClC,UAAM,cAAc,WAAW,CAAC;AAChC,6BAAyB,MAAM;AAC7B,oBAAc,QAAQ,YAAY;AAAA,IACpC;AACA,gBAAY;AAAA,MACV;AAAA,QACE;AAAA,QACA,MAAM;AACJ,sBAAY;AAAA,QACd;AAAA,QACA,EAAE,GAAG,cAAc,OAAO,OAAO;AAAA,MACnC;AAAA,IACF;AACA,oBAAgB,CAAC,YAAY;AAC3B,YAAM,kBAAkB,YAAY;AACpC,cAAQ;AACR,oBAAc,SAAS,YAAY,QAAQ;AAAA,IAC7C;AACA,gBAAY;AAAA,MACV;AAAA,QACE;AAAA,QACA,IAAI,SAAS;AACX,gBAAM,SAAS,cAAc,QAAQ,KAAK,cAAc,UAAU,YAAY;AAC9E,wBAAc,QAAQ;AACtB,sBAAY,QAAQ;AACpB,cAAI;AACF;AACF,qBAAW,GAAG,IAAI;AAAA,QACpB;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO,MAAM;AACX,kBAAY,QAAQ,CAAC,OAAO,GAAG,CAAC;AAAA,IAClC;AAAA,EACF;AACA,SAAO,EAAE,MAAM,eAAe,uBAAuB;AACvD;AAEA,SAAS,eAAe,QAAQ,IAAI,SAAS;AAC3C,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH,WAAW;AAAA,IACb;AAAA,EACF;AACF;AAEA,SAAS,UAAU,QAAQ,IAAI,SAAS;AACtC,QAAM,OAAO,MAAM,QAAQ,IAAI,SAAS;AACtC,aAAS,MAAM,KAAK,CAAC;AACrB,WAAO,GAAG,GAAG,IAAI;AAAA,EACnB,GAAG,OAAO;AACV,SAAO;AACT;AAEA,SAAS,eAAe,QAAQ,IAAI,UAAU,CAAC,GAAG;AAChD,QAAM;AAAA,IACJ,WAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU;AAAA,IACV,GAAG;AAAA,EACL,IAAI;AACJ,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH,aAAa,eAAe,UAAU,UAAU,OAAO;AAAA,IACzD;AAAA,EACF;AACF;AAEA,SAAS,iBAAiB,QAAQ,IAAI,UAAU,CAAC,GAAG;AAClD,MAAI;AACJ,WAAS,WAAW;AAClB,QAAI,CAAC;AACH;AACF,UAAM,KAAK;AACX,gBAAY;AACZ,OAAG;AAAA,EACL;AACA,WAAS,UAAU,UAAU;AAC3B,gBAAY;AAAA,EACd;AACA,QAAM,MAAM,CAAC,OAAO,aAAa;AAC/B,aAAS;AACT,WAAO,GAAG,OAAO,UAAU,SAAS;AAAA,EACtC;AACA,QAAM,MAAM,eAAe,QAAQ,KAAK,OAAO;AAC/C,QAAM,EAAE,cAAc,IAAI;AAC1B,QAAM,UAAU,MAAM;AACpB,QAAI;AACJ,kBAAc,MAAM;AAClB,aAAO,IAAI,gBAAgB,MAAM,GAAG,YAAY,MAAM,CAAC;AAAA,IACzD,CAAC;AACD,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,GAAG;AAAA,IACH;AAAA,EACF;AACF;AACA,SAAS,gBAAgB,SAAS;AAChC,MAAI,WAAW,OAAO;AACpB,WAAO;AACT,MAAI,MAAM,QAAQ,OAAO;AACvB,WAAO,QAAQ,IAAI,CAAC,SAAS,QAAU,IAAI,CAAC;AAC9C,SAAO,QAAU,OAAO;AAC1B;AACA,SAAS,YAAY,QAAQ;AAC3B,SAAO,MAAM,QAAQ,MAAM,IAAI,OAAO,IAAI,MAAM,MAAM,IAAI;AAC5D;AAEA,SAAS,SAAS,QAAQ,IAAI,SAAS;AACrC,QAAM,OAAO;AAAA,IACX;AAAA,IACA,CAAC,GAAG,IAAI,iBAAiB;AACvB,UAAI,GAAG;AACL,YAAI,WAAW,OAAO,SAAS,QAAQ;AACrC,mBAAS,MAAM,KAAK,CAAC;AACvB,WAAG,GAAG,IAAI,YAAY;AAAA,MACxB;AAAA,IACF;AAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH,MAAM;AAAA,IACR;AAAA,EACF;AACA,SAAO;AACT;;;ACnkDA,SAAS,cAAc,oBAAoB,cAAc,cAAc;AACrE,MAAI;AACJ,MAAI,MAAM,YAAY,GAAG;AACvB,cAAU;AAAA,MACR,YAAY;AAAA,IACd;AAAA,EACF,OAAO;AACL,cAAU,gBAAgB,CAAC;AAAA,EAC7B;AACA,QAAM;AAAA,IACJ,OAAO;AAAA,IACP,aAAa;AAAA,IACb,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,IAAI;AACJ,QAAM,UAAU,WAAW,CAAC,IAAI;AAChC,QAAM,UAAU,UAAU,WAAW,YAAY,IAAI,IAAI,YAAY;AACrE,MAAI,UAAU;AACd,cAAY,OAAO,iBAAiB;AAClC,QAAI,CAAC,QAAQ;AACX;AACF;AACA,UAAM,qBAAqB;AAC3B,QAAI,cAAc;AAClB,QAAI,YAAY;AACd,cAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,mBAAW,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH;AACA,QAAI;AACF,YAAM,SAAS,MAAM,mBAAmB,CAAC,mBAAmB;AAC1D,qBAAa,MAAM;AACjB,cAAI;AACF,uBAAW,QAAQ;AACrB,cAAI,CAAC;AACH,2BAAe;AAAA,QACnB,CAAC;AAAA,MACH,CAAC;AACD,UAAI,uBAAuB;AACzB,gBAAQ,QAAQ;AAAA,IACpB,SAAS,GAAG;AACV,cAAQ,CAAC;AAAA,IACX,UAAE;AACA,UAAI,cAAc,uBAAuB;AACvC,mBAAW,QAAQ;AACrB,oBAAc;AAAA,IAChB;AAAA,EACF,CAAC;AACD,MAAI,MAAM;AACR,WAAO,SAAS,MAAM;AACpB,cAAQ,QAAQ;AAChB,aAAO,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,SAAS,eAAe,KAAK,SAAS,eAAe,uBAAuB;AAC1E,MAAI,SAAS,OAAO,GAAG;AACvB,MAAI;AACF,aAAS,OAAO,KAAK,aAAa;AACpC,MAAI;AACF,aAAS,OAAO,KAAK,eAAe,qBAAqB;AAC3D,MAAI,OAAO,YAAY,YAAY;AACjC,WAAO,SAAS,CAAC,QAAQ,QAAQ,QAAQ,GAAG,CAAC;AAAA,EAC/C,OAAO;AACL,WAAO,SAAS;AAAA,MACd,KAAK,CAAC,QAAQ,QAAQ,IAAI,QAAQ,GAAG;AAAA,MACrC,KAAK,QAAQ;AAAA,IACf,CAAC;AAAA,EACH;AACF;AAEA,SAAS,uBAAuB,UAAU,CAAC,GAAG;AAC5C,QAAM;AAAA,IACJ,eAAe;AAAA,EACjB,IAAI;AACJ,QAAM,SAAS,WAAW;AAC1B,QAAM,SAAuB,gBAAgB;AAAA,IAC3C,MAAM,GAAG,EAAE,MAAM,GAAG;AAClB,aAAO,MAAM;AACX,eAAO,QAAQ,MAAM;AAAA,MACvB;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,QAAsB,gBAAgB;AAAA,IAC1C;AAAA,IACA,OAAO,QAAQ;AAAA,IACf,MAAM,OAAO,EAAE,OAAO,MAAM,GAAG;AAC7B,aAAO,MAAM;AACX,YAAI;AACJ,YAAI,CAAC,OAAO,SAAS;AACnB,gBAAM,IAAI,MAAM,6DAA6D;AAC/E,cAAM,SAAS,KAAK,OAAO,UAAU,OAAO,SAAS,GAAG,KAAK,QAAQ;AAAA,UACnE,GAAG,QAAQ,SAAS,OAAO,qBAAqB,KAAK,IAAI;AAAA,UACzD,QAAQ;AAAA,QACV,CAAC;AACD,eAAO,iBAAiB,SAAS,OAAO,SAAS,MAAM,YAAY,IAAI,MAAM,CAAC,IAAI;AAAA,MACpF;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL,EAAE,QAAQ,MAAM;AAAA,IAChB,CAAC,QAAQ,KAAK;AAAA,EAChB;AACF;AACA,SAAS,qBAAqB,KAAK;AACjC,QAAM,SAAS,CAAC;AAChB,aAAW,OAAO;AAChB,WAAO,SAAS,GAAG,CAAC,IAAI,IAAI,GAAG;AACjC,SAAO;AACT;AAEA,SAAS,sBAAsB,UAAU,CAAC,GAAG;AAC3C,MAAI,QAAQ;AACZ,QAAM,YAAY,IAAI,CAAC,CAAC;AACxB,WAAS,UAAU,MAAM;AACvB,UAAM,QAAQ,gBAAgB;AAAA,MAC5B,KAAK;AAAA,MACL;AAAA,MACA,SAAS;AAAA,MACT,SAAS,MAAM;AAAA,MACf;AAAA,MACA,QAAQ,MAAM;AAAA,MACd;AAAA,MACA,aAAa;AAAA,MACb;AAAA,IACF,CAAC;AACD,cAAU,MAAM,KAAK,KAAK;AAC1B,UAAM,UAAU,IAAI,QAAQ,CAAC,UAAU,YAAY;AACjD,YAAM,UAAU,CAAC,MAAM;AACrB,cAAM,cAAc;AACpB,eAAO,SAAS,CAAC;AAAA,MACnB;AACA,YAAM,SAAS;AAAA,IACjB,CAAC,EAAE,QAAQ,MAAM;AACf,YAAM,UAAU;AAChB,YAAM,SAAS,UAAU,MAAM,QAAQ,KAAK;AAC5C,UAAI,WAAW;AACb,kBAAU,MAAM,OAAO,QAAQ,CAAC;AAAA,IACpC,CAAC;AACD,WAAO,MAAM;AAAA,EACf;AACA,WAAS,SAAS,MAAM;AACtB,QAAI,QAAQ,aAAa,UAAU,MAAM,SAAS;AAChD,aAAO,UAAU,MAAM,CAAC,EAAE;AAC5B,WAAO,OAAO,GAAG,IAAI;AAAA,EACvB;AACA,QAAM,YAA0B,gBAAgB,CAAC,GAAG,EAAE,MAAM,MAAM;AAChE,UAAM,aAAa,MAAM,UAAU,MAAM,IAAI,CAAC,UAAU;AACtD,UAAI;AACJ,aAAO,EAAE,UAAU,EAAE,KAAK,MAAM,IAAI,IAAI,KAAK,MAAM,YAAY,OAAO,SAAS,GAAG,KAAK,OAAO,KAAK,CAAC;AAAA,IACtG,CAAC;AACD,QAAI,QAAQ;AACV,aAAO,MAAM,EAAE,iBAAiB,QAAQ,YAAY,UAAU;AAChE,WAAO;AAAA,EACT,CAAC;AACD,YAAU,QAAQ;AAClB,SAAO;AACT;AAEA,SAAS,cAAc,IAAI;AACzB,SAAO,YAAY,MAAM;AACvB,WAAO,GAAG,MAAM,MAAM,KAAK,IAAI,CAAC,MAAM,QAAQ,CAAC,CAAC,CAAC;AAAA,EACnD;AACF;AAEA,IAAM,gBAAgB,WAAW,SAAS;AAC1C,IAAM,kBAAkB,WAAW,OAAO,WAAW;AACrD,IAAM,mBAAmB,WAAW,OAAO,YAAY;AACvD,IAAM,kBAAkB,WAAW,OAAO,WAAW;AAErD,SAAS,aAAa,OAAO;AAC3B,MAAI;AACJ,QAAM,QAAQ,QAAQ,KAAK;AAC3B,UAAQ,KAAK,SAAS,OAAO,SAAS,MAAM,QAAQ,OAAO,KAAK;AAClE;AAEA,SAAS,oBAAoB,MAAM;AACjC,QAAM,WAAW,CAAC;AAClB,QAAM,UAAU,MAAM;AACpB,aAAS,QAAQ,CAAC,OAAO,GAAG,CAAC;AAC7B,aAAS,SAAS;AAAA,EACpB;AACA,QAAM,WAAW,CAAC,IAAI,OAAO,UAAU,YAAY;AACjD,OAAG,iBAAiB,OAAO,UAAU,OAAO;AAC5C,WAAO,MAAM,GAAG,oBAAoB,OAAO,UAAU,OAAO;AAAA,EAC9D;AACA,QAAM,oBAAoB,SAAS,MAAM;AACvC,UAAM,OAAO,QAAQ,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,KAAK,IAAI;AAC9D,WAAO,KAAK,MAAM,CAAC,MAAM,OAAO,MAAM,QAAQ,IAAI,OAAO;AAAA,EAC3D,CAAC;AACD,QAAM,YAAY;AAAA,IAChB,MAAM;AACJ,UAAI,IAAI;AACR,aAAO;AAAA,SACJ,MAAM,KAAK,kBAAkB,UAAU,OAAO,SAAS,GAAG,IAAI,CAAC,MAAM,aAAa,CAAC,CAAC,MAAM,OAAO,KAAK,CAAC,aAAa,EAAE,OAAO,CAAC,MAAM,KAAK,IAAI;AAAA,QAC9I,QAAQ,QAAQ,kBAAkB,QAAQ,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC;AAAA,QAC5D,QAAQ,MAAM,kBAAkB,QAAQ,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC;AAAA;AAAA,QAE1D,QAAQ,kBAAkB,QAAQ,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC;AAAA,MACrD;AAAA,IACF;AAAA,IACA,CAAC,CAAC,aAAa,YAAY,eAAe,WAAW,MAAM;AACzD,cAAQ;AACR,UAAI,EAAE,eAAe,OAAO,SAAS,YAAY,WAAW,EAAE,cAAc,OAAO,SAAS,WAAW,WAAW,EAAE,iBAAiB,OAAO,SAAS,cAAc;AACjK;AACF,YAAM,eAAe,SAAS,WAAW,IAAI,EAAE,GAAG,YAAY,IAAI;AAClE,eAAS;AAAA,QACP,GAAG,YAAY;AAAA,UACb,CAAC,OAAO,WAAW;AAAA,YACjB,CAAC,UAAU,cAAc,IAAI,CAAC,aAAa,SAAS,IAAI,OAAO,UAAU,YAAY,CAAC;AAAA,UACxF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,EAAE,OAAO,OAAO;AAAA,EAClB;AACA,QAAM,OAAO,MAAM;AACjB,cAAU;AACV,YAAQ;AAAA,EACV;AACA,oBAAkB,OAAO;AACzB,SAAO;AACT;AAEA,IAAI,iBAAiB;AACrB,SAAS,eAAe,QAAQ,SAAS,UAAU,CAAC,GAAG;AACrD,QAAM,EAAE,QAAAO,UAAS,eAAe,SAAS,CAAC,GAAG,UAAU,MAAM,eAAe,OAAO,WAAW,MAAM,IAAI;AACxG,MAAI,CAACA,SAAQ;AACX,WAAO,WAAW,EAAE,MAAM,MAAM,QAAQ,MAAM,SAAS,KAAK,IAAI;AAAA,EAClE;AACA,MAAI,SAAS,CAAC,gBAAgB;AAC5B,qBAAiB;AACjB,UAAM,kBAAkB,EAAE,SAAS,KAAK;AACxC,UAAM,KAAKA,QAAO,SAAS,KAAK,QAAQ,EAAE,QAAQ,CAAC,OAAO,iBAAiB,IAAI,SAAS,MAAM,eAAe,CAAC;AAC9G,qBAAiBA,QAAO,SAAS,iBAAiB,SAAS,MAAM,eAAe;AAAA,EAClF;AACA,MAAI,eAAe;AACnB,QAAM,eAAe,CAAC,UAAU;AAC9B,WAAO,QAAQ,MAAM,EAAE,KAAK,CAAC,YAAY;AACvC,UAAI,OAAO,YAAY,UAAU;AAC/B,eAAO,MAAM,KAAKA,QAAO,SAAS,iBAAiB,OAAO,CAAC,EAAE,KAAK,CAAC,OAAO,OAAO,MAAM,UAAU,MAAM,aAAa,EAAE,SAAS,EAAE,CAAC;AAAA,MACpI,OAAO;AACL,cAAM,KAAK,aAAa,OAAO;AAC/B,eAAO,OAAO,MAAM,WAAW,MAAM,MAAM,aAAa,EAAE,SAAS,EAAE;AAAA,MACvE;AAAA,IACF,CAAC;AAAA,EACH;AACA,WAAS,iBAAiB,SAAS;AACjC,UAAM,KAAK,QAAQ,OAAO;AAC1B,WAAO,MAAM,GAAG,EAAE,QAAQ,cAAc;AAAA,EAC1C;AACA,WAAS,mBAAmB,SAAS,OAAO;AAC1C,UAAM,KAAK,QAAQ,OAAO;AAC1B,UAAM,WAAW,GAAG,EAAE,WAAW,GAAG,EAAE,QAAQ;AAC9C,QAAI,YAAY,QAAQ,CAAC,MAAM,QAAQ,QAAQ;AAC7C,aAAO;AACT,WAAO,SAAS,KAAK,CAAC,UAAU,MAAM,OAAO,MAAM,UAAU,MAAM,aAAa,EAAE,SAAS,MAAM,EAAE,CAAC;AAAA,EACtG;AACA,QAAM,WAAW,CAAC,UAAU;AAC1B,UAAM,KAAK,aAAa,MAAM;AAC9B,QAAI,MAAM,UAAU;AAClB;AACF,QAAI,EAAE,cAAc,YAAY,iBAAiB,MAAM,KAAK,mBAAmB,QAAQ,KAAK;AAC1F;AACF,QAAI,CAAC,MAAM,OAAO,MAAM,UAAU,MAAM,aAAa,EAAE,SAAS,EAAE;AAChE;AACF,QAAI,YAAY,SAAS,MAAM,WAAW;AACxC,qBAAe,CAAC,aAAa,KAAK;AACpC,QAAI,CAAC,cAAc;AACjB,qBAAe;AACf;AAAA,IACF;AACA,YAAQ,KAAK;AAAA,EACf;AACA,MAAI,oBAAoB;AACxB,QAAM,UAAU;AAAA,IACd,iBAAiBA,SAAQ,SAAS,CAAC,UAAU;AAC3C,UAAI,CAAC,mBAAmB;AACtB,4BAAoB;AACpB,mBAAW,MAAM;AACf,8BAAoB;AAAA,QACtB,GAAG,CAAC;AACJ,iBAAS,KAAK;AAAA,MAChB;AAAA,IACF,GAAG,EAAE,SAAS,MAAM,QAAQ,CAAC;AAAA,IAC7B,iBAAiBA,SAAQ,eAAe,CAAC,MAAM;AAC7C,YAAM,KAAK,aAAa,MAAM;AAC9B,qBAAe,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,aAAa,EAAE,SAAS,EAAE;AAAA,IAC3E,GAAG,EAAE,SAAS,KAAK,CAAC;AAAA,IACpB,gBAAgB,iBAAiBA,SAAQ,QAAQ,CAAC,UAAU;AAC1D,iBAAW,MAAM;AACf,YAAI;AACJ,cAAM,KAAK,aAAa,MAAM;AAC9B,cAAM,KAAKA,QAAO,SAAS,kBAAkB,OAAO,SAAS,GAAG,aAAa,YAAY,EAAE,MAAM,OAAO,SAAS,GAAG,SAASA,QAAO,SAAS,aAAa,IAAI;AAC5J,kBAAQ,KAAK;AAAA,QACf;AAAA,MACF,GAAG,CAAC;AAAA,IACN,GAAG,EAAE,SAAS,KAAK,CAAC;AAAA,EACtB,EAAE,OAAO,OAAO;AAChB,QAAM,OAAO,MAAM,QAAQ,QAAQ,CAAC,OAAO,GAAG,CAAC;AAC/C,MAAI,UAAU;AACZ,WAAO;AAAA,MACL;AAAA,MACA,QAAQ,MAAM;AACZ,uBAAe;AAAA,MACjB;AAAA,MACA,SAAS,CAAC,UAAU;AAClB,uBAAe;AACf,iBAAS,KAAK;AACd,uBAAe;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,aAAa;AACpB,QAAM,YAAY,WAAW,KAAK;AAClC,QAAM,WAAW,mBAAmB;AACpC,MAAI,UAAU;AACZ,cAAU,MAAM;AACd,gBAAU,QAAQ;AAAA,IACpB,GAAG,QAAQ;AAAA,EACb;AACA,SAAO;AACT;AAEA,SAAS,aAAa,UAAU;AAC9B,QAAM,YAAY,WAAW;AAC7B,SAAO,SAAS,MAAM;AACpB,cAAU;AACV,WAAO,QAAQ,SAAS,CAAC;AAAA,EAC3B,CAAC;AACH;AAEA,SAAS,oBAAoB,QAAQ,UAAU,UAAU,CAAC,GAAG;AAC3D,QAAM,EAAE,QAAAA,UAAS,eAAe,GAAG,gBAAgB,IAAI;AACvD,MAAI;AACJ,QAAM,cAAc,aAAa,MAAMA,WAAU,sBAAsBA,OAAM;AAC7E,QAAM,UAAU,MAAM;AACpB,QAAI,UAAU;AACZ,eAAS,WAAW;AACpB,iBAAW;AAAA,IACb;AAAA,EACF;AACA,QAAM,UAAU,SAAS,MAAM;AAC7B,UAAM,QAAQ,QAAQ,MAAM;AAC5B,UAAM,QAAQ,QAAQ,KAAK,EAAE,IAAI,YAAY,EAAE,OAAO,UAAU;AAChE,WAAO,IAAI,IAAI,KAAK;AAAA,EACtB,CAAC;AACD,QAAM,YAAY;AAAA,IAChB,MAAM,QAAQ;AAAA,IACd,CAAC,aAAa;AACZ,cAAQ;AACR,UAAI,YAAY,SAAS,SAAS,MAAM;AACtC,mBAAW,IAAI,iBAAiB,QAAQ;AACxC,iBAAS,QAAQ,CAAC,OAAO,SAAS,QAAQ,IAAI,eAAe,CAAC;AAAA,MAChE;AAAA,IACF;AAAA,IACA,EAAE,WAAW,MAAM,OAAO,OAAO;AAAA,EACnC;AACA,QAAM,cAAc,MAAM;AACxB,WAAO,YAAY,OAAO,SAAS,SAAS,YAAY;AAAA,EAC1D;AACA,QAAM,OAAO,MAAM;AACjB,cAAU;AACV,YAAQ;AAAA,EACV;AACA,oBAAkB,IAAI;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,iBAAiB,QAAQ,UAAU,UAAU,CAAC,GAAG;AACxD,QAAM;AAAA,IACJ,QAAAA,UAAS;AAAA,IACT,UAAAC,YAAWD,WAAU,OAAO,SAASA,QAAO;AAAA,IAC5C,QAAQ;AAAA,EACV,IAAI;AACJ,MAAI,CAACA,WAAU,CAACC;AACd,WAAO;AACT,MAAI;AACJ,QAAM,mBAAmB,CAAC,OAAO;AAC/B,cAAU,OAAO,SAAS,OAAO;AACjC,aAAS;AAAA,EACX;AACA,QAAM,YAAY,YAAY,MAAM;AAClC,UAAM,KAAK,aAAa,MAAM;AAC9B,QAAI,IAAI;AACN,YAAM,EAAE,KAAK,IAAI;AAAA,QACfA;AAAA,QACA,CAAC,kBAAkB;AACjB,gBAAM,gBAAgB,cAAc,IAAI,CAAC,aAAa,CAAC,GAAG,SAAS,YAAY,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,SAAS,SAAS,MAAM,KAAK,SAAS,EAAE,CAAC;AACxI,cAAI,eAAe;AACjB,qBAAS,aAAa;AAAA,UACxB;AAAA,QACF;AAAA,QACA;AAAA,UACE,QAAAD;AAAA,UACA,WAAW;AAAA,UACX,SAAS;AAAA,QACX;AAAA,MACF;AACA,uBAAiB,IAAI;AAAA,IACvB;AAAA,EACF,GAAG,EAAE,MAAM,CAAC;AACZ,QAAM,aAAa,MAAM;AACvB,cAAU;AACV,qBAAiB;AAAA,EACnB;AACA,oBAAkB,UAAU;AAC5B,SAAO;AACT;AAEA,SAAS,mBAAmB,WAAW;AACrC,MAAI,OAAO,cAAc;AACvB,WAAO;AAAA,WACA,OAAO,cAAc;AAC5B,WAAO,CAAC,UAAU,MAAM,QAAQ;AAAA,WACzB,MAAM,QAAQ,SAAS;AAC9B,WAAO,CAAC,UAAU,UAAU,SAAS,MAAM,GAAG;AAChD,SAAO,MAAM;AACf;AACA,SAAS,eAAe,MAAM;AAC5B,MAAI;AACJ,MAAI;AACJ,MAAI,UAAU,CAAC;AACf,MAAI,KAAK,WAAW,GAAG;AACrB,UAAM,KAAK,CAAC;AACZ,cAAU,KAAK,CAAC;AAChB,cAAU,KAAK,CAAC;AAAA,EAClB,WAAW,KAAK,WAAW,GAAG;AAC5B,QAAI,OAAO,KAAK,CAAC,MAAM,UAAU;AAC/B,YAAM;AACN,gBAAU,KAAK,CAAC;AAChB,gBAAU,KAAK,CAAC;AAAA,IAClB,OAAO;AACL,YAAM,KAAK,CAAC;AACZ,gBAAU,KAAK,CAAC;AAAA,IAClB;AAAA,EACF,OAAO;AACL,UAAM;AACN,cAAU,KAAK,CAAC;AAAA,EAClB;AACA,QAAM;AAAA,IACJ,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,SAAS;AAAA,EACX,IAAI;AACJ,QAAM,YAAY,mBAAmB,GAAG;AACxC,QAAM,WAAW,CAAC,MAAM;AACtB,QAAI,EAAE,UAAU,QAAQ,MAAM;AAC5B;AACF,QAAI,UAAU,CAAC;AACb,cAAQ,CAAC;AAAA,EACb;AACA,SAAO,iBAAiB,QAAQ,WAAW,UAAU,OAAO;AAC9D;AACA,SAAS,UAAU,KAAK,SAAS,UAAU,CAAC,GAAG;AAC7C,SAAO,YAAY,KAAK,SAAS,EAAE,GAAG,SAAS,WAAW,UAAU,CAAC;AACvE;AACA,SAAS,aAAa,KAAK,SAAS,UAAU,CAAC,GAAG;AAChD,SAAO,YAAY,KAAK,SAAS,EAAE,GAAG,SAAS,WAAW,WAAW,CAAC;AACxE;AACA,SAAS,QAAQ,KAAK,SAAS,UAAU,CAAC,GAAG;AAC3C,SAAO,YAAY,KAAK,SAAS,EAAE,GAAG,SAAS,WAAW,QAAQ,CAAC;AACrE;AAEA,IAAM,gBAAgB;AACtB,IAAM,oBAAoB;AAC1B,SAAS,YAAY,QAAQ,SAAS,SAAS;AAC7C,MAAI,IAAI;AACR,QAAM,aAAa,SAAS,MAAM,aAAa,MAAM,CAAC;AACtD,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,iBAAiB;AACrB,WAAS,QAAQ;AACf,QAAI,SAAS;AACX,mBAAa,OAAO;AACpB,gBAAU;AAAA,IACZ;AACA,eAAW;AACX,qBAAiB;AACjB,qBAAiB;AAAA,EACnB;AACA,WAAS,UAAU,IAAI;AACrB,QAAI,KAAK,KAAK;AACd,UAAM,CAAC,iBAAiB,WAAW,eAAe,IAAI,CAAC,gBAAgB,UAAU,cAAc;AAC/F,UAAM;AACN,QAAI,EAAE,WAAW,OAAO,SAAS,QAAQ,cAAc,CAAC,aAAa,CAAC;AACpE;AACF,UAAM,MAAM,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,SAAS,IAAI,SAAS,GAAG,WAAW,WAAW;AACjH;AACF,SAAK,MAAM,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,SAAS,IAAI;AAC9E,SAAG,eAAe;AACpB,SAAK,KAAK,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,SAAS,GAAG;AAC5E,SAAG,gBAAgB;AACrB,UAAM,KAAK,GAAG,IAAI,UAAU;AAC5B,UAAM,KAAK,GAAG,IAAI,UAAU;AAC5B,UAAM,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAC5C,YAAQ,UAAU,GAAG,YAAY,iBAAiB,UAAU,eAAe;AAAA,EAC7E;AACA,WAAS,OAAO,IAAI;AAClB,QAAI,KAAK,KAAK,IAAI;AAClB,UAAM,MAAM,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,SAAS,IAAI,SAAS,GAAG,WAAW,WAAW;AACjH;AACF,UAAM;AACN,SAAK,MAAM,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,SAAS,IAAI;AAC9E,SAAG,eAAe;AACpB,SAAK,KAAK,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,SAAS,GAAG;AAC5E,SAAG,gBAAgB;AACrB,eAAW;AAAA,MACT,GAAG,GAAG;AAAA,MACN,GAAG,GAAG;AAAA,IACR;AACA,qBAAiB,GAAG;AACpB,cAAU;AAAA,MACR,MAAM;AACJ,yBAAiB;AACjB,gBAAQ,EAAE;AAAA,MACZ;AAAA,OACC,KAAK,WAAW,OAAO,SAAS,QAAQ,UAAU,OAAO,KAAK;AAAA,IACjE;AAAA,EACF;AACA,WAAS,OAAO,IAAI;AAClB,QAAI,KAAK,KAAK,IAAI;AAClB,UAAM,MAAM,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,SAAS,IAAI,SAAS,GAAG,WAAW,WAAW;AACjH;AACF,QAAI,CAAC,aAAa,WAAW,OAAO,SAAS,QAAQ,uBAAuB;AAC1E;AACF,SAAK,MAAM,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,SAAS,IAAI;AAC9E,SAAG,eAAe;AACpB,SAAK,KAAK,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,SAAS,GAAG;AAC5E,SAAG,gBAAgB;AACrB,UAAM,KAAK,GAAG,IAAI,SAAS;AAC3B,UAAM,KAAK,GAAG,IAAI,SAAS;AAC3B,UAAM,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAC5C,QAAI,cAAc,KAAK,WAAW,OAAO,SAAS,QAAQ,sBAAsB,OAAO,KAAK;AAC1F,YAAM;AAAA,EACV;AACA,QAAM,kBAAkB;AAAA,IACtB,UAAU,KAAK,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,SAAS,GAAG;AAAA,IACnF,OAAO,KAAK,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,SAAS,GAAG;AAAA,EAClF;AACA,QAAM,UAAU;AAAA,IACd,iBAAiB,YAAY,eAAe,QAAQ,eAAe;AAAA,IACnE,iBAAiB,YAAY,eAAe,QAAQ,eAAe;AAAA,IACnE,iBAAiB,YAAY,CAAC,aAAa,cAAc,GAAG,WAAW,eAAe;AAAA,EACxF;AACA,QAAM,OAAO,MAAM,QAAQ,QAAQ,CAAC,OAAO,GAAG,CAAC;AAC/C,SAAO;AACT;AAEA,SAAS,2BAA2B;AAClC,QAAM,EAAE,eAAe,KAAK,IAAI;AAChC,MAAI,CAAC;AACH,WAAO;AACT,MAAI,kBAAkB;AACpB,WAAO;AACT,UAAQ,cAAc,SAAS;AAAA,IAC7B,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,EACX;AACA,SAAO,cAAc,aAAa,iBAAiB;AACrD;AACA,SAAS,iBAAiB;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,MAAI,WAAW,WAAW;AACxB,WAAO;AACT,MAAI,WAAW,MAAM,WAAW,MAAM,WAAW,MAAM,WAAW;AAChE,WAAO;AACT,MAAI,WAAW,MAAM,WAAW;AAC9B,WAAO;AACT,SAAO;AACT;AACA,SAAS,cAAc,UAAU,UAAU,CAAC,GAAG;AAC7C,QAAM,EAAE,UAAU,YAAY,gBAAgB,IAAI;AAClD,QAAM,UAAU,CAAC,UAAU;AACzB,QAAI,CAAC,yBAAyB,KAAK,iBAAiB,KAAK,GAAG;AAC1D,eAAS,KAAK;AAAA,IAChB;AAAA,EACF;AACA,MAAI;AACF,qBAAiB,WAAW,WAAW,SAAS,EAAE,SAAS,KAAK,CAAC;AACrE;AAEA,SAAS,YAAY,KAAK,eAAe,MAAM;AAC7C,QAAM,WAAW,mBAAmB;AACpC,MAAI,WAAW,MAAM;AAAA,EACrB;AACA,QAAM,UAAU,UAAU,CAAC,OAAO,YAAY;AAC5C,eAAW;AACX,WAAO;AAAA,MACL,MAAM;AACJ,YAAI,IAAI;AACR,cAAM;AACN,gBAAQ,MAAM,KAAK,YAAY,OAAO,SAAS,SAAS,UAAU,OAAO,SAAS,GAAG,MAAM,GAAG,MAAM,OAAO,KAAK;AAAA,MAClH;AAAA,MACA,MAAM;AAAA,MACN;AAAA,IACF;AAAA,EACF,CAAC;AACD,eAAa,QAAQ;AACrB,YAAU,QAAQ;AAClB,SAAO;AACT;AAEA,SAAS,iBAAiB,UAAU,CAAC,GAAG;AACtC,MAAI;AACJ,QAAM;AAAA,IACJ,QAAAA,UAAS;AAAA,IACT,OAAO;AAAA,IACP,mBAAmB;AAAA,EACrB,IAAI;AACJ,QAAMC,aAAY,KAAK,QAAQ,aAAa,OAAO,KAAKD,WAAU,OAAO,SAASA,QAAO;AACzF,QAAM,uBAAuB,MAAM;AACjC,QAAI;AACJ,QAAI,UAAUC,aAAY,OAAO,SAASA,UAAS;AACnD,QAAI,MAAM;AACR,aAAO,WAAW,OAAO,SAAS,QAAQ;AACxC,mBAAW,MAAM,WAAW,OAAO,SAAS,QAAQ,eAAe,OAAO,SAAS,IAAI;AAAA,IAC3F;AACA,WAAO;AAAA,EACT;AACA,QAAM,gBAAgB,WAAW;AACjC,QAAM,UAAU,MAAM;AACpB,kBAAc,QAAQ,qBAAqB;AAAA,EAC7C;AACA,MAAID,SAAQ;AACV,UAAM,kBAAkB;AAAA,MACtB,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AACA;AAAA,MACEA;AAAA,MACA;AAAA,MACA,CAAC,UAAU;AACT,YAAI,MAAM,kBAAkB;AAC1B;AACF,gBAAQ;AAAA,MACV;AAAA,MACA;AAAA,IACF;AACA;AAAA,MACEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,MAAI,kBAAkB;AACpB,qBAAiB,eAAe,SAAS,EAAE,UAAAC,UAAS,CAAC;AAAA,EACvD;AACA,UAAQ;AACR,SAAO;AACT;AAEA,SAAS,SAAS,IAAI,UAAU,CAAC,GAAG;AAClC,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,QAAAD,UAAS;AAAA,IACT,OAAO;AAAA,EACT,IAAI;AACJ,QAAM,WAAW,WAAW,KAAK;AACjC,QAAM,gBAAgB,SAAS,MAAM;AACnC,WAAO,WAAW,MAAM,QAAQ,QAAQ,IAAI;AAAA,EAC9C,CAAC;AACD,MAAI,yBAAyB;AAC7B,MAAI,QAAQ;AACZ,WAAS,KAAKE,YAAW;AACvB,QAAI,CAAC,SAAS,SAAS,CAACF;AACtB;AACF,QAAI,CAAC;AACH,+BAAyBE;AAC3B,UAAM,QAAQA,aAAY;AAC1B,QAAI,cAAc,SAAS,QAAQ,cAAc,OAAO;AACtD,cAAQF,QAAO,sBAAsB,IAAI;AACzC;AAAA,IACF;AACA,6BAAyBE;AACzB,OAAG,EAAE,OAAO,WAAAA,WAAU,CAAC;AACvB,QAAI,MAAM;AACR,eAAS,QAAQ;AACjB,cAAQ;AACR;AAAA,IACF;AACA,YAAQF,QAAO,sBAAsB,IAAI;AAAA,EAC3C;AACA,WAAS,SAAS;AAChB,QAAI,CAAC,SAAS,SAASA,SAAQ;AAC7B,eAAS,QAAQ;AACjB,+BAAyB;AACzB,cAAQA,QAAO,sBAAsB,IAAI;AAAA,IAC3C;AAAA,EACF;AACA,WAAS,QAAQ;AACf,aAAS,QAAQ;AACjB,QAAI,SAAS,QAAQA,SAAQ;AAC3B,MAAAA,QAAO,qBAAqB,KAAK;AACjC,cAAQ;AAAA,IACV;AAAA,EACF;AACA,MAAI;AACF,WAAO;AACT,oBAAkB,KAAK;AACvB,SAAO;AAAA,IACL,UAAU,SAAS,QAAQ;AAAA,IAC3B;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,WAAW,QAAQ,WAAW,SAAS;AAC9C,MAAI;AACJ,MAAI;AACJ,MAAI,SAAS,OAAO,GAAG;AACrB,aAAS;AACT,qBAAiB,WAAW,SAAS,CAAC,UAAU,aAAa,gBAAgB,WAAW,WAAW,SAAS,CAAC;AAAA,EAC/G,OAAO;AACL,aAAS,EAAE,UAAU,QAAQ;AAC7B,qBAAiB;AAAA,EACnB;AACA,QAAM;AAAA,IACJ,QAAAA,UAAS;AAAA,IACT,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IACA,cAAc,gBAAgB;AAAA,IAC9B;AAAA,IACA,UAAU,CAAC,MAAM;AACf,cAAQ,MAAM,CAAC;AAAA,IACjB;AAAA,EACF,IAAI;AACJ,QAAM,cAAc,aAAa,MAAMA,WAAU,eAAe,aAAa,YAAY,SAAS;AAClG,QAAM,UAAU,WAAW,MAAM;AACjC,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,WAAW;AAAA,IACX,aAAa;AAAA,IACb,UAAU;AAAA,IACV,cAAc;AAAA,IACd,SAAS;AAAA,IACT,WAAW,YAAY,SAAS;AAAA,IAChC,cAAc;AAAA,EAChB,CAAC;AACD,QAAM,UAAU,SAAS,MAAM,MAAM,OAAO;AAC5C,QAAM,YAAY,SAAS,MAAM,MAAM,SAAS;AAChD,QAAM,eAAe,SAAS,MAAM,MAAM,YAAY;AACtD,QAAM,YAAY,SAAS;AAAA,IACzB,MAAM;AACJ,aAAO,MAAM;AAAA,IACf;AAAA,IACA,IAAI,OAAO;AACT,YAAM,YAAY;AAClB,UAAI,QAAQ;AACV,gBAAQ,MAAM,YAAY;AAAA,IAC9B;AAAA,EACF,CAAC;AACD,QAAM,cAAc,SAAS;AAAA,IAC3B,MAAM;AACJ,aAAO,MAAM;AAAA,IACf;AAAA,IACA,IAAI,OAAO;AACT,YAAM,cAAc;AACpB,UAAI,QAAQ,OAAO;AACjB,gBAAQ,MAAM,cAAc;AAC5B,mBAAW;AAAA,MACb;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,WAAW,SAAS;AAAA,IACxB,MAAM;AACJ,aAAO,MAAM;AAAA,IACf;AAAA,IACA,IAAI,OAAO;AACT,YAAM,WAAW;AACjB,UAAI,QAAQ;AACV,gBAAQ,MAAM,WAAW;AAAA,IAC7B;AAAA,EACF,CAAC;AACD,QAAM,eAAe,SAAS;AAAA,IAC5B,MAAM;AACJ,aAAO,MAAM;AAAA,IACf;AAAA,IACA,IAAI,OAAO;AACT,YAAM,eAAe;AACrB,UAAI,QAAQ;AACV,gBAAQ,MAAM,eAAe;AAAA,IACjC;AAAA,EACF,CAAC;AACD,QAAM,OAAO,MAAM;AACjB,QAAI,QAAQ,OAAO;AACjB,UAAI;AACF,gBAAQ,MAAM,KAAK;AACnB,mBAAW;AAAA,MACb,SAAS,GAAG;AACV,kBAAU;AACV,gBAAQ,CAAC;AAAA,MACX;AAAA,IACF,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,QAAQ,MAAM;AAClB,QAAI;AACJ,QAAI;AACF,OAAC,KAAK,QAAQ,UAAU,OAAO,SAAS,GAAG,MAAM;AACjD,gBAAU;AAAA,IACZ,SAAS,GAAG;AACV,cAAQ,CAAC;AAAA,IACX;AAAA,EACF;AACA,QAAM,UAAU,MAAM;AACpB,QAAI;AACJ,QAAI,CAAC,QAAQ;AACX,aAAO;AACT,QAAI;AACF,OAAC,KAAK,QAAQ,UAAU,OAAO,SAAS,GAAG,QAAQ;AACnD,iBAAW;AAAA,IACb,SAAS,GAAG;AACV,gBAAU;AACV,cAAQ,CAAC;AAAA,IACX;AAAA,EACF;AACA,QAAM,SAAS,MAAM;AACnB,QAAI;AACJ,QAAI;AACF,OAAC,KAAK,QAAQ,UAAU,OAAO,SAAS,GAAG,OAAO;AAClD,gBAAU;AAAA,IACZ,SAAS,GAAG;AACV,cAAQ,CAAC;AAAA,IACX;AAAA,EACF;AACA,QAAM,SAAS,MAAM;AACnB,QAAI;AACJ,QAAI;AACF,OAAC,KAAK,QAAQ,UAAU,OAAO,SAAS,GAAG,OAAO;AAClD,gBAAU;AAAA,IACZ,SAAS,GAAG;AACV,cAAQ,CAAC;AAAA,IACX;AAAA,EACF;AACA,QAAM,MAAM,aAAa,MAAM,GAAG,CAAC,OAAO;AACxC,QAAI,IAAI;AACN,aAAO;AAAA,IACT,OAAO;AACL,cAAQ,QAAQ;AAAA,IAClB;AAAA,EACF,CAAC;AACD,QAAM,MAAM,WAAW,CAAC,UAAU;AAChC,QAAI,QAAQ,OAAO;AACjB,aAAO;AACP,YAAM,WAAW,aAAa,MAAM;AACpC,UAAI,UAAU;AACZ,gBAAQ,MAAM,SAAS,IAAI;AAAA,UACzB;AAAA,UACA,QAAQ,KAAK;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG,EAAE,MAAM,KAAK,CAAC;AACjB,eAAa,MAAM,OAAO,IAAI,GAAG,KAAK;AACtC,oBAAkB,MAAM;AACxB,WAAS,OAAO,MAAM;AACpB,UAAM,KAAK,aAAa,MAAM;AAC9B,QAAI,CAAC,YAAY,SAAS,CAAC;AACzB;AACF,QAAI,CAAC,QAAQ;AACX,cAAQ,QAAQ,GAAG,QAAQ,QAAQ,SAAS,GAAG,cAAc;AAC/D,QAAI;AACF,cAAQ,MAAM,QAAQ;AACxB,QAAI,kBAAkB;AACpB,cAAQ,MAAM,eAAe;AAC/B,QAAI,QAAQ,CAAC;AACX,cAAQ,MAAM,MAAM;AAAA;AAEpB,iBAAW;AACb,eAAW,OAAO,SAAS,QAAQ,QAAQ,KAAK;AAAA,EAClD;AACA,QAAM,kBAAkB,EAAE,SAAS,KAAK;AACxC,mBAAiB,SAAS,CAAC,UAAU,UAAU,QAAQ,GAAG,WAAW,eAAe;AACpF,mBAAiB,SAAS,UAAU,MAAM;AACxC,QAAI;AACJ,QAAI;AACF,OAAC,KAAK,QAAQ,UAAU,OAAO,SAAS,GAAG,aAAa;AAAA,EAC5D,GAAG,eAAe;AAClB,QAAM,EAAE,QAAQ,WAAW,OAAO,SAAS,IAAI,SAAS,MAAM;AAC5D,QAAI,CAAC,QAAQ;AACX;AACF,UAAM,UAAU,QAAQ,MAAM;AAC9B,UAAM,YAAY,QAAQ,MAAM;AAChC,UAAM,eAAe,QAAQ,MAAM;AACnC,UAAM,YAAY,QAAQ,MAAM;AAChC,UAAM,cAAc,QAAQ,MAAM;AAClC,UAAM,WAAW,QAAQ,MAAM;AAC/B,UAAM,eAAe,QAAQ,MAAM;AAAA,EACrC,GAAG,EAAE,WAAW,MAAM,CAAC;AACvB,WAAS,aAAa;AACpB,QAAI,YAAY;AACd,gBAAU;AAAA,EACd;AACA,WAAS,YAAY;AACnB,QAAI,YAAY,SAASA;AACvB,MAAAA,QAAO,sBAAsB,QAAQ;AAAA,EACzC;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,cAAc,OAAO,SAAS;AACrC,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,aAAa;AAAA,IACb;AAAA,EACF,IAAI,WAAW,CAAC;AAChB,QAAM,eAAe;AAAA,IACnB,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,UAAU;AAAA,EACZ;AACA,QAAM,gBAAgB,MAAM,KAAK,MAAM,KAAK,EAAE,QAAQ,MAAM,OAAO,CAAC,GAAG,OAAO,EAAE,OAAO,aAAa,SAAS,MAAM,KAAK,EAAE;AAC1H,QAAM,SAAS,SAAS,aAAa;AACrC,QAAM,cAAc,WAAW,EAAE;AACjC,MAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AAChC,eAAW;AACX,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,WAAS,aAAa,OAAO,KAAK;AAChC,gBAAY;AACZ,WAAO,YAAY,KAAK,EAAE,OAAO;AACjC,WAAO,YAAY,KAAK,EAAE,QAAQ;AAAA,EACpC;AACA,QAAM,OAAO,CAAC,MAAM,SAAS;AAC3B,WAAO,KAAK,KAAK,CAAC,YAAY;AAC5B,UAAI;AACJ,UAAI,UAAU,OAAO,SAAS,OAAO,SAAS;AAC5C,qBAAa,aAAa,SAAS,IAAI,MAAM,SAAS,CAAC;AACvD;AAAA,MACF;AACA,YAAM,KAAK,OAAO,YAAY,KAAK,MAAM,OAAO,SAAS,GAAG,WAAW,aAAa,YAAY,WAAW;AACzG,mBAAW;AACX;AAAA,MACF;AACA,YAAM,OAAO,KAAK,OAAO,EAAE,KAAK,CAAC,eAAe;AAC9C,qBAAa,aAAa,WAAW,UAAU;AAC/C,YAAI,YAAY,UAAU,MAAM,SAAS;AACvC,qBAAW;AACb,eAAO;AAAA,MACT,CAAC;AACD,UAAI,CAAC;AACH,eAAO;AACT,aAAO,QAAQ,KAAK,CAAC,MAAM,YAAY,MAAM,CAAC,CAAC;AAAA,IACjD,CAAC,EAAE,MAAM,CAAC,MAAM;AACd,UAAI,UAAU,OAAO,SAAS,OAAO,SAAS;AAC5C,qBAAa,aAAa,SAAS,CAAC;AACpC,eAAO;AAAA,MACT;AACA,mBAAa,aAAa,UAAU,CAAC;AACrC,cAAQ;AACR,aAAO;AAAA,IACT,CAAC;AAAA,EACH,GAAG,QAAQ,QAAQ,CAAC;AACpB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,YAAY,QAAQ;AAC3B,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAM,QAAQ,IAAI,MAAM,SAAS;AACjC,QAAI,OAAO;AACT,aAAO,KAAK;AAAA;AAEZ,aAAO,iBAAiB,SAAS,MAAM,OAAO,KAAK,GAAG,EAAE,MAAM,KAAK,CAAC;AAAA,EACxE,CAAC;AACH;AAEA,SAAS,cAAc,SAAS,cAAc,SAAS;AACrD,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV;AAAA,EACF,IAAI,WAAW,OAAO,UAAU,CAAC;AACjC,QAAM,QAAQ,UAAU,WAAW,YAAY,IAAI,IAAI,YAAY;AACnE,QAAM,UAAU,WAAW,KAAK;AAChC,QAAM,YAAY,WAAW,KAAK;AAClC,QAAM,QAAQ,WAAW,MAAM;AAC/B,iBAAe,QAAQ,SAAS,MAAM,MAAM;AAC1C,QAAI;AACF,YAAM,QAAQ;AAChB,UAAM,QAAQ;AACd,YAAQ,QAAQ;AAChB,cAAU,QAAQ;AAClB,QAAI,SAAS;AACX,YAAM,eAAe,MAAM;AAC7B,UAAM,WAAW,OAAO,YAAY,aAAa,QAAQ,GAAG,IAAI,IAAI;AACpE,QAAI;AACF,YAAM,OAAO,MAAM;AACnB,YAAM,QAAQ;AACd,cAAQ,QAAQ;AAChB,gBAAU,IAAI;AAAA,IAChB,SAAS,GAAG;AACV,YAAM,QAAQ;AACd,cAAQ,CAAC;AACT,UAAI;AACF,cAAM;AAAA,IACV,UAAE;AACA,gBAAU,QAAQ;AAAA,IACpB;AACA,WAAO,MAAM;AAAA,EACf;AACA,MAAI,WAAW;AACb,YAAQ,KAAK;AAAA,EACf;AACA,QAAM,QAAQ;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,WAAS,oBAAoB;AAC3B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,YAAM,SAAS,EAAE,KAAK,KAAK,EAAE,KAAK,MAAM,QAAQ,KAAK,CAAC,EAAE,MAAM,MAAM;AAAA,IACtE,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL,GAAG;AAAA,IACH,KAAK,aAAa,YAAY;AAC5B,aAAO,kBAAkB,EAAE,KAAK,aAAa,UAAU;AAAA,IACzD;AAAA,EACF;AACF;AAEA,IAAM,WAAW;AAAA,EACf,OAAO,CAAC,MAAM,KAAK,UAAU,CAAC;AAAA,EAC9B,QAAQ,CAAC,MAAM,KAAK,UAAU,CAAC;AAAA,EAC/B,KAAK,CAAC,MAAM,KAAK,UAAU,MAAM,KAAK,CAAC,CAAC;AAAA,EACxC,KAAK,CAAC,MAAM,KAAK,UAAU,OAAO,YAAY,CAAC,CAAC;AAAA,EAChD,MAAM,MAAM;AACd;AACA,SAAS,wBAAwB,QAAQ;AACvC,MAAI,CAAC;AACH,WAAO,SAAS;AAClB,MAAI,kBAAkB;AACpB,WAAO,SAAS;AAAA,WACT,kBAAkB;AACzB,WAAO,SAAS;AAAA,WACT,MAAM,QAAQ,MAAM;AAC3B,WAAO,SAAS;AAAA;AAEhB,WAAO,SAAS;AACpB;AAEA,SAAS,UAAU,QAAQ,SAAS;AAClC,QAAM,SAAS,WAAW,EAAE;AAC5B,QAAM,UAAU,WAAW;AAC3B,WAAS,UAAU;AACjB,QAAI,CAAC;AACH;AACF,YAAQ,QAAQ,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC/C,UAAI;AACF,cAAM,UAAU,QAAQ,MAAM;AAC9B,YAAI,WAAW,MAAM;AACnB,kBAAQ,EAAE;AAAA,QACZ,WAAW,OAAO,YAAY,UAAU;AACtC,kBAAQ,aAAa,IAAI,KAAK,CAAC,OAAO,GAAG,EAAE,MAAM,aAAa,CAAC,CAAC,CAAC;AAAA,QACnE,WAAW,mBAAmB,MAAM;AAClC,kBAAQ,aAAa,OAAO,CAAC;AAAA,QAC/B,WAAW,mBAAmB,aAAa;AACzC,kBAAQ,OAAO,KAAK,OAAO,aAAa,GAAG,IAAI,WAAW,OAAO,CAAC,CAAC,CAAC;AAAA,QACtE,WAAW,mBAAmB,mBAAmB;AAC/C,kBAAQ,QAAQ,UAAU,WAAW,OAAO,SAAS,QAAQ,MAAM,WAAW,OAAO,SAAS,QAAQ,OAAO,CAAC;AAAA,QAChH,WAAW,mBAAmB,kBAAkB;AAC9C,gBAAM,MAAM,QAAQ,UAAU,KAAK;AACnC,cAAI,cAAc;AAClB,oBAAU,GAAG,EAAE,KAAK,MAAM;AACxB,kBAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,kBAAM,MAAM,OAAO,WAAW,IAAI;AAClC,mBAAO,QAAQ,IAAI;AACnB,mBAAO,SAAS,IAAI;AACpB,gBAAI,UAAU,KAAK,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AACpD,oBAAQ,OAAO,UAAU,WAAW,OAAO,SAAS,QAAQ,MAAM,WAAW,OAAO,SAAS,QAAQ,OAAO,CAAC;AAAA,UAC/G,CAAC,EAAE,MAAM,MAAM;AAAA,QACjB,WAAW,OAAO,YAAY,UAAU;AACtC,gBAAM,gBAAgB,WAAW,OAAO,SAAS,QAAQ,eAAe,wBAAwB,OAAO;AACvG,gBAAM,aAAa,aAAa,OAAO;AACvC,iBAAO,QAAQ,aAAa,IAAI,KAAK,CAAC,UAAU,GAAG,EAAE,MAAM,mBAAmB,CAAC,CAAC,CAAC;AAAA,QACnF,OAAO;AACL,iBAAO,IAAI,MAAM,6BAA6B,CAAC;AAAA,QACjD;AAAA,MACF,SAAS,OAAO;AACd,eAAO,KAAK;AAAA,MACd;AAAA,IACF,CAAC;AACD,YAAQ,MAAM,KAAK,CAAC,QAAQ;AAC1B,aAAO,SAAS,WAAW,OAAO,SAAS,QAAQ,aAAa,QAAQ,IAAI,QAAQ,qBAAqB,EAAE,IAAI;AAAA,IACjH,CAAC;AACD,WAAO,QAAQ;AAAA,EACjB;AACA,MAAI,MAAM,MAAM,KAAK,OAAO,WAAW;AACrC,UAAM,QAAQ,SAAS,EAAE,WAAW,KAAK,CAAC;AAAA;AAE1C,YAAQ;AACV,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,UAAU,KAAK;AACtB,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,QAAI,CAAC,IAAI,UAAU;AACjB,UAAI,SAAS,MAAM;AACjB,gBAAQ;AAAA,MACV;AACA,UAAI,UAAU;AAAA,IAChB,OAAO;AACL,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACH;AACA,SAAS,aAAa,MAAM;AAC1B,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAM,KAAK,IAAI,WAAW;AAC1B,OAAG,SAAS,CAAC,MAAM;AACjB,cAAQ,EAAE,OAAO,MAAM;AAAA,IACzB;AACA,OAAG,UAAU;AACb,OAAG,cAAc,IAAI;AAAA,EACvB,CAAC;AACH;AAEA,SAAS,WAAW,UAAU,CAAC,GAAG;AAChC,QAAM,EAAE,WAAAG,aAAY,iBAAiB,IAAI;AACzC,QAAMC,UAAS,CAAC,kBAAkB,sBAAsB,yBAAyB,aAAa;AAC9F,QAAM,cAAc,aAAa,MAAMD,cAAa,gBAAgBA,cAAa,OAAOA,WAAU,eAAe,UAAU;AAC3H,QAAM,WAAW,WAAW,KAAK;AACjC,QAAM,eAAe,WAAW,CAAC;AACjC,QAAM,kBAAkB,WAAW,CAAC;AACpC,QAAM,QAAQ,WAAW,CAAC;AAC1B,MAAI;AACJ,WAAS,oBAAoB;AAC3B,aAAS,QAAQ,KAAK;AACtB,iBAAa,QAAQ,KAAK,gBAAgB;AAC1C,oBAAgB,QAAQ,KAAK,mBAAmB;AAChD,UAAM,QAAQ,KAAK;AAAA,EACrB;AACA,MAAI,YAAY,OAAO;AACrB,IAAAA,WAAU,WAAW,EAAE,KAAK,CAAC,aAAa;AACxC,gBAAU;AACV,wBAAkB,KAAK,OAAO;AAC9B,uBAAiB,SAASC,SAAQ,mBAAmB,EAAE,SAAS,KAAK,CAAC;AAAA,IACxE,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,aAAa,SAAS;AAC7B,MAAI;AAAA,IACF,mBAAmB;AAAA,EACrB,IAAI,WAAW,CAAC;AAChB,QAAM;AAAA,IACJ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,WAAAD,aAAY;AAAA,EACd,IAAI,WAAW,CAAC;AAChB,QAAM,cAAc,aAAa,MAAMA,cAAa,eAAeA,UAAS;AAC5E,QAAM,SAAS,WAAW;AAC1B,QAAM,QAAQ,WAAW,IAAI;AAC7B,QAAM,QAAQ,MAAM;AAClB,iCAA6B;AAAA,EAC/B,CAAC;AACD,iBAAe,gBAAgB;AAC7B,QAAI,CAAC,YAAY;AACf;AACF,UAAM,QAAQ;AACd,QAAI,WAAW,QAAQ,SAAS;AAC9B,yBAAmB;AACrB,QAAI;AACF,aAAO,QAAQ,OAAOA,cAAa,OAAO,SAASA,WAAU,UAAU,cAAc;AAAA,QACnF;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,SAAS,KAAK;AACZ,YAAM,QAAQ;AAAA,IAChB;AAAA,EACF;AACA,QAAM,SAAS,WAAW;AAC1B,QAAM,cAAc,WAAW,KAAK;AACpC,WAAS,QAAQ;AACf,gBAAY,QAAQ;AACpB,WAAO,QAAQ;AACf,WAAO,QAAQ;AAAA,EACjB;AACA,iBAAe,+BAA+B;AAC5C,UAAM,QAAQ;AACd,QAAI,OAAO,SAAS,OAAO,MAAM,MAAM;AACrC,uBAAiB,QAAQ,0BAA0B,OAAO,EAAE,SAAS,KAAK,CAAC;AAC3E,UAAI;AACF,eAAO,QAAQ,MAAM,OAAO,MAAM,KAAK,QAAQ;AAC/C,oBAAY,QAAQ,OAAO,MAAM;AAAA,MACnC,SAAS,KAAK;AACZ,cAAM,QAAQ;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AACA,eAAa,MAAM;AACjB,QAAI;AACJ,QAAI,OAAO;AACT,OAAC,KAAK,OAAO,MAAM,SAAS,OAAO,SAAS,GAAG,QAAQ;AAAA,EAC3D,CAAC;AACD,oBAAkB,MAAM;AACtB,QAAI;AACJ,QAAI,OAAO;AACT,OAAC,KAAK,OAAO,MAAM,SAAS,OAAO,SAAS,GAAG,WAAW;AAAA,EAC9D,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA,aAAa,SAAS,WAAW;AAAA;AAAA,IAEjC;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,EACF;AACF;AAEA,IAAM,iBAAiB,OAAO,kBAAkB;AAChD,SAAS,cAAc;AACrB,QAAM,WAAW,oBAAoB,IAAI,YAAY,gBAAgB,IAAI,IAAI;AAC7E,SAAO,OAAO,aAAa,WAAW,WAAW;AACnD;AACA,SAAS,gBAAgB,OAAO,KAAK;AACnC,MAAI,QAAQ,QAAQ;AAClB,QAAI,QAAQ,gBAAgB,KAAK;AAAA,EACnC,OAAO;AACL,iBAAa,gBAAgB,KAAK;AAAA,EACpC;AACF;AAEA,SAAS,cAAc,OAAO,UAAU,CAAC,GAAG;AAC1C,QAAM,EAAE,QAAAH,UAAS,eAAe,WAAW,YAAY,EAAE,IAAI;AAC7D,QAAM,cAAc,aAAa,MAAMA,WAAU,gBAAgBA,WAAU,OAAOA,QAAO,eAAe,UAAU;AAClH,QAAM,aAAa,WAAW,OAAO,aAAa,QAAQ;AAC1D,QAAM,aAAa,WAAW;AAC9B,QAAM,UAAU,WAAW,KAAK;AAChC,QAAM,UAAU,CAAC,UAAU;AACzB,YAAQ,QAAQ,MAAM;AAAA,EACxB;AACA,cAAY,MAAM;AAChB,QAAI,WAAW,OAAO;AACpB,iBAAW,QAAQ,CAAC,YAAY;AAChC,YAAM,eAAe,QAAQ,KAAK,EAAE,MAAM,GAAG;AAC7C,cAAQ,QAAQ,aAAa,KAAK,CAAC,gBAAgB;AACjD,cAAM,MAAM,YAAY,SAAS,SAAS;AAC1C,cAAM,WAAW,YAAY,MAAM,gDAAgD;AACnF,cAAM,WAAW,YAAY,MAAM,gDAAgD;AACnF,YAAI,MAAM,QAAQ,YAAY,QAAQ;AACtC,YAAI,YAAY,KAAK;AACnB,gBAAM,YAAY,QAAQ,SAAS,CAAC,CAAC;AAAA,QACvC;AACA,YAAI,YAAY,KAAK;AACnB,gBAAM,YAAY,QAAQ,SAAS,CAAC,CAAC;AAAA,QACvC;AACA,eAAO,MAAM,CAAC,MAAM;AAAA,MACtB,CAAC;AACD;AAAA,IACF;AACA,QAAI,CAAC,YAAY;AACf;AACF,eAAW,QAAQA,QAAO,WAAW,QAAQ,KAAK,CAAC;AACnD,YAAQ,QAAQ,WAAW,MAAM;AAAA,EACnC,CAAC;AACD,mBAAiB,YAAY,UAAU,SAAS,EAAE,SAAS,KAAK,CAAC;AACjE,SAAO,SAAS,MAAM,QAAQ,KAAK;AACrC;AAEA,IAAM,sBAAsB;AAAA,EAC1B,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AACT;AACA,IAAM,yBAAyB;AAAA,EAC7B,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,KAAK;AACP;AACA,IAAM,uBAAuB;AAAA,EAC3B,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AACN;AACA,IAAM,uBAAuB;AAAA,EAC3B,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,KAAK;AACP;AACA,IAAM,qBAAqB;AAC3B,IAAM,uBAAuB;AAAA,EAC3B,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,KAAK;AACP;AACA,IAAM,oBAAoB;AAAA,EACxB,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AACN;AACA,IAAM,qBAAqB;AAAA,EACzB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AACb;AACA,IAAM,uBAAuB;AAAA,EAC3B,OAAO;AAAA,EACP,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AACT;AACA,IAAM,uBAAuB;AAAA,EAC3B,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AACN;AACA,IAAM,qBAAqB;AAAA,EACzB,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AACN;AAEA,SAAS,eAAe,aAAa,UAAU,CAAC,GAAG;AACjD,WAASK,UAAS,GAAG,OAAO;AAC1B,QAAI,IAAI,QAAQ,YAAY,QAAQ,CAAC,CAAC,CAAC;AACvC,QAAI,SAAS;AACX,UAAI,iBAAiB,GAAG,KAAK;AAC/B,QAAI,OAAO,MAAM;AACf,UAAI,GAAG,CAAC;AACV,WAAO;AAAA,EACT;AACA,QAAM,EAAE,QAAAL,UAAS,eAAe,WAAW,aAAa,WAAW,YAAY,EAAE,IAAI;AACrF,QAAM,aAAa,OAAO,aAAa;AACvC,QAAM,UAAU,aAAa,WAAW,KAAK,IAAI,EAAE,OAAO,KAAK;AAC/D,MAAI,YAAY;AACd,iBAAa,MAAM,QAAQ,QAAQ,CAAC,CAACA,OAAM;AAAA,EAC7C;AACA,WAAS,MAAM,OAAO,MAAM;AAC1B,QAAI,CAAC,QAAQ,SAAS,YAAY;AAChC,aAAO,UAAU,QAAQ,YAAY,QAAQ,IAAI,IAAI,YAAY,QAAQ,IAAI;AAAA,IAC/E;AACA,QAAI,CAACA;AACH,aAAO;AACT,WAAOA,QAAO,WAAW,IAAI,KAAK,WAAW,IAAI,GAAG,EAAE;AAAA,EACxD;AACA,QAAM,iBAAiB,CAAC,MAAM;AAC5B,WAAO,cAAc,MAAM,eAAeK,UAAS,CAAC,CAAC,KAAK,OAAO;AAAA,EACnE;AACA,QAAM,iBAAiB,CAAC,MAAM;AAC5B,WAAO,cAAc,MAAM,eAAeA,UAAS,CAAC,CAAC,KAAK,OAAO;AAAA,EACnE;AACA,QAAM,kBAAkB,OAAO,KAAK,WAAW,EAAE,OAAO,CAAC,WAAW,MAAM;AACxE,WAAO,eAAe,WAAW,GAAG;AAAA,MAClC,KAAK,MAAM,aAAa,cAAc,eAAe,CAAC,IAAI,eAAe,CAAC;AAAA,MAC1E,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,WAAS,UAAU;AACjB,UAAM,SAAS,OAAO,KAAK,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,gBAAgB,CAAC,GAAG,QAAQA,UAAS,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AAC5H,WAAO,SAAS,MAAM,OAAO,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AAAA,EACzE;AACA,SAAO,OAAO,OAAO,iBAAiB;AAAA,IACpC;AAAA,IACA;AAAA,IACA,QAAQ,GAAG;AACT,aAAO,cAAc,MAAM,eAAeA,UAAS,GAAG,GAAG,CAAC,KAAK,OAAO;AAAA,IACxE;AAAA,IACA,QAAQ,GAAG;AACT,aAAO,cAAc,MAAM,eAAeA,UAAS,GAAG,IAAI,CAAC,KAAK,OAAO;AAAA,IACzE;AAAA,IACA,QAAQ,GAAG,GAAG;AACZ,aAAO,cAAc,MAAM,eAAeA,UAAS,CAAC,CAAC,qBAAqBA,UAAS,GAAG,IAAI,CAAC,KAAK,OAAO;AAAA,IACzG;AAAA,IACA,UAAU,GAAG;AACX,aAAO,MAAM,OAAOA,UAAS,GAAG,GAAG,CAAC;AAAA,IACtC;AAAA,IACA,iBAAiB,GAAG;AAClB,aAAO,MAAM,OAAOA,UAAS,CAAC,CAAC;AAAA,IACjC;AAAA,IACA,UAAU,GAAG;AACX,aAAO,MAAM,OAAOA,UAAS,GAAG,IAAI,CAAC;AAAA,IACvC;AAAA,IACA,iBAAiB,GAAG;AAClB,aAAO,MAAM,OAAOA,UAAS,CAAC,CAAC;AAAA,IACjC;AAAA,IACA,YAAY,GAAG,GAAG;AAChB,aAAO,MAAM,OAAOA,UAAS,CAAC,CAAC,KAAK,MAAM,OAAOA,UAAS,GAAG,IAAI,CAAC;AAAA,IACpE;AAAA,IACA;AAAA,IACA,SAAS;AACP,YAAM,MAAM,QAAQ;AACpB,aAAO,SAAS,MAAM,IAAI,MAAM,WAAW,IAAI,KAAK,IAAI,MAAM,GAAG,aAAa,cAAc,KAAK,CAAC,CAAC;AAAA,IACrG;AAAA,EACF,CAAC;AACH;AAEA,SAAS,oBAAoB,SAAS;AACpC,QAAM;AAAA,IACJ;AAAA,IACA,QAAAL,UAAS;AAAA,EACX,IAAI;AACJ,QAAM,cAAc,aAAa,MAAMA,WAAU,sBAAsBA,OAAM;AAC7E,QAAM,WAAW,WAAW,KAAK;AACjC,QAAM,UAAU,IAAI;AACpB,QAAM,OAAO,IAAI;AACjB,QAAM,QAAQ,WAAW,IAAI;AAC7B,QAAM,OAAO,CAAC,UAAU;AACtB,QAAI,QAAQ;AACV,cAAQ,MAAM,YAAY,KAAK;AAAA,EACnC;AACA,QAAM,QAAQ,MAAM;AAClB,QAAI,QAAQ;AACV,cAAQ,MAAM,MAAM;AACtB,aAAS,QAAQ;AAAA,EACnB;AACA,MAAI,YAAY,OAAO;AACrB,iBAAa,MAAM;AACjB,YAAM,QAAQ;AACd,cAAQ,QAAQ,IAAI,iBAAiB,IAAI;AACzC,YAAM,kBAAkB;AAAA,QACtB,SAAS;AAAA,MACX;AACA,uBAAiB,SAAS,WAAW,CAAC,MAAM;AAC1C,aAAK,QAAQ,EAAE;AAAA,MACjB,GAAG,eAAe;AAClB,uBAAiB,SAAS,gBAAgB,CAAC,MAAM;AAC/C,cAAM,QAAQ;AAAA,MAChB,GAAG,eAAe;AAClB,uBAAiB,SAAS,SAAS,MAAM;AACvC,iBAAS,QAAQ;AAAA,MACnB,GAAG,eAAe;AAAA,IACpB,CAAC;AAAA,EACH;AACA,oBAAkB,MAAM;AACtB,UAAM;AAAA,EACR,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,sBAAsB;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,SAAS,mBAAmB,UAAU,CAAC,GAAG;AACxC,QAAM,EAAE,QAAAA,UAAS,cAAc,IAAI;AACnC,QAAM,OAAO,OAAO;AAAA,IAClB,oBAAoB,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,CAAC;AAAA,EAC/C;AACA,aAAW,CAAC,KAAKM,IAAG,KAAK,cAAc,IAAI,GAAG;AAC5C,UAAMA,MAAK,CAAC,UAAU;AACpB,UAAI,EAAEN,WAAU,OAAO,SAASA,QAAO,aAAaA,QAAO,SAAS,GAAG,MAAM;AAC3E;AACF,MAAAA,QAAO,SAAS,GAAG,IAAI;AAAA,IACzB,CAAC;AAAA,EACH;AACA,QAAM,aAAa,CAAC,YAAY;AAC9B,QAAI;AACJ,UAAM,EAAE,OAAO,QAAQ,OAAO,KAAKA,WAAU,OAAO,SAASA,QAAO,YAAY,CAAC;AACjF,UAAM,EAAE,OAAO,KAAKA,WAAU,OAAO,SAASA,QAAO,aAAa,CAAC;AACnE,eAAW,OAAO;AAChB,WAAK,GAAG,EAAE,SAAS,KAAKA,WAAU,OAAO,SAASA,QAAO,aAAa,OAAO,SAAS,GAAG,GAAG;AAC9F,WAAO,SAAS;AAAA,MACd;AAAA,MACA,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AACA,QAAM,QAAQ,IAAI,WAAW,MAAM,CAAC;AACpC,MAAIA,SAAQ;AACV,UAAM,kBAAkB,EAAE,SAAS,KAAK;AACxC,qBAAiBA,SAAQ,YAAY,MAAM,MAAM,QAAQ,WAAW,UAAU,GAAG,eAAe;AAChG,qBAAiBA,SAAQ,cAAc,MAAM,MAAM,QAAQ,WAAW,YAAY,GAAG,eAAe;AAAA,EACtG;AACA,SAAO;AACT;AAEA,SAAS,UAAU,UAAU,aAAa,CAAC,GAAG,MAAM,MAAM,GAAG,SAAS;AACpE,QAAM,EAAE,WAAW,MAAM,GAAG,aAAa,IAAI,WAAW,CAAC;AACzD,QAAM,cAAc,UAAU,SAAS,OAAO,QAAQ;AACtD,QAAM,MAAM,SAAS,OAAO,CAAC,UAAU;AACrC,QAAI,CAAC,WAAW,OAAO,YAAY,KAAK;AACtC,kBAAY,QAAQ;AAAA,EACxB,GAAG,YAAY;AACf,SAAO;AACT;AAEA,SAAS,cAAc,gBAAgB,UAAU,CAAC,GAAG;AACnD,QAAM;AAAA,IACJ,WAAW;AAAA,IACX,WAAAG,aAAY;AAAA,EACd,IAAI;AACJ,QAAM,cAAc,aAAa,MAAMA,cAAa,iBAAiBA,UAAS;AAC9E,QAAM,mBAAmB,WAAW;AACpC,QAAM,OAAO,OAAO,mBAAmB,WAAW,EAAE,MAAM,eAAe,IAAI;AAC7E,QAAM,QAAQ,WAAW;AACzB,QAAM,SAAS,MAAM;AACnB,QAAI,IAAI;AACR,UAAM,SAAS,MAAM,KAAK,iBAAiB,UAAU,OAAO,SAAS,GAAG,UAAU,OAAO,KAAK;AAAA,EAChG;AACA,mBAAiB,kBAAkB,UAAU,QAAQ,EAAE,SAAS,KAAK,CAAC;AACtE,QAAM,QAAQ,uBAAuB,YAAY;AAC/C,QAAI,CAAC,YAAY;AACf;AACF,QAAI,CAAC,iBAAiB,OAAO;AAC3B,UAAI;AACF,yBAAiB,QAAQ,MAAMA,WAAU,YAAY,MAAM,IAAI;AAAA,MACjE,SAAS,GAAG;AACV,yBAAiB,QAAQ;AAAA,MAC3B,UAAE;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI;AACF,aAAO,MAAM,iBAAiB,KAAK;AAAA,EACvC,CAAC;AACD,QAAM;AACN,MAAI,UAAU;AACZ,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,SAAS,aAAa,UAAU,CAAC,GAAG;AAClC,QAAM;AAAA,IACJ,WAAAA,aAAY;AAAA,IACZ,OAAO;AAAA,IACP;AAAA,IACA,eAAe;AAAA,IACf,SAAS;AAAA,EACX,IAAI;AACJ,QAAM,0BAA0B,aAAa,MAAMA,cAAa,eAAeA,UAAS;AACxF,QAAM,iBAAiB,cAAc,gBAAgB;AACrD,QAAM,kBAAkB,cAAc,iBAAiB;AACvD,QAAM,cAAc,SAAS,MAAM,wBAAwB,SAAS,MAAM;AAC1E,QAAM,OAAO,WAAW,EAAE;AAC1B,QAAM,SAAS,WAAW,KAAK;AAC/B,QAAM,UAAU,aAAa,MAAM,OAAO,QAAQ,OAAO,cAAc,EAAE,WAAW,MAAM,CAAC;AAC3F,iBAAe,aAAa;AAC1B,QAAI,YAAY,EAAE,wBAAwB,SAAS,UAAU,eAAe,KAAK;AACjF,QAAI,CAAC,WAAW;AACd,UAAI;AACF,aAAK,QAAQ,MAAMA,WAAU,UAAU,SAAS;AAAA,MAClD,SAAS,GAAG;AACV,oBAAY;AAAA,MACd;AAAA,IACF;AACA,QAAI,WAAW;AACb,WAAK,QAAQ,WAAW;AAAA,IAC1B;AAAA,EACF;AACA,MAAI,YAAY,SAAS;AACvB,qBAAiB,CAAC,QAAQ,KAAK,GAAG,YAAY,EAAE,SAAS,KAAK,CAAC;AACjE,iBAAe,KAAK,QAAQ,QAAQ,MAAM,GAAG;AAC3C,QAAI,YAAY,SAAS,SAAS,MAAM;AACtC,UAAI,YAAY,EAAE,wBAAwB,SAAS,UAAU,gBAAgB,KAAK;AAClF,UAAI,CAAC,WAAW;AACd,YAAI;AACF,gBAAMA,WAAU,UAAU,UAAU,KAAK;AAAA,QAC3C,SAAS,GAAG;AACV,sBAAY;AAAA,QACd;AAAA,MACF;AACA,UAAI;AACF,mBAAW,KAAK;AAClB,WAAK,QAAQ;AACb,aAAO,QAAQ;AACf,cAAQ,MAAM;AAAA,IAChB;AAAA,EACF;AACA,WAAS,WAAW,OAAO;AACzB,UAAM,KAAK,SAAS,cAAc,UAAU;AAC5C,OAAG,QAAQ,SAAS,OAAO,QAAQ;AACnC,OAAG,MAAM,WAAW;AACpB,OAAG,MAAM,UAAU;AACnB,aAAS,KAAK,YAAY,EAAE;AAC5B,OAAG,OAAO;AACV,aAAS,YAAY,MAAM;AAC3B,OAAG,OAAO;AAAA,EACZ;AACA,WAAS,aAAa;AACpB,QAAI,IAAI,IAAI;AACZ,YAAQ,MAAM,MAAM,KAAK,YAAY,OAAO,SAAS,SAAS,iBAAiB,OAAO,SAAS,GAAG,KAAK,QAAQ,MAAM,OAAO,SAAS,GAAG,SAAS,MAAM,OAAO,KAAK;AAAA,EACrK;AACA,WAAS,UAAU,QAAQ;AACzB,WAAO,WAAW,aAAa,WAAW;AAAA,EAC5C;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,kBAAkB,UAAU,CAAC,GAAG;AACvC,QAAM;AAAA,IACJ,WAAAA,aAAY;AAAA,IACZ,OAAO;AAAA,IACP;AAAA,IACA,eAAe;AAAA,EACjB,IAAI;AACJ,QAAM,cAAc,aAAa,MAAMA,cAAa,eAAeA,UAAS;AAC5E,QAAM,UAAU,IAAI,CAAC,CAAC;AACtB,QAAM,SAAS,WAAW,KAAK;AAC/B,QAAM,UAAU,aAAa,MAAM,OAAO,QAAQ,OAAO,cAAc,EAAE,WAAW,MAAM,CAAC;AAC3F,WAAS,gBAAgB;AACvB,QAAI,YAAY,OAAO;AACrB,MAAAA,WAAU,UAAU,KAAK,EAAE,KAAK,CAAC,UAAU;AACzC,gBAAQ,QAAQ;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF;AACA,MAAI,YAAY,SAAS;AACvB,qBAAiB,CAAC,QAAQ,KAAK,GAAG,eAAe,EAAE,SAAS,KAAK,CAAC;AACpE,iBAAe,KAAK,QAAQ,QAAQ,MAAM,GAAG;AAC3C,QAAI,YAAY,SAAS,SAAS,MAAM;AACtC,YAAMA,WAAU,UAAU,MAAM,KAAK;AACrC,cAAQ,QAAQ;AAChB,aAAO,QAAQ;AACf,cAAQ,MAAM;AAAA,IAChB;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,YAAY,QAAQ;AAC3B,SAAO,KAAK,MAAM,KAAK,UAAU,MAAM,CAAC;AAC1C;AACA,SAAS,UAAU,QAAQ,UAAU,CAAC,GAAG;AACvC,QAAM,SAAS,IAAI,CAAC,CAAC;AACrB,QAAM,aAAa,WAAW,KAAK;AACnC,MAAI,YAAY;AAChB,QAAM;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA;AAAA,IAER,OAAO;AAAA,IACP,YAAY;AAAA,EACd,IAAI;AACJ,QAAM,QAAQ,MAAM;AAClB,QAAI,WAAW;AACb,kBAAY;AACZ;AAAA,IACF;AACA,eAAW,QAAQ;AAAA,EACrB,GAAG;AAAA,IACD,MAAM;AAAA,IACN,OAAO;AAAA,EACT,CAAC;AACD,WAAS,OAAO;AACd,gBAAY;AACZ,eAAW,QAAQ;AACnB,WAAO,QAAQ,MAAM,QAAQ,MAAM,CAAC;AAAA,EACtC;AACA,MAAI,CAAC,WAAW,MAAM,MAAM,KAAK,OAAO,WAAW,aAAa;AAC9D,UAAM,QAAQ,MAAM;AAAA,MAClB,GAAG;AAAA,MACH;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH,OAAO;AACL,SAAK;AAAA,EACP;AACA,SAAO,EAAE,QAAQ,YAAY,KAAK;AACpC;AAEA,IAAM,UAAU,OAAO,eAAe,cAAc,aAAa,OAAO,WAAW,cAAc,SAAS,OAAO,WAAW,cAAc,SAAS,OAAO,SAAS,cAAc,OAAO,CAAC;AACzL,IAAM,YAAY;AAClB,IAAM,WAA2B,YAAY;AAC7C,SAAS,cAAc;AACrB,MAAI,EAAE,aAAa;AACjB,YAAQ,SAAS,IAAI,QAAQ,SAAS,KAAK,CAAC;AAC9C,SAAO,QAAQ,SAAS;AAC1B;AACA,SAAS,cAAc,KAAK,UAAU;AACpC,SAAO,SAAS,GAAG,KAAK;AAC1B;AACA,SAAS,cAAc,KAAK,IAAI;AAC9B,WAAS,GAAG,IAAI;AAClB;AAEA,SAAS,iBAAiB,SAAS;AACjC,SAAO,cAAc,gCAAgC,OAAO;AAC9D;AAEA,SAAS,oBAAoB,SAAS;AACpC,SAAO,WAAW,OAAO,QAAQ,mBAAmB,MAAM,QAAQ,mBAAmB,MAAM,QAAQ,mBAAmB,OAAO,SAAS,OAAO,YAAY,YAAY,YAAY,OAAO,YAAY,WAAW,WAAW,OAAO,YAAY,WAAW,WAAW,CAAC,OAAO,MAAM,OAAO,IAAI,WAAW;AACzS;AAEA,IAAM,qBAAqB;AAAA,EACzB,SAAS;AAAA,IACP,MAAM,CAAC,MAAM,MAAM;AAAA,IACnB,OAAO,CAAC,MAAM,OAAO,CAAC;AAAA,EACxB;AAAA,EACA,QAAQ;AAAA,IACN,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC;AAAA,IACzB,OAAO,CAAC,MAAM,KAAK,UAAU,CAAC;AAAA,EAChC;AAAA,EACA,QAAQ;AAAA,IACN,MAAM,CAAC,MAAM,OAAO,WAAW,CAAC;AAAA,IAChC,OAAO,CAAC,MAAM,OAAO,CAAC;AAAA,EACxB;AAAA,EACA,KAAK;AAAA,IACH,MAAM,CAAC,MAAM;AAAA,IACb,OAAO,CAAC,MAAM,OAAO,CAAC;AAAA,EACxB;AAAA,EACA,QAAQ;AAAA,IACN,MAAM,CAAC,MAAM;AAAA,IACb,OAAO,CAAC,MAAM,OAAO,CAAC;AAAA,EACxB;AAAA,EACA,KAAK;AAAA,IACH,MAAM,CAAC,MAAM,IAAI,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,IAClC,OAAO,CAAC,MAAM,KAAK,UAAU,MAAM,KAAK,EAAE,QAAQ,CAAC,CAAC;AAAA,EACtD;AAAA,EACA,KAAK;AAAA,IACH,MAAM,CAAC,MAAM,IAAI,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,IAClC,OAAO,CAAC,MAAM,KAAK,UAAU,MAAM,KAAK,CAAC,CAAC;AAAA,EAC5C;AAAA,EACA,MAAM;AAAA,IACJ,MAAM,CAAC,MAAM,IAAI,KAAK,CAAC;AAAA,IACvB,OAAO,CAAC,MAAM,EAAE,YAAY;AAAA,EAC9B;AACF;AACA,IAAM,yBAAyB;AAC/B,SAAS,WAAW,KAAKI,WAAU,SAAS,UAAU,CAAC,GAAG;AACxD,MAAI;AACJ,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,yBAAyB;AAAA,IACzB,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB;AAAA,IACA,QAAAP,UAAS;AAAA,IACT;AAAA,IACA,UAAU,CAAC,MAAM;AACf,cAAQ,MAAM,CAAC;AAAA,IACjB;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ,UAAU,aAAa,KAAK,OAAOO,cAAa,aAAaA,UAAS,IAAIA,SAAQ;AAChG,QAAM,cAAc,SAAS,MAAM,QAAQ,GAAG,CAAC;AAC/C,MAAI,CAAC,SAAS;AACZ,QAAI;AACF,gBAAU,cAAc,qBAAqB,MAAM;AACjD,YAAI;AACJ,gBAAQ,MAAM,kBAAkB,OAAO,SAAS,IAAI;AAAA,MACtD,CAAC,EAAE;AAAA,IACL,SAAS,GAAG;AACV,cAAQ,CAAC;AAAA,IACX;AAAA,EACF;AACA,MAAI,CAAC;AACH,WAAO;AACT,QAAM,UAAU,QAAQA,SAAQ;AAChC,QAAM,OAAO,oBAAoB,OAAO;AACxC,QAAM,cAAc,KAAK,QAAQ,eAAe,OAAO,KAAK,mBAAmB,IAAI;AACnF,QAAM,EAAE,OAAO,YAAY,QAAQ,YAAY,IAAI;AAAA,IACjD;AAAA,IACA,MAAM,MAAM,KAAK,KAAK;AAAA,IACtB,EAAE,OAAO,MAAM,YAAY;AAAA,EAC7B;AACA,QAAM,aAAa,MAAM,OAAO,GAAG,EAAE,MAAM,CAAC;AAC5C,MAAIP,WAAU,wBAAwB;AACpC,iBAAa,MAAM;AACjB,UAAI,mBAAmB;AACrB,yBAAiBA,SAAQ,WAAW,QAAQ,EAAE,SAAS,KAAK,CAAC;AAAA;AAE7D,yBAAiBA,SAAQ,wBAAwB,qBAAqB;AACxE,UAAI;AACF,eAAO;AAAA,IACX,CAAC;AAAA,EACH;AACA,MAAI,CAAC;AACH,WAAO;AACT,WAAS,mBAAmB,UAAU,UAAU;AAC9C,QAAIA,SAAQ;AACV,YAAM,UAAU;AAAA,QACd,KAAK,YAAY;AAAA,QACjB;AAAA,QACA;AAAA,QACA,aAAa;AAAA,MACf;AACA,MAAAA,QAAO,cAAc,mBAAmB,UAAU,IAAI,aAAa,WAAW,OAAO,IAAI,IAAI,YAAY,wBAAwB;AAAA,QAC/H,QAAQ;AAAA,MACV,CAAC,CAAC;AAAA,IACJ;AAAA,EACF;AACA,WAAS,MAAM,GAAG;AAChB,QAAI;AACF,YAAM,WAAW,QAAQ,QAAQ,YAAY,KAAK;AAClD,UAAI,KAAK,MAAM;AACb,2BAAmB,UAAU,IAAI;AACjC,gBAAQ,WAAW,YAAY,KAAK;AAAA,MACtC,OAAO;AACL,cAAM,aAAa,WAAW,MAAM,CAAC;AACrC,YAAI,aAAa,YAAY;AAC3B,kBAAQ,QAAQ,YAAY,OAAO,UAAU;AAC7C,6BAAmB,UAAU,UAAU;AAAA,QACzC;AAAA,MACF;AAAA,IACF,SAAS,GAAG;AACV,cAAQ,CAAC;AAAA,IACX;AAAA,EACF;AACA,WAAS,KAAK,OAAO;AACnB,UAAM,WAAW,QAAQ,MAAM,WAAW,QAAQ,QAAQ,YAAY,KAAK;AAC3E,QAAI,YAAY,MAAM;AACpB,UAAI,iBAAiB,WAAW;AAC9B,gBAAQ,QAAQ,YAAY,OAAO,WAAW,MAAM,OAAO,CAAC;AAC9D,aAAO;AAAA,IACT,WAAW,CAAC,SAAS,eAAe;AAClC,YAAM,QAAQ,WAAW,KAAK,QAAQ;AACtC,UAAI,OAAO,kBAAkB;AAC3B,eAAO,cAAc,OAAO,OAAO;AAAA,eAC5B,SAAS,YAAY,CAAC,MAAM,QAAQ,KAAK;AAChD,eAAO,EAAE,GAAG,SAAS,GAAG,MAAM;AAChC,aAAO;AAAA,IACT,WAAW,OAAO,aAAa,UAAU;AACvC,aAAO;AAAA,IACT,OAAO;AACL,aAAO,WAAW,KAAK,QAAQ;AAAA,IACjC;AAAA,EACF;AACA,WAAS,OAAO,OAAO;AACrB,QAAI,SAAS,MAAM,gBAAgB;AACjC;AACF,QAAI,SAAS,MAAM,OAAO,MAAM;AAC9B,WAAK,QAAQ;AACb;AAAA,IACF;AACA,QAAI,SAAS,MAAM,QAAQ,YAAY;AACrC;AACF,eAAW;AACX,QAAI;AACF,WAAK,SAAS,OAAO,SAAS,MAAM,cAAc,WAAW,MAAM,KAAK,KAAK;AAC3E,aAAK,QAAQ,KAAK,KAAK;AAAA,IAC3B,SAAS,GAAG;AACV,cAAQ,CAAC;AAAA,IACX,UAAE;AACA,UAAI;AACF,iBAAS,WAAW;AAAA;AAEpB,oBAAY;AAAA,IAChB;AAAA,EACF;AACA,WAAS,sBAAsB,OAAO;AACpC,WAAO,MAAM,MAAM;AAAA,EACrB;AACA,SAAO;AACT;AAEA,IAAM,oBAAoB;AAC1B,SAAS,aAAa,UAAU,CAAC,GAAG;AAClC,QAAM;AAAA,IACJ,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,QAAAA,UAAS;AAAA,IACT;AAAA,IACA,aAAa;AAAA,IACb,yBAAyB;AAAA,IACzB;AAAA,IACA;AAAA,IACA,oBAAoB;AAAA,EACtB,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,IACN,GAAG,QAAQ,SAAS,CAAC;AAAA,EACvB;AACA,QAAM,gBAAgB,iBAAiB,EAAE,QAAAA,QAAO,CAAC;AACjD,QAAM,SAAS,SAAS,MAAM,cAAc,QAAQ,SAAS,OAAO;AACpE,QAAM,QAAQ,eAAe,cAAc,OAAOQ,OAAM,YAAY,IAAI,WAAW,YAAY,cAAc,SAAS,EAAE,QAAAR,SAAQ,uBAAuB,CAAC;AACxJ,QAAM,QAAQ,SAAS,MAAM,MAAM,UAAU,SAAS,OAAO,QAAQ,MAAM,KAAK;AAChF,QAAM,kBAAkB;AAAA,IACtB;AAAA,IACA,CAAC,WAAW,YAAY,UAAU;AAChC,YAAM,KAAK,OAAO,cAAc,WAAWA,WAAU,OAAO,SAASA,QAAO,SAAS,cAAc,SAAS,IAAI,aAAa,SAAS;AACtI,UAAI,CAAC;AACH;AACF,YAAM,eAA+B,oBAAI,IAAI;AAC7C,YAAM,kBAAkC,oBAAI,IAAI;AAChD,UAAI,oBAAoB;AACxB,UAAI,eAAe,SAAS;AAC1B,cAAM,UAAU,MAAM,MAAM,KAAK;AACjC,eAAO,OAAO,KAAK,EAAE,QAAQ,CAAC,OAAO,KAAK,IAAI,MAAM,KAAK,CAAC,EAAE,OAAO,OAAO,EAAE,QAAQ,CAAC,MAAM;AACzF,cAAI,QAAQ,SAAS,CAAC;AACpB,yBAAa,IAAI,CAAC;AAAA;AAElB,4BAAgB,IAAI,CAAC;AAAA,QACzB,CAAC;AAAA,MACH,OAAO;AACL,4BAAoB,EAAE,KAAK,YAAY,MAAM;AAAA,MAC/C;AACA,UAAI,aAAa,SAAS,KAAK,gBAAgB,SAAS,KAAK,sBAAsB;AACjF;AACF,UAAI;AACJ,UAAI,mBAAmB;AACrB,gBAAQA,QAAO,SAAS,cAAc,OAAO;AAC7C,cAAM,YAAY,SAAS,eAAe,iBAAiB,CAAC;AAC5D,QAAAA,QAAO,SAAS,KAAK,YAAY,KAAK;AAAA,MACxC;AACA,iBAAW,KAAK,cAAc;AAC5B,WAAG,UAAU,IAAI,CAAC;AAAA,MACpB;AACA,iBAAW,KAAK,iBAAiB;AAC/B,WAAG,UAAU,OAAO,CAAC;AAAA,MACvB;AACA,UAAI,mBAAmB;AACrB,WAAG,aAAa,kBAAkB,KAAK,kBAAkB,KAAK;AAAA,MAChE;AACA,UAAI,mBAAmB;AACrB,QAAAA,QAAO,iBAAiB,KAAK,EAAE;AAC/B,iBAAS,KAAK,YAAY,KAAK;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AACA,WAAS,iBAAiB,MAAM;AAC9B,QAAI;AACJ,oBAAgB,UAAU,YAAY,KAAK,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;AAAA,EAC7E;AACA,WAAS,UAAU,MAAM;AACvB,QAAI,QAAQ;AACV,cAAQ,UAAU,MAAM,gBAAgB;AAAA;AAExC,uBAAiB,IAAI;AAAA,EACzB;AACA,QAAM,OAAO,WAAW,EAAE,OAAO,QAAQ,WAAW,KAAK,CAAC;AAC1D,eAAa,MAAM,UAAU,MAAM,KAAK,CAAC;AACzC,QAAM,OAAO,SAAS;AAAA,IACpB,MAAM;AACJ,aAAO,WAAW,MAAM,QAAQ,MAAM;AAAA,IACxC;AAAA,IACA,IAAI,GAAG;AACL,YAAM,QAAQ;AAAA,IAChB;AAAA,EACF,CAAC;AACD,SAAO,OAAO,OAAO,MAAM,EAAE,OAAO,QAAQ,MAAM,CAAC;AACrD;AAEA,SAAS,iBAAiB,WAAW,WAAW,KAAK,GAAG;AACtD,QAAM,cAAc,gBAAgB;AACpC,QAAM,aAAa,gBAAgB;AACnC,QAAM,aAAa,gBAAgB;AACnC,MAAI,WAAW;AACf,QAAM,SAAS,CAAC,SAAS;AACvB,eAAW,QAAQ,IAAI;AACvB,aAAS,QAAQ;AACjB,WAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,iBAAW;AAAA,IACb,CAAC;AAAA,EACH;AACA,QAAM,UAAU,CAAC,SAAS;AACxB,aAAS,QAAQ;AACjB,gBAAY,QAAQ,IAAI;AACxB,aAAS,EAAE,MAAM,YAAY,MAAM,CAAC;AAAA,EACtC;AACA,QAAM,SAAS,CAAC,SAAS;AACvB,aAAS,QAAQ;AACjB,eAAW,QAAQ,IAAI;AACvB,aAAS,EAAE,MAAM,YAAY,KAAK,CAAC;AAAA,EACrC;AACA,SAAO;AAAA,IACL,YAAY,SAAS,MAAM,SAAS,KAAK;AAAA,IACzC;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,WAAW;AAAA,IACrB,WAAW,YAAY;AAAA,IACvB,UAAU,WAAW;AAAA,EACvB;AACF;AAEA,SAAS,aAAa,kBAAkB,SAAS;AAC/C,MAAI,IAAI;AACR,QAAM,YAAY,WAAW,QAAQ,gBAAgB,CAAC;AACtD,QAAM,qBAAqB,cAAc,MAAM;AAC7C,QAAI,KAAK;AACT,UAAM,QAAQ,UAAU,QAAQ;AAChC,cAAU,QAAQ,QAAQ,IAAI,IAAI;AAClC,KAAC,MAAM,WAAW,OAAO,SAAS,QAAQ,WAAW,OAAO,SAAS,IAAI,KAAK,OAAO;AACrF,QAAI,UAAU,SAAS,GAAG;AACxB,yBAAmB,MAAM;AACzB,OAAC,MAAM,WAAW,OAAO,SAAS,QAAQ,eAAe,OAAO,SAAS,IAAI,KAAK,OAAO;AAAA,IAC3F;AAAA,EACF,IAAI,KAAK,WAAW,OAAO,SAAS,QAAQ,aAAa,OAAO,KAAK,KAAK,EAAE,YAAY,KAAK,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,KAAK,MAAM,CAAC;AACjK,QAAM,QAAQ,CAAC,cAAc;AAC3B,QAAI;AACJ,cAAU,SAAS,MAAM,QAAQ,SAAS,MAAM,OAAO,MAAM,QAAQ,gBAAgB;AAAA,EACvF;AACA,QAAM,OAAO,MAAM;AACjB,uBAAmB,MAAM;AACzB,UAAM;AAAA,EACR;AACA,QAAM,SAAS,MAAM;AACnB,QAAI,CAAC,mBAAmB,SAAS,OAAO;AACtC,UAAI,UAAU,QAAQ,GAAG;AACvB,2BAAmB,OAAO;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AACA,QAAM,QAAQ,CAAC,cAAc;AAC3B,UAAM,SAAS;AACf,uBAAmB,OAAO;AAAA,EAC5B;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,mBAAmB;AAAA,IAC1B;AAAA,IACA,UAAU,mBAAmB;AAAA,EAC/B;AACF;AAEA,SAAS,UAAU,MAAM,QAAQ,UAAU,CAAC,GAAG;AAC7C,QAAM,EAAE,QAAAA,UAAS,eAAe,cAAc,UAAU,MAAM,IAAI;AAClE,QAAM,WAAW,WAAW,YAAY;AACxC,QAAM,QAAQ,SAAS,MAAM;AAC3B,QAAI;AACJ,WAAO,aAAa,MAAM,OAAO,KAAKA,WAAU,OAAO,SAASA,QAAO,aAAa,OAAO,SAAS,GAAG;AAAA,EACzG,CAAC;AACD,WAAS,eAAe;AACtB,QAAI;AACJ,UAAM,MAAM,QAAQ,IAAI;AACxB,UAAM,KAAK,QAAQ,KAAK;AACxB,QAAI,MAAMA,WAAU,KAAK;AACvB,YAAM,SAAS,KAAKA,QAAO,iBAAiB,EAAE,EAAE,iBAAiB,GAAG,MAAM,OAAO,SAAS,GAAG,KAAK;AAClG,eAAS,QAAQ,SAAS,SAAS,SAAS;AAAA,IAC9C;AAAA,EACF;AACA,MAAI,SAAS;AACX,wBAAoB,OAAO,cAAc;AAAA,MACvC,iBAAiB,CAAC,SAAS,OAAO;AAAA,MAClC,QAAAA;AAAA,IACF,CAAC;AAAA,EACH;AACA;AAAA,IACE,CAAC,OAAO,MAAM,QAAQ,IAAI,CAAC;AAAA,IAC3B,CAAC,GAAG,QAAQ;AACV,UAAI,IAAI,CAAC,KAAK,IAAI,CAAC;AACjB,YAAI,CAAC,EAAE,MAAM,eAAe,IAAI,CAAC,CAAC;AACpC,mBAAa;AAAA,IACf;AAAA,IACA,EAAE,WAAW,KAAK;AAAA,EACpB;AACA;AAAA,IACE,CAAC,UAAU,KAAK;AAAA,IAChB,CAAC,CAAC,KAAK,EAAE,MAAM;AACb,YAAM,WAAW,QAAQ,IAAI;AAC7B,WAAK,MAAM,OAAO,SAAS,GAAG,UAAU,UAAU;AAChD,YAAI,OAAO;AACT,aAAG,MAAM,eAAe,QAAQ;AAAA;AAEhC,aAAG,MAAM,YAAY,UAAU,GAAG;AAAA,MACtC;AAAA,IACF;AAAA,IACA,EAAE,WAAW,KAAK;AAAA,EACpB;AACA,SAAO;AACT;AAEA,SAAS,kBAAkB,eAAe;AACxC,QAAM,KAAK,mBAAmB;AAC9B,QAAM,iBAAiB;AAAA,IACrB,MAAM;AAAA,IACN,MAAM,gBAAgB,aAAa,aAAa,IAAI,GAAG,MAAM;AAAA,EAC/D;AACA,YAAU,eAAe,OAAO;AAChC,YAAU,eAAe,OAAO;AAChC,SAAO;AACT;AAEA,SAAS,aAAa,MAAM,SAAS;AACnC,QAAM,QAAQ,WAAW,gBAAgB,CAAC;AAC1C,QAAM,UAAUQ,OAAM,IAAI;AAC1B,QAAM,QAAQ,SAAS;AAAA,IACrB,MAAM;AACJ,UAAI;AACJ,YAAM,aAAa,QAAQ;AAC3B,UAAI,UAAU,WAAW,OAAO,SAAS,QAAQ,cAAc,QAAQ,WAAW,MAAM,OAAO,UAAU,IAAI,WAAW,QAAQ,MAAM,KAAK;AAC3I,UAAI,SAAS;AACX,kBAAU,KAAK,WAAW,OAAO,SAAS,QAAQ,kBAAkB,OAAO,KAAK;AAClF,aAAO;AAAA,IACT;AAAA,IACA,IAAI,GAAG;AACL,MAAAC,KAAI,CAAC;AAAA,IACP;AAAA,EACF,CAAC;AACD,WAASA,KAAI,GAAG;AACd,UAAM,aAAa,QAAQ;AAC3B,UAAM,SAAS,WAAW;AAC1B,UAAM,UAAU,IAAI,SAAS,UAAU;AACvC,UAAM,QAAQ,WAAW,MAAM;AAC/B,UAAM,QAAQ;AACd,WAAO;AAAA,EACT;AACA,WAAS,MAAM,QAAQ,GAAG;AACxB,WAAOA,KAAI,MAAM,QAAQ,KAAK;AAAA,EAChC;AACA,WAAS,KAAK,IAAI,GAAG;AACnB,WAAO,MAAM,CAAC;AAAA,EAChB;AACA,WAAS,KAAK,IAAI,GAAG;AACnB,WAAO,MAAM,CAAC,CAAC;AAAA,EACjB;AACA,WAAS,kBAAkB;AACzB,QAAI,IAAI;AACR,YAAQ,KAAK,SAAS,KAAK,WAAW,OAAO,SAAS,QAAQ,iBAAiB,OAAO,KAAK,QAAQ,IAAI,EAAE,CAAC,CAAC,MAAM,OAAO,KAAK;AAAA,EAC/H;AACA,QAAM,SAAS,MAAMA,KAAI,MAAM,KAAK,CAAC;AACrC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,IAAIA;AAAA,EACN;AACF;AAEA,SAAS,QAAQ,UAAU,CAAC,GAAG;AAC7B,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,aAAa;AAAA,EACf,IAAI;AACJ,QAAM,OAAO,aAAa;AAAA,IACxB,GAAG;AAAA,IACH,WAAW,CAAC,OAAO,mBAAmB;AACpC,UAAI;AACJ,UAAI,QAAQ;AACV,SAAC,KAAK,QAAQ,cAAc,OAAO,SAAS,GAAG,KAAK,SAAS,UAAU,QAAQ,gBAAgB,KAAK;AAAA;AAEpG,uBAAe,KAAK;AAAA,IACxB;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,QAAM,SAAS,SAAS,MAAM,KAAK,OAAO,KAAK;AAC/C,QAAM,SAAS,SAAS;AAAA,IACtB,MAAM;AACJ,aAAO,KAAK,UAAU;AAAA,IACxB;AAAA,IACA,IAAI,GAAG;AACL,YAAM,UAAU,IAAI,SAAS;AAC7B,UAAI,OAAO,UAAU;AACnB,aAAK,QAAQ;AAAA;AAEb,aAAK,QAAQ;AAAA,IACjB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,SAAS,SAAS,GAAG;AACnB,SAAO;AACT;AACA,SAAS,YAAY,QAAQ,OAAO;AAClC,SAAO,OAAO,QAAQ;AACxB;AACA,SAAS,YAAY,OAAO;AAC1B,SAAO,QAAQ,OAAO,UAAU,aAAa,QAAQ,cAAc;AACrE;AACA,SAAS,aAAa,OAAO;AAC3B,SAAO,QAAQ,OAAO,UAAU,aAAa,QAAQ,cAAc;AACrE;AACA,SAAS,oBAAoB,QAAQ,UAAU,CAAC,GAAG;AACjD,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,OAAO,YAAY,KAAK;AAAA,IACxB,QAAQ,aAAa,KAAK;AAAA,IAC1B,YAAY;AAAA,EACd,IAAI;AACJ,WAAS,uBAAuB;AAC9B,WAAO,QAAQ;AAAA,MACb,UAAU,KAAK,OAAO,KAAK;AAAA,MAC3B,WAAW,UAAU;AAAA,IACvB,CAAC;AAAA,EACH;AACA,QAAM,OAAO,IAAI,qBAAqB,CAAC;AACvC,QAAM,YAAY,IAAI,CAAC,CAAC;AACxB,QAAM,YAAY,IAAI,CAAC,CAAC;AACxB,QAAM,aAAa,CAAC,WAAW;AAC7B,cAAU,QAAQ,MAAM,OAAO,QAAQ,CAAC;AACxC,SAAK,QAAQ;AAAA,EACf;AACA,QAAM,SAAS,MAAM;AACnB,cAAU,MAAM,QAAQ,KAAK,KAAK;AAClC,SAAK,QAAQ,qBAAqB;AAClC,QAAI,QAAQ,YAAY,UAAU,MAAM,SAAS,QAAQ;AACvD,gBAAU,MAAM,OAAO,QAAQ,UAAU,OAAO,iBAAiB;AACnE,QAAI,UAAU,MAAM;AAClB,gBAAU,MAAM,OAAO,GAAG,UAAU,MAAM,MAAM;AAAA,EACpD;AACA,QAAM,QAAQ,MAAM;AAClB,cAAU,MAAM,OAAO,GAAG,UAAU,MAAM,MAAM;AAChD,cAAU,MAAM,OAAO,GAAG,UAAU,MAAM,MAAM;AAAA,EAClD;AACA,QAAM,OAAO,MAAM;AACjB,UAAM,QAAQ,UAAU,MAAM,MAAM;AACpC,QAAI,OAAO;AACT,gBAAU,MAAM,QAAQ,KAAK,KAAK;AAClC,iBAAW,KAAK;AAAA,IAClB;AAAA,EACF;AACA,QAAM,OAAO,MAAM;AACjB,UAAM,QAAQ,UAAU,MAAM,MAAM;AACpC,QAAI,OAAO;AACT,gBAAU,MAAM,QAAQ,KAAK,KAAK;AAClC,iBAAW,KAAK;AAAA,IAClB;AAAA,EACF;AACA,QAAM,QAAQ,MAAM;AAClB,eAAW,KAAK,KAAK;AAAA,EACvB;AACA,QAAM,UAAU,SAAS,MAAM,CAAC,KAAK,OAAO,GAAG,UAAU,KAAK,CAAC;AAC/D,QAAM,UAAU,SAAS,MAAM,UAAU,MAAM,SAAS,CAAC;AACzD,QAAM,UAAU,SAAS,MAAM,UAAU,MAAM,SAAS,CAAC;AACzD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,cAAc,QAAQ,UAAU,CAAC,GAAG;AAC3C,QAAM;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ,aAAa;AAAA,IACb;AAAA,IACA,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ,IAAI,eAAe,WAAW;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,EAAE,MAAM,OAAO,aAAa,eAAe;AAAA,EAC7C;AACA,WAAS,UAAU,SAAS,OAAO;AACjC,2BAAuB;AACvB,kBAAc,MAAM;AAClB,cAAQ,QAAQ;AAAA,IAClB,CAAC;AAAA,EACH;AACA,QAAM,gBAAgB,oBAAoB,QAAQ,EAAE,GAAG,SAAS,OAAO,QAAQ,SAAS,MAAM,UAAU,CAAC;AACzG,QAAM,EAAE,OAAO,QAAQ,aAAa,IAAI;AACxC,WAAS,SAAS;AAChB,2BAAuB;AACvB,iBAAa;AAAA,EACf;AACA,WAAS,OAAO,WAAW;AACzB,mBAAe;AACf,QAAI;AACF,aAAO;AAAA,EACX;AACA,WAAS,MAAM,IAAI;AACjB,QAAI,WAAW;AACf,UAAM,SAAS,MAAM,WAAW;AAChC,kBAAc,MAAM;AAClB,SAAG,MAAM;AAAA,IACX,CAAC;AACD,QAAI,CAAC;AACH,aAAO;AAAA,EACX;AACA,WAAS,UAAU;AACjB,SAAK;AACL,UAAM;AAAA,EACR;AACA,SAAO;AAAA,IACL,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,uBAAuB,QAAQ,UAAU,CAAC,GAAG;AACpD,QAAM,SAAS,QAAQ,WAAW,eAAe,QAAQ,QAAQ,IAAI;AACrE,QAAM,UAAU,cAAc,QAAQ,EAAE,GAAG,SAAS,aAAa,OAAO,CAAC;AACzE,SAAO;AAAA,IACL,GAAG;AAAA,EACL;AACF;AAEA,SAAS,gBAAgB,UAAU,CAAC,GAAG;AACrC,QAAM;AAAA,IACJ,QAAAT,UAAS;AAAA,IACT,qBAAqB;AAAA,IACrB,cAAc;AAAA,EAChB,IAAI;AACJ,QAAM,cAAc,aAAa,MAAM,OAAO,sBAAsB,WAAW;AAC/E,QAAM,qBAAqB,aAAa,MAAM,YAAY,SAAS,uBAAuB,qBAAqB,OAAO,kBAAkB,sBAAsB,UAAU;AACxK,QAAM,oBAAoB,WAAW,KAAK;AAC1C,QAAM,eAAe,IAAI,EAAE,GAAG,MAAM,GAAG,MAAM,GAAG,KAAK,CAAC;AACtD,QAAM,eAAe,IAAI,EAAE,OAAO,MAAM,MAAM,MAAM,OAAO,KAAK,CAAC;AACjE,QAAM,WAAW,WAAW,CAAC;AAC7B,QAAM,+BAA+B,IAAI;AAAA,IACvC,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACL,CAAC;AACD,WAAS,OAAO;AACd,QAAIA,SAAQ;AACV,YAAM,iBAAiB;AAAA,QACrB;AAAA,QACA,CAAC,UAAU;AACT,cAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACpC,uBAAa,QAAQ;AAAA,YACnB,KAAK,KAAK,MAAM,iBAAiB,OAAO,SAAS,GAAG,MAAM;AAAA,YAC1D,KAAK,KAAK,MAAM,iBAAiB,OAAO,SAAS,GAAG,MAAM;AAAA,YAC1D,KAAK,KAAK,MAAM,iBAAiB,OAAO,SAAS,GAAG,MAAM;AAAA,UAC5D;AACA,uCAA6B,QAAQ;AAAA,YACnC,KAAK,KAAK,MAAM,iCAAiC,OAAO,SAAS,GAAG,MAAM;AAAA,YAC1E,KAAK,KAAK,MAAM,iCAAiC,OAAO,SAAS,GAAG,MAAM;AAAA,YAC1E,KAAK,KAAK,MAAM,iCAAiC,OAAO,SAAS,GAAG,MAAM;AAAA,UAC5E;AACA,uBAAa,QAAQ;AAAA,YACnB,SAAS,KAAK,MAAM,iBAAiB,OAAO,SAAS,GAAG,UAAU;AAAA,YAClE,QAAQ,KAAK,MAAM,iBAAiB,OAAO,SAAS,GAAG,SAAS;AAAA,YAChE,SAAS,KAAK,MAAM,iBAAiB,OAAO,SAAS,GAAG,UAAU;AAAA,UACpE;AACA,mBAAS,QAAQ,MAAM;AAAA,QACzB;AAAA,MACF;AACA,uBAAiBA,SAAQ,gBAAgB,gBAAgB,EAAE,SAAS,KAAK,CAAC;AAAA,IAC5E;AAAA,EACF;AACA,QAAM,oBAAoB,YAAY;AACpC,QAAI,CAAC,mBAAmB;AACtB,wBAAkB,QAAQ;AAC5B,QAAI,kBAAkB;AACpB;AACF,QAAI,mBAAmB,OAAO;AAC5B,YAAM,oBAAoB,kBAAkB;AAC5C,UAAI;AACF,cAAM,WAAW,MAAM,kBAAkB;AACzC,YAAI,aAAa,WAAW;AAC1B,4BAAkB,QAAQ;AAC1B,eAAK;AAAA,QACP;AAAA,MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,KAAK;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AACA,MAAI,YAAY,OAAO;AACrB,QAAI,sBAAsB,mBAAmB,OAAO;AAClD,wBAAkB,EAAE,KAAK,MAAM,KAAK,CAAC;AAAA,IACvC,OAAO;AACL,WAAK;AAAA,IACP;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,qBAAqB,UAAU,CAAC,GAAG;AAC1C,QAAM,EAAE,QAAAA,UAAS,cAAc,IAAI;AACnC,QAAM,cAAc,aAAa,MAAMA,WAAU,4BAA4BA,OAAM;AACnF,QAAM,aAAa,WAAW,KAAK;AACnC,QAAM,QAAQ,WAAW,IAAI;AAC7B,QAAM,OAAO,WAAW,IAAI;AAC5B,QAAM,QAAQ,WAAW,IAAI;AAC7B,MAAIA,WAAU,YAAY,OAAO;AAC/B,qBAAiBA,SAAQ,qBAAqB,CAAC,UAAU;AACvD,iBAAW,QAAQ,MAAM;AACzB,YAAM,QAAQ,MAAM;AACpB,WAAK,QAAQ,MAAM;AACnB,YAAM,QAAQ,MAAM;AAAA,IACtB,GAAG,EAAE,SAAS,KAAK,CAAC;AAAA,EACtB;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,oBAAoB,UAAU,CAAC,GAAG;AACzC,QAAM;AAAA,IACJ,QAAAA,UAAS;AAAA,EACX,IAAI;AACJ,QAAM,aAAa,WAAW,CAAC;AAC/B,QAAM,QAAQ,cAAc,MAAM,gBAAgB,WAAW,KAAK,SAAS,OAAO;AAClF,MAAI,OAAO;AACX,MAAIA,SAAQ;AACV,WAAO,eAAe,OAAO,MAAM,WAAW,QAAQA,QAAO,gBAAgB;AAAA,EAC/E;AACA,SAAO;AAAA,IACL,YAAY,SAAS,UAAU;AAAA,IAC/B;AAAA,EACF;AACF;AAEA,SAAS,eAAe,UAAU,CAAC,GAAG;AACpC,QAAM;AAAA,IACJ,WAAAG,aAAY;AAAA,IACZ,qBAAqB;AAAA,IACrB,cAAc,EAAE,OAAO,MAAM,OAAO,KAAK;AAAA,IACzC,WAAAO;AAAA,EACF,IAAI;AACJ,QAAM,UAAU,IAAI,CAAC,CAAC;AACtB,QAAM,cAAc,SAAS,MAAM,QAAQ,MAAM,OAAO,CAAC,MAAM,EAAE,SAAS,YAAY,CAAC;AACvF,QAAM,cAAc,SAAS,MAAM,QAAQ,MAAM,OAAO,CAAC,MAAM,EAAE,SAAS,YAAY,CAAC;AACvF,QAAM,eAAe,SAAS,MAAM,QAAQ,MAAM,OAAO,CAAC,MAAM,EAAE,SAAS,aAAa,CAAC;AACzF,QAAM,cAAc,aAAa,MAAMP,cAAaA,WAAU,gBAAgBA,WAAU,aAAa,gBAAgB;AACrH,QAAM,oBAAoB,WAAW,KAAK;AAC1C,MAAI;AACJ,iBAAe,SAAS;AACtB,QAAI,CAAC,YAAY;AACf;AACF,YAAQ,QAAQ,MAAMA,WAAU,aAAa,iBAAiB;AAC9D,IAAAO,cAAa,OAAO,SAASA,WAAU,QAAQ,KAAK;AACpD,QAAI,QAAQ;AACV,aAAO,UAAU,EAAE,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC;AAC1C,eAAS;AAAA,IACX;AAAA,EACF;AACA,iBAAe,oBAAoB;AACjC,UAAM,aAAa,YAAY,QAAQ,WAAW;AAClD,QAAI,CAAC,YAAY;AACf,aAAO;AACT,QAAI,kBAAkB;AACpB,aAAO;AACT,UAAM,EAAE,OAAO,MAAM,IAAI,cAAc,YAAY,EAAE,UAAU,KAAK,CAAC;AACrE,UAAM,MAAM;AACZ,QAAI,MAAM,UAAU,WAAW;AAC7B,UAAI,UAAU;AACd,UAAI;AACF,iBAAS,MAAMP,WAAU,aAAa,aAAa,WAAW;AAAA,MAChE,SAAS,GAAG;AACV,iBAAS;AACT,kBAAU;AAAA,MACZ;AACA,aAAO;AACP,wBAAkB,QAAQ;AAAA,IAC5B,OAAO;AACL,wBAAkB,QAAQ;AAAA,IAC5B;AACA,WAAO,kBAAkB;AAAA,EAC3B;AACA,MAAI,YAAY,OAAO;AACrB,QAAI;AACF,wBAAkB;AACpB,qBAAiBA,WAAU,cAAc,gBAAgB,QAAQ,EAAE,SAAS,KAAK,CAAC;AAClF,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,gBAAgB,UAAU,CAAC,GAAG;AACrC,MAAI;AACJ,QAAM,UAAU,YAAY,KAAK,QAAQ,YAAY,OAAO,KAAK,KAAK;AACtE,QAAM,QAAQ,QAAQ;AACtB,QAAM,QAAQ,QAAQ;AACtB,QAAM,EAAE,WAAAA,aAAY,iBAAiB,IAAI;AACzC,QAAM,cAAc,aAAa,MAAM;AACrC,QAAI;AACJ,YAAQ,MAAMA,cAAa,OAAO,SAASA,WAAU,iBAAiB,OAAO,SAAS,IAAI;AAAA,EAC5F,CAAC;AACD,QAAM,aAAa,EAAE,OAAO,MAAM;AAClC,QAAM,SAAS,WAAW;AAC1B,iBAAe,SAAS;AACtB,QAAI;AACJ,QAAI,CAAC,YAAY,SAAS,OAAO;AAC/B;AACF,WAAO,QAAQ,MAAMA,WAAU,aAAa,gBAAgB,UAAU;AACtE,KAAC,MAAM,OAAO,UAAU,OAAO,SAAS,IAAI,UAAU,EAAE,QAAQ,CAAC,MAAM,iBAAiB,GAAG,SAAS,MAAM,EAAE,SAAS,KAAK,CAAC,CAAC;AAC5H,WAAO,OAAO;AAAA,EAChB;AACA,iBAAe,QAAQ;AACrB,QAAI;AACJ,KAAC,MAAM,OAAO,UAAU,OAAO,SAAS,IAAI,UAAU,EAAE,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC;AAC/E,WAAO,QAAQ;AAAA,EACjB;AACA,WAAS,OAAO;AACd,UAAM;AACN,YAAQ,QAAQ;AAAA,EAClB;AACA,iBAAe,QAAQ;AACrB,UAAM,OAAO;AACb,QAAI,OAAO;AACT,cAAQ,QAAQ;AAClB,WAAO,OAAO;AAAA,EAChB;AACA;AAAA,IACE;AAAA,IACA,CAAC,MAAM;AACL,UAAI;AACF,eAAO;AAAA;AAEP,cAAM;AAAA,IACV;AAAA,IACA,EAAE,WAAW,KAAK;AAAA,EACpB;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,sBAAsB,UAAU,CAAC,GAAG;AAC3C,QAAM,EAAE,UAAAF,YAAW,gBAAgB,IAAI;AACvC,MAAI,CAACA;AACH,WAAO,WAAW,SAAS;AAC7B,QAAM,aAAa,WAAWA,UAAS,eAAe;AACtD,mBAAiBA,WAAU,oBAAoB,MAAM;AACnD,eAAW,QAAQA,UAAS;AAAA,EAC9B,GAAG,EAAE,SAAS,KAAK,CAAC;AACpB,SAAO;AACT;AAEA,SAAS,aAAa,QAAQ,UAAU,CAAC,GAAG;AAC1C,MAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA,gBAAAU;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO;AAAA,IACP,kBAAkB;AAAA,IAClB;AAAA,IACA,QAAQ,iBAAiB;AAAA,IACzB,UAAU,CAAC,CAAC;AAAA,EACd,IAAI;AACJ,QAAM,WAAW;AAAA,KACd,KAAK,QAAQ,YAAY,MAAM,OAAO,KAAK,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,EAC3D;AACA,QAAM,eAAe,IAAI;AACzB,QAAM,cAAc,CAAC,MAAM;AACzB,QAAI;AACF,aAAO,aAAa,SAAS,EAAE,WAAW;AAC5C,WAAO;AAAA,EACT;AACA,QAAM,cAAc,CAAC,MAAM;AACzB,QAAI,QAAQA,eAAc;AACxB,QAAE,eAAe;AACnB,QAAI,QAAQ,eAAe;AACzB,QAAE,gBAAgB;AAAA,EACtB;AACA,QAAM,QAAQ,CAAC,MAAM;AACnB,QAAI;AACJ,QAAI,CAAC,QAAQ,OAAO,EAAE,SAAS,EAAE,MAAM;AACrC;AACF,QAAI,QAAQ,QAAQ,QAAQ,KAAK,CAAC,YAAY,CAAC;AAC7C;AACF,QAAI,QAAQ,KAAK,KAAK,EAAE,WAAW,QAAQ,MAAM;AAC/C;AACF,UAAM,YAAY,QAAQ,gBAAgB;AAC1C,UAAM,iBAAiB,MAAM,aAAa,OAAO,SAAS,UAAU,0BAA0B,OAAO,SAAS,IAAI,KAAK,SAAS;AAChI,UAAM,aAAa,QAAQ,MAAM,EAAE,sBAAsB;AACzD,UAAM,MAAM;AAAA,MACV,GAAG,EAAE,WAAW,YAAY,WAAW,OAAO,cAAc,OAAO,UAAU,aAAa,WAAW;AAAA,MACrG,GAAG,EAAE,WAAW,YAAY,WAAW,MAAM,cAAc,MAAM,UAAU,YAAY,WAAW;AAAA,IACpG;AACA,SAAK,WAAW,OAAO,SAAS,QAAQ,KAAK,CAAC,OAAO;AACnD;AACF,iBAAa,QAAQ;AACrB,gBAAY,CAAC;AAAA,EACf;AACA,QAAM,OAAO,CAAC,MAAM;AAClB,QAAI,QAAQ,QAAQ,QAAQ,KAAK,CAAC,YAAY,CAAC;AAC7C;AACF,QAAI,CAAC,aAAa;AAChB;AACF,UAAM,YAAY,QAAQ,gBAAgB;AAC1C,UAAM,aAAa,QAAQ,MAAM,EAAE,sBAAsB;AACzD,QAAI,EAAE,GAAG,EAAE,IAAI,SAAS;AACxB,QAAI,SAAS,OAAO,SAAS,QAAQ;AACnC,UAAI,EAAE,UAAU,aAAa,MAAM;AACnC,UAAI;AACF,YAAI,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,UAAU,cAAc,WAAW,KAAK;AAAA,IACzE;AACA,QAAI,SAAS,OAAO,SAAS,QAAQ;AACnC,UAAI,EAAE,UAAU,aAAa,MAAM;AACnC,UAAI;AACF,YAAI,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,UAAU,eAAe,WAAW,MAAM;AAAA,IAC3E;AACA,aAAS,QAAQ;AAAA,MACf;AAAA,MACA;AAAA,IACF;AACA,cAAU,OAAO,SAAS,OAAO,SAAS,OAAO,CAAC;AAClD,gBAAY,CAAC;AAAA,EACf;AACA,QAAM,MAAM,CAAC,MAAM;AACjB,QAAI,QAAQ,QAAQ,QAAQ,KAAK,CAAC,YAAY,CAAC;AAC7C;AACF,QAAI,CAAC,aAAa;AAChB;AACF,iBAAa,QAAQ;AACrB,aAAS,OAAO,SAAS,MAAM,SAAS,OAAO,CAAC;AAChD,gBAAY,CAAC;AAAA,EACf;AACA,MAAI,UAAU;AACZ,UAAM,SAAS,MAAM;AACnB,UAAI;AACJ,aAAO;AAAA,QACL,UAAU,MAAM,QAAQ,YAAY,OAAO,MAAM;AAAA,QACjD,SAAS,CAAC,QAAQA,eAAc;AAAA,MAClC;AAAA,IACF;AACA,qBAAiB,gBAAgB,eAAe,OAAO,MAAM;AAC7D,qBAAiB,iBAAiB,eAAe,MAAM,MAAM;AAC7D,qBAAiB,iBAAiB,aAAa,KAAK,MAAM;AAAA,EAC5D;AACA,SAAO;AAAA,IACL,GAAGC,QAAO,QAAQ;AAAA,IAClB;AAAA,IACA,YAAY,SAAS,MAAM,CAAC,CAAC,aAAa,KAAK;AAAA,IAC/C,OAAO;AAAA,MACL,MAAM,QAAQ,SAAS,MAAM,CAAC,UAAU,SAAS,MAAM,CAAC;AAAA,IAC1D;AAAA,EACF;AACF;AAEA,SAAS,YAAY,QAAQ,UAAU,CAAC,GAAG;AACzC,MAAI,IAAI;AACR,QAAM,iBAAiB,WAAW,KAAK;AACvC,QAAM,QAAQ,WAAW,IAAI;AAC7B,MAAI,UAAU;AACd,MAAI,UAAU;AACd,MAAI,UAAU;AACZ,UAAM,WAAW,OAAO,YAAY,aAAa,EAAE,QAAQ,QAAQ,IAAI;AACvE,UAAM,YAAY,KAAK,SAAS,aAAa,OAAO,KAAK;AACzD,UAAM,8BAA8B,KAAK,SAAS,+BAA+B,OAAO,KAAK;AAC7F,UAAM,WAAW,CAAC,UAAU;AAC1B,UAAI,KAAK;AACT,YAAM,OAAO,MAAM,MAAM,OAAO,MAAM,MAAM,iBAAiB,OAAO,SAAS,IAAI,UAAU,OAAO,MAAM,CAAC,CAAC;AAC1G,aAAO,KAAK,WAAW,IAAI,OAAO,WAAW,OAAO,CAAC,KAAK,CAAC,CAAC;AAAA,IAC9D;AACA,UAAM,iBAAiB,CAAC,UAAU;AAChC,YAAM,YAAY,MAAM,SAAS,SAAS;AAC1C,UAAI,OAAO,cAAc;AACvB,eAAO,UAAU,KAAK;AACxB,UAAI,EAAE,aAAa,OAAO,SAAS,UAAU;AAC3C,eAAO;AACT,UAAI,MAAM,WAAW;AACnB,eAAO;AACT,aAAO,MAAM;AAAA,QACX,CAAC,SAAS,UAAU,KAAK,CAAC,gBAAgB,KAAK,SAAS,WAAW,CAAC;AAAA,MACtE;AAAA,IACF;AACA,UAAM,gBAAgB,CAAC,UAAU;AAC/B,YAAM,QAAQ,MAAM,KAAK,SAAS,OAAO,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,KAAK,IAAI;AAC5E,YAAM,iBAAiB,eAAe,KAAK;AAC3C,YAAM,qBAAqB,YAAY,MAAM,UAAU;AACvD,aAAO,kBAAkB;AAAA,IAC3B;AACA,UAAM,WAAW,MAAM,mCAAmC,KAAK,UAAU,SAAS,KAAK,EAAE,YAAY;AACrG,UAAM,kBAAkB,CAAC,OAAO,cAAc;AAC5C,UAAI,KAAK,KAAK,IAAI,IAAI,IAAI;AAC1B,YAAM,wBAAwB,MAAM,MAAM,iBAAiB,OAAO,SAAS,IAAI;AAC/E,iBAAW,MAAM,wBAAwB,cAAc,oBAAoB,MAAM,OAAO,MAAM;AAC9F,UAAI,4BAA4B;AAC9B,cAAM,eAAe;AAAA,MACvB;AACA,UAAI,CAAC,SAAS,KAAK,CAAC,SAAS;AAC3B,YAAI,MAAM,cAAc;AACtB,gBAAM,aAAa,aAAa;AAAA,QAClC;AACA;AAAA,MACF;AACA,YAAM,eAAe;AACrB,UAAI,MAAM,cAAc;AACtB,cAAM,aAAa,aAAa;AAAA,MAClC;AACA,YAAM,eAAe,SAAS,KAAK;AACnC,cAAQ,WAAW;AAAA,QACjB,KAAK;AACH,qBAAW;AACX,yBAAe,QAAQ;AACvB,WAAC,KAAK,SAAS,YAAY,OAAO,SAAS,GAAG,KAAK,UAAU,MAAM,KAAK;AACxE;AAAA,QACF,KAAK;AACH,WAAC,KAAK,SAAS,WAAW,OAAO,SAAS,GAAG,KAAK,UAAU,MAAM,KAAK;AACvE;AAAA,QACF,KAAK;AACH,qBAAW;AACX,cAAI,YAAY;AACd,2BAAe,QAAQ;AACzB,WAAC,KAAK,SAAS,YAAY,OAAO,SAAS,GAAG,KAAK,UAAU,MAAM,KAAK;AACxE;AAAA,QACF,KAAK;AACH,oBAAU;AACV,yBAAe,QAAQ;AACvB,cAAI,SAAS;AACX,kBAAM,QAAQ;AACd,aAAC,KAAK,SAAS,WAAW,OAAO,SAAS,GAAG,KAAK,UAAU,cAAc,KAAK;AAAA,UACjF;AACA;AAAA,MACJ;AAAA,IACF;AACA,qBAAiB,QAAQ,aAAa,CAAC,UAAU,gBAAgB,OAAO,OAAO,CAAC;AAChF,qBAAiB,QAAQ,YAAY,CAAC,UAAU,gBAAgB,OAAO,MAAM,CAAC;AAC9E,qBAAiB,QAAQ,aAAa,CAAC,UAAU,gBAAgB,OAAO,OAAO,CAAC;AAChF,qBAAiB,QAAQ,QAAQ,CAAC,UAAU,gBAAgB,OAAO,MAAM,CAAC;AAAA,EAC5E;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,kBAAkB,QAAQ,UAAU,UAAU,CAAC,GAAG;AACzD,QAAM,EAAE,QAAAZ,UAAS,eAAe,GAAG,gBAAgB,IAAI;AACvD,MAAI;AACJ,QAAM,cAAc,aAAa,MAAMA,WAAU,oBAAoBA,OAAM;AAC3E,QAAM,UAAU,MAAM;AACpB,QAAI,UAAU;AACZ,eAAS,WAAW;AACpB,iBAAW;AAAA,IACb;AAAA,EACF;AACA,QAAM,UAAU,SAAS,MAAM;AAC7B,UAAM,WAAW,QAAQ,MAAM;AAC/B,WAAO,MAAM,QAAQ,QAAQ,IAAI,SAAS,IAAI,CAAC,OAAO,aAAa,EAAE,CAAC,IAAI,CAAC,aAAa,QAAQ,CAAC;AAAA,EACnG,CAAC;AACD,QAAM,YAAY;AAAA,IAChB;AAAA,IACA,CAAC,QAAQ;AACP,cAAQ;AACR,UAAI,YAAY,SAASA,SAAQ;AAC/B,mBAAW,IAAI,eAAe,QAAQ;AACtC,mBAAW,OAAO,KAAK;AACrB,cAAI;AACF,qBAAS,QAAQ,KAAK,eAAe;AAAA,QACzC;AAAA,MACF;AAAA,IACF;AAAA,IACA,EAAE,WAAW,MAAM,OAAO,OAAO;AAAA,EACnC;AACA,QAAM,OAAO,MAAM;AACjB,YAAQ;AACR,cAAU;AAAA,EACZ;AACA,oBAAkB,IAAI;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,mBAAmB,QAAQ,UAAU,CAAC,GAAG;AAChD,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,eAAe;AAAA,EACjB,IAAI;AACJ,QAAM,SAAS,WAAW,CAAC;AAC3B,QAAM,SAAS,WAAW,CAAC;AAC3B,QAAM,OAAO,WAAW,CAAC;AACzB,QAAM,QAAQ,WAAW,CAAC;AAC1B,QAAM,MAAM,WAAW,CAAC;AACxB,QAAM,QAAQ,WAAW,CAAC;AAC1B,QAAM,IAAI,WAAW,CAAC;AACtB,QAAM,IAAI,WAAW,CAAC;AACtB,WAAS,cAAc;AACrB,UAAM,KAAK,aAAa,MAAM;AAC9B,QAAI,CAAC,IAAI;AACP,UAAI,OAAO;AACT,eAAO,QAAQ;AACf,eAAO,QAAQ;AACf,aAAK,QAAQ;AACb,cAAM,QAAQ;AACd,YAAI,QAAQ;AACZ,cAAM,QAAQ;AACd,UAAE,QAAQ;AACV,UAAE,QAAQ;AAAA,MACZ;AACA;AAAA,IACF;AACA,UAAM,OAAO,GAAG,sBAAsB;AACtC,WAAO,QAAQ,KAAK;AACpB,WAAO,QAAQ,KAAK;AACpB,SAAK,QAAQ,KAAK;AAClB,UAAM,QAAQ,KAAK;AACnB,QAAI,QAAQ,KAAK;AACjB,UAAM,QAAQ,KAAK;AACnB,MAAE,QAAQ,KAAK;AACf,MAAE,QAAQ,KAAK;AAAA,EACjB;AACA,WAAS,SAAS;AAChB,QAAI,iBAAiB;AACnB,kBAAY;AAAA,aACL,iBAAiB;AACxB,4BAAsB,MAAM,YAAY,CAAC;AAAA,EAC7C;AACA,oBAAkB,QAAQ,MAAM;AAChC,QAAM,MAAM,aAAa,MAAM,GAAG,CAAC,QAAQ,CAAC,OAAO,OAAO,CAAC;AAC3D,sBAAoB,QAAQ,QAAQ;AAAA,IAClC,iBAAiB,CAAC,SAAS,OAAO;AAAA,EACpC,CAAC;AACD,MAAI;AACF,qBAAiB,UAAU,QAAQ,EAAE,SAAS,MAAM,SAAS,KAAK,CAAC;AACrE,MAAI;AACF,qBAAiB,UAAU,QAAQ,EAAE,SAAS,KAAK,CAAC;AACtD,eAAa,MAAM;AACjB,QAAI;AACF,aAAO;AAAA,EACX,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,kBAAkB,SAAS;AAClC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,UAAAC,YAAW;AAAA,IACX;AAAA,IACA,WAAW;AAAA,IACX,YAAY;AAAA,EACd,IAAI;AACJ,QAAM,cAAc,aAAa,MAAM;AACrC,QAAI,QAAQ,QAAQ;AAClB,aAAOA,aAAY,uBAAuBA;AAC5C,WAAOA,aAAY,sBAAsBA;AAAA,EAC3C,CAAC;AACD,QAAM,UAAU,WAAW,IAAI;AAC/B,QAAM,KAAK,MAAM;AACf,QAAI,IAAI;AACR,YAAQ,QAAQ,QAAQ,QAAQ,KAAK,KAAKA,aAAY,OAAO,SAASA,UAAS,kBAAkB,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC,MAAM,OAAO,KAAK,CAAC,KAAK,KAAKA,aAAY,OAAO,SAASA,UAAS,iBAAiB,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC,MAAM,OAAO,KAAK;AAAA,EACpP;AACA,QAAM,WAAW,aAAa,0BAA0B,SAAS,IAAI,EAAE,UAAU,CAAC,IAAI,cAAc,IAAI,UAAU,EAAE,UAAU,CAAC;AAC/H,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL;AACF;AAEA,SAAS,gBAAgB,IAAI,UAAU,CAAC,GAAG;AACzC,QAAM;AAAA,IACJ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,QAAAD,UAAS;AAAA,EACX,IAAI;AACJ,QAAM,YAAY,WAAW,KAAK;AAClC,MAAI;AACJ,QAAM,SAAS,CAAC,aAAa;AAC3B,UAAM,QAAQ,WAAW,aAAa;AACtC,QAAI,OAAO;AACT,mBAAa,KAAK;AAClB,cAAQ;AAAA,IACV;AACA,QAAI;AACF,cAAQ,WAAW,MAAM,UAAU,QAAQ,UAAU,KAAK;AAAA;AAE1D,gBAAU,QAAQ;AAAA,EACtB;AACA,MAAI,CAACA;AACH,WAAO;AACT,mBAAiB,IAAI,cAAc,MAAM,OAAO,IAAI,GAAG,EAAE,SAAS,KAAK,CAAC;AACxE,mBAAiB,IAAI,cAAc,MAAM,OAAO,KAAK,GAAG,EAAE,SAAS,KAAK,CAAC;AACzE,MAAI,kBAAkB;AACpB;AAAA,MACE,SAAS,MAAM,aAAa,EAAE,CAAC;AAAA,MAC/B,MAAM,OAAO,KAAK;AAAA,IACpB;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,eAAe,QAAQ,cAAc,EAAE,OAAO,GAAG,QAAQ,EAAE,GAAG,UAAU,CAAC,GAAG;AACnF,QAAM,EAAE,QAAAA,UAAS,eAAe,MAAM,cAAc,IAAI;AACxD,QAAM,QAAQ,SAAS,MAAM;AAC3B,QAAI,IAAI;AACR,YAAQ,MAAM,KAAK,aAAa,MAAM,MAAM,OAAO,SAAS,GAAG,iBAAiB,OAAO,SAAS,GAAG,SAAS,KAAK;AAAA,EACnH,CAAC;AACD,QAAM,QAAQ,WAAW,YAAY,KAAK;AAC1C,QAAM,SAAS,WAAW,YAAY,MAAM;AAC5C,QAAM,EAAE,MAAM,MAAM,IAAI;AAAA,IACtB;AAAA,IACA,CAAC,CAAC,KAAK,MAAM;AACX,YAAM,UAAU,QAAQ,eAAe,MAAM,gBAAgB,QAAQ,gBAAgB,MAAM,iBAAiB,MAAM;AAClH,UAAIA,WAAU,MAAM,OAAO;AACzB,cAAM,QAAQ,aAAa,MAAM;AACjC,YAAI,OAAO;AACT,gBAAM,OAAO,MAAM,sBAAsB;AACzC,gBAAM,QAAQ,KAAK;AACnB,iBAAO,QAAQ,KAAK;AAAA,QACtB;AAAA,MACF,OAAO;AACL,YAAI,SAAS;AACX,gBAAM,gBAAgB,QAAQ,OAAO;AACrC,gBAAM,QAAQ,cAAc,OAAO,CAAC,KAAK,EAAE,WAAW,MAAM,MAAM,YAAY,CAAC;AAC/E,iBAAO,QAAQ,cAAc,OAAO,CAAC,KAAK,EAAE,UAAU,MAAM,MAAM,WAAW,CAAC;AAAA,QAChF,OAAO;AACL,gBAAM,QAAQ,MAAM,YAAY;AAChC,iBAAO,QAAQ,MAAM,YAAY;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,eAAa,MAAM;AACjB,UAAM,MAAM,aAAa,MAAM;AAC/B,QAAI,KAAK;AACP,YAAM,QAAQ,iBAAiB,MAAM,IAAI,cAAc,YAAY;AACnE,aAAO,QAAQ,kBAAkB,MAAM,IAAI,eAAe,YAAY;AAAA,IACxE;AAAA,EACF,CAAC;AACD,QAAM,QAAQ;AAAA,IACZ,MAAM,aAAa,MAAM;AAAA,IACzB,CAAC,QAAQ;AACP,YAAM,QAAQ,MAAM,YAAY,QAAQ;AACxC,aAAO,QAAQ,MAAM,YAAY,SAAS;AAAA,IAC5C;AAAA,EACF;AACA,WAAS,OAAO;AACd,UAAM;AACN,UAAM;AAAA,EACR;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,wBAAwB,QAAQ,UAAU,UAAU,CAAC,GAAG;AAC/D,QAAM;AAAA,IACJ;AAAA,IACA,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,QAAAA,UAAS;AAAA,IACT,YAAY;AAAA,EACd,IAAI;AACJ,QAAM,cAAc,aAAa,MAAMA,WAAU,0BAA0BA,OAAM;AACjF,QAAM,UAAU,SAAS,MAAM;AAC7B,UAAM,UAAU,QAAQ,MAAM;AAC9B,WAAO,QAAQ,OAAO,EAAE,IAAI,YAAY,EAAE,OAAO,UAAU;AAAA,EAC7D,CAAC;AACD,MAAI,UAAU;AACd,QAAM,WAAW,WAAW,SAAS;AACrC,QAAM,YAAY,YAAY,QAAQ;AAAA,IACpC,MAAM,CAAC,QAAQ,OAAO,aAAa,IAAI,GAAG,SAAS,KAAK;AAAA,IACxD,CAAC,CAAC,UAAU,KAAK,MAAM;AACrB,cAAQ;AACR,UAAI,CAAC,SAAS;AACZ;AACF,UAAI,CAAC,SAAS;AACZ;AACF,YAAM,WAAW,IAAI;AAAA,QACnB;AAAA,QACA;AAAA,UACE,MAAM,aAAa,KAAK;AAAA,UACxB;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,eAAS,QAAQ,CAAC,OAAO,MAAM,SAAS,QAAQ,EAAE,CAAC;AACnD,gBAAU,MAAM;AACd,iBAAS,WAAW;AACpB,kBAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,EAAE,WAAW,OAAO,OAAO;AAAA,EAC7B,IAAI;AACJ,QAAM,OAAO,MAAM;AACjB,YAAQ;AACR,cAAU;AACV,aAAS,QAAQ;AAAA,EACnB;AACA,oBAAkB,IAAI;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,QAAQ;AACN,cAAQ;AACR,eAAS,QAAQ;AAAA,IACnB;AAAA,IACA,SAAS;AACP,eAAS,QAAQ;AAAA,IACnB;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,qBAAqB,SAAS,UAAU,CAAC,GAAG;AACnD,QAAM;AAAA,IACJ,QAAAA,UAAS;AAAA,IACT;AAAA,IACA,YAAY;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,EACT,IAAI;AACJ,QAAM,mBAAmB,WAAW,KAAK;AACzC,QAAM,EAAE,KAAK,IAAI;AAAA,IACf;AAAA,IACA,CAAC,gCAAgC;AAC/B,UAAI,iBAAiB,iBAAiB;AACtC,UAAI,aAAa;AACjB,iBAAW,SAAS,6BAA6B;AAC/C,YAAI,MAAM,QAAQ,YAAY;AAC5B,uBAAa,MAAM;AACnB,2BAAiB,MAAM;AAAA,QACzB;AAAA,MACF;AACA,uBAAiB,QAAQ;AACzB,UAAI,MAAM;AACR,kBAAU,kBAAkB,MAAM;AAChC,eAAK;AAAA,QACP,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,QAAAA;AAAA,MACA;AAAA,MACA,YAAY,QAAQ,UAAU;AAAA,IAChC;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,SAAyB,oBAAI,IAAI;AAEvC,SAAS,YAAY,KAAK;AACxB,QAAM,QAAQ,gBAAgB;AAC9B,WAAS,GAAG,UAAU;AACpB,QAAI;AACJ,UAAM,YAAY,OAAO,IAAI,GAAG,KAAqB,oBAAI,IAAI;AAC7D,cAAU,IAAI,QAAQ;AACtB,WAAO,IAAI,KAAK,SAAS;AACzB,UAAM,OAAO,MAAM,IAAI,QAAQ;AAC/B,KAAC,KAAK,SAAS,OAAO,SAAS,MAAM,aAAa,OAAO,SAAS,GAAG,KAAK,IAAI;AAC9E,WAAO;AAAA,EACT;AACA,WAAS,KAAK,UAAU;AACtB,aAAS,aAAa,MAAM;AAC1B,UAAI,SAAS;AACb,eAAS,GAAG,IAAI;AAAA,IAClB;AACA,WAAO,GAAG,SAAS;AAAA,EACrB;AACA,WAAS,IAAI,UAAU;AACrB,UAAM,YAAY,OAAO,IAAI,GAAG;AAChC,QAAI,CAAC;AACH;AACF,cAAU,OAAO,QAAQ;AACzB,QAAI,CAAC,UAAU;AACb,YAAM;AAAA,EACV;AACA,WAAS,QAAQ;AACf,WAAO,OAAO,GAAG;AAAA,EACnB;AACA,WAAS,KAAK,OAAO,SAAS;AAC5B,QAAI;AACJ,KAAC,KAAK,OAAO,IAAI,GAAG,MAAM,OAAO,SAAS,GAAG,QAAQ,CAAC,MAAM,EAAE,OAAO,OAAO,CAAC;AAAA,EAC/E;AACA,SAAO,EAAE,IAAI,MAAM,KAAK,MAAM,MAAM;AACtC;AAEA,SAAS,uBAAuB,SAAS;AACvC,MAAI,YAAY;AACd,WAAO,CAAC;AACV,SAAO;AACT;AACA,SAAS,eAAe,KAAKI,UAAS,CAAC,GAAG,UAAU,CAAC,GAAG;AACtD,QAAM,QAAQ,WAAW,IAAI;AAC7B,QAAM,OAAO,WAAW,IAAI;AAC5B,QAAM,SAAS,WAAW,YAAY;AACtC,QAAM,cAAc,IAAI,IAAI;AAC5B,QAAM,QAAQ,WAAW,IAAI;AAC7B,QAAM,SAASI,OAAM,GAAG;AACxB,QAAM,cAAc,WAAW,IAAI;AACnC,MAAI,mBAAmB;AACvB,MAAI,UAAU;AACd,QAAM;AAAA,IACJ,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,cAAc;AAAA,IACd;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ,MAAM;AAClB,QAAI,YAAY,YAAY,OAAO;AACjC,kBAAY,MAAM,MAAM;AACxB,kBAAY,QAAQ;AACpB,aAAO,QAAQ;AACf,yBAAmB;AAAA,IACrB;AAAA,EACF;AACA,QAAM,QAAQ,MAAM;AAClB,QAAI,oBAAoB,OAAO,OAAO,UAAU;AAC9C;AACF,UAAM,KAAK,IAAI,YAAY,OAAO,OAAO,EAAE,gBAAgB,CAAC;AAC5D,WAAO,QAAQ;AACf,gBAAY,QAAQ;AACpB,OAAG,SAAS,MAAM;AAChB,aAAO,QAAQ;AACf,YAAM,QAAQ;AAAA,IAChB;AACA,OAAG,UAAU,CAAC,MAAM;AAClB,aAAO,QAAQ;AACf,YAAM,QAAQ;AACd,UAAI,GAAG,eAAe,KAAK,CAAC,oBAAoB,eAAe;AAC7D,WAAG,MAAM;AACT,cAAM;AAAA,UACJ,UAAU;AAAA,UACV,QAAQ;AAAA,UACR;AAAA,QACF,IAAI,uBAAuB,aAAa;AACxC,mBAAW;AACX,YAAI,OAAO,YAAY,aAAa,UAAU,KAAK,UAAU;AAC3D,qBAAW,OAAO,KAAK;AAAA,iBAChB,OAAO,YAAY,cAAc,QAAQ;AAChD,qBAAW,OAAO,KAAK;AAAA;AAEvB,sBAAY,OAAO,SAAS,SAAS;AAAA,MACzC;AAAA,IACF;AACA,OAAG,YAAY,CAAC,MAAM;AACpB,YAAM,QAAQ;AACd,WAAK,QAAQ,EAAE;AACf,kBAAY,QAAQ,EAAE;AAAA,IACxB;AACA,eAAW,cAAcJ,SAAQ;AAC/B,uBAAiB,IAAI,YAAY,CAAC,MAAM;AACtC,cAAM,QAAQ;AACd,aAAK,QAAQ,EAAE,QAAQ;AAAA,MACzB,GAAG,EAAE,SAAS,KAAK,CAAC;AAAA,IACtB;AAAA,EACF;AACA,QAAM,OAAO,MAAM;AACjB,QAAI,CAAC;AACH;AACF,UAAM;AACN,uBAAmB;AACnB,cAAU;AACV,UAAM;AAAA,EACR;AACA,MAAI;AACF,SAAK;AACP,MAAI;AACF,UAAM,QAAQ,IAAI;AACpB,oBAAkB,KAAK;AACvB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,cAAc,UAAU,CAAC,GAAG;AACnC,QAAM,EAAE,eAAe,GAAG,IAAI;AAC9B,QAAM,cAAc,aAAa,MAAM,OAAO,WAAW,eAAe,gBAAgB,MAAM;AAC9F,QAAM,UAAU,WAAW,YAAY;AACvC,iBAAe,KAAK,aAAa;AAC/B,QAAI,CAAC,YAAY;AACf;AACF,UAAM,aAAa,IAAI,OAAO,WAAW;AACzC,UAAM,SAAS,MAAM,WAAW,KAAK,WAAW;AAChD,YAAQ,QAAQ,OAAO;AACvB,WAAO;AAAA,EACT;AACA,SAAO,EAAE,aAAa,SAAS,KAAK;AACtC;AAEA,SAAS,WAAW,UAAU,MAAM,UAAU,CAAC,GAAG;AAChD,QAAM;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,UAAAH,YAAW;AAAA,EACb,IAAI;AACJ,QAAM,UAAUO,OAAM,OAAO;AAC7B,QAAM,YAAY,CAAC,SAAS;AAC1B,UAAM,WAAWP,aAAY,OAAO,SAASA,UAAS,KAAK,iBAAiB,cAAc,GAAG,IAAI;AACjG,QAAI,CAAC,YAAY,SAAS,WAAW,GAAG;AACtC,YAAM,OAAOA,aAAY,OAAO,SAASA,UAAS,cAAc,MAAM;AACtE,UAAI,MAAM;AACR,aAAK,MAAM;AACX,aAAK,OAAO,GAAG,OAAO,GAAG,IAAI;AAC7B,aAAK,OAAO,SAAS,KAAK,MAAM,GAAG,EAAE,IAAI,CAAC;AAC1C,QAAAA,aAAY,OAAO,SAASA,UAAS,KAAK,OAAO,IAAI;AAAA,MACvD;AACA;AAAA,IACF;AACA,gBAAY,OAAO,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,OAAO,GAAG,OAAO,GAAG,IAAI,EAAE;AAAA,EACpF;AACA;AAAA,IACE;AAAA,IACA,CAAC,GAAG,MAAM;AACR,UAAI,OAAO,MAAM,YAAY,MAAM;AACjC,kBAAU,CAAC;AAAA,IACf;AAAA,IACA,EAAE,WAAW,KAAK;AAAA,EACpB;AACA,SAAO;AACT;AAEA,IAAM,iBAAiB;AAAA,EACrB,MAAM;AAAA,EACN,MAAM;AACR;AACA,SAAS,eAAe,KAAK;AAC3B,SAAO,OAAO,aAAa,KAAK,aAAa,WAAW,eAAe,WAAW,eAAe,cAAc,gBAAgB,SAAS,mBAAmB;AAC7J;AACA,IAAM,aAAa;AACnB,SAAS,cAAc,KAAK;AAC1B,SAAO,WAAW,KAAK,GAAG;AAC5B;AACA,SAAS,gBAAgB,SAAS;AAChC,MAAI,OAAO,YAAY,eAAe,mBAAmB;AACvD,WAAO,OAAO,YAAY,QAAQ,QAAQ,CAAC;AAC7C,SAAO;AACT;AACA,SAAS,iBAAiB,gBAAgB,WAAW;AACnD,MAAI,gBAAgB,aAAa;AAC/B,WAAO,OAAO,QAAQ;AACpB,UAAI;AACJ,eAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,KAAK;AAC9C,YAAI,UAAU,CAAC,KAAK,MAAM;AACxB,qBAAW,UAAU,CAAC;AACtB;AAAA,QACF;AAAA,MACF;AACA,UAAI;AACF,eAAO,EAAE,GAAG,KAAK,GAAG,MAAM,SAAS,GAAG,EAAE;AAC1C,aAAO;AAAA,IACT;AAAA,EACF,OAAO;AACL,WAAO,OAAO,QAAQ;AACpB,iBAAW,YAAY,WAAW;AAChC,YAAI;AACF,gBAAM,EAAE,GAAG,KAAK,GAAG,MAAM,SAAS,GAAG,EAAE;AAAA,MAC3C;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,SAAS,YAAY,SAAS,CAAC,GAAG;AAChC,QAAM,eAAe,OAAO,eAAe;AAC3C,QAAM,WAAW,OAAO,WAAW,CAAC;AACpC,QAAM,gBAAgB,OAAO,gBAAgB,CAAC;AAC9C,WAAS,gBAAgB,QAAQ,MAAM;AACrC,UAAM,cAAc,SAAS,MAAM;AACjC,YAAM,UAAU,QAAQ,OAAO,OAAO;AACtC,YAAM,YAAY,QAAQ,GAAG;AAC7B,aAAO,WAAW,CAAC,cAAc,SAAS,IAAI,UAAU,SAAS,SAAS,IAAI;AAAA,IAChF,CAAC;AACD,QAAI,UAAU;AACd,QAAI,eAAe;AACnB,QAAI,KAAK,SAAS,GAAG;AACnB,UAAI,eAAe,KAAK,CAAC,CAAC,GAAG;AAC3B,kBAAU;AAAA,UACR,GAAG;AAAA,UACH,GAAG,KAAK,CAAC;AAAA,UACT,aAAa,iBAAiB,cAAc,SAAS,aAAa,KAAK,CAAC,EAAE,WAAW;AAAA,UACrF,YAAY,iBAAiB,cAAc,SAAS,YAAY,KAAK,CAAC,EAAE,UAAU;AAAA,UAClF,cAAc,iBAAiB,cAAc,SAAS,cAAc,KAAK,CAAC,EAAE,YAAY;AAAA,QAC1F;AAAA,MACF,OAAO;AACL,uBAAe;AAAA,UACb,GAAG;AAAA,UACH,GAAG,KAAK,CAAC;AAAA,UACT,SAAS;AAAA,YACP,GAAG,gBAAgB,aAAa,OAAO,KAAK,CAAC;AAAA,YAC7C,GAAG,gBAAgB,KAAK,CAAC,EAAE,OAAO,KAAK,CAAC;AAAA,UAC1C;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK,SAAS,KAAK,eAAe,KAAK,CAAC,CAAC,GAAG;AAC9C,gBAAU;AAAA,QACR,GAAG;AAAA,QACH,GAAG,KAAK,CAAC;AAAA,QACT,aAAa,iBAAiB,cAAc,SAAS,aAAa,KAAK,CAAC,EAAE,WAAW;AAAA,QACrF,YAAY,iBAAiB,cAAc,SAAS,YAAY,KAAK,CAAC,EAAE,UAAU;AAAA,QAClF,cAAc,iBAAiB,cAAc,SAAS,cAAc,KAAK,CAAC,EAAE,YAAY;AAAA,MAC1F;AAAA,IACF;AACA,WAAO,SAAS,aAAa,cAAc,OAAO;AAAA,EACpD;AACA,SAAO;AACT;AACA,SAAS,SAAS,QAAQ,MAAM;AAC9B,MAAI;AACJ,QAAM,gBAAgB,OAAO,oBAAoB;AACjD,MAAI,eAAe,CAAC;AACpB,MAAI,UAAU;AAAA,IACZ,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,mBAAmB;AAAA,EACrB;AACA,QAAM,SAAS;AAAA,IACb,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACA,MAAI,KAAK,SAAS,GAAG;AACnB,QAAI,eAAe,KAAK,CAAC,CAAC;AACxB,gBAAU,EAAE,GAAG,SAAS,GAAG,KAAK,CAAC,EAAE;AAAA;AAEnC,qBAAe,KAAK,CAAC;AAAA,EACzB;AACA,MAAI,KAAK,SAAS,GAAG;AACnB,QAAI,eAAe,KAAK,CAAC,CAAC;AACxB,gBAAU,EAAE,GAAG,SAAS,GAAG,KAAK,CAAC,EAAE;AAAA,EACvC;AACA,QAAM;AAAA,IACJ,SAAS,KAAK,kBAAkB,OAAO,SAAS,GAAG;AAAA,IACnD;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,gBAAgB,gBAAgB;AACtC,QAAM,aAAa,gBAAgB;AACnC,QAAM,eAAe,gBAAgB;AACrC,QAAM,aAAa,WAAW,KAAK;AACnC,QAAM,aAAa,WAAW,KAAK;AACnC,QAAM,UAAU,WAAW,KAAK;AAChC,QAAM,aAAa,WAAW,IAAI;AAClC,QAAM,WAAW,WAAW,IAAI;AAChC,QAAM,QAAQ,WAAW,IAAI;AAC7B,QAAM,OAAO,WAAW,eAAe,IAAI;AAC3C,QAAM,WAAW,SAAS,MAAM,iBAAiB,WAAW,KAAK;AACjE,MAAI;AACJ,MAAI;AACJ,QAAM,QAAQ,MAAM;AAClB,QAAI,eAAe;AACjB,oBAAc,OAAO,SAAS,WAAW,MAAM;AAC/C,mBAAa,IAAI,gBAAgB;AACjC,iBAAW,OAAO,UAAU,MAAM,QAAQ,QAAQ;AAClD,qBAAe;AAAA,QACb,GAAG;AAAA,QACH,QAAQ,WAAW;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AACA,QAAM,UAAU,CAAC,cAAc;AAC7B,eAAW,QAAQ;AACnB,eAAW,QAAQ,CAAC;AAAA,EACtB;AACA,MAAI;AACF,YAAQ,aAAa,OAAO,SAAS,EAAE,WAAW,MAAM,CAAC;AAC3D,MAAI,iBAAiB;AACrB,QAAM,UAAU,OAAO,gBAAgB,UAAU;AAC/C,QAAI,KAAK;AACT,UAAM;AACN,YAAQ,IAAI;AACZ,UAAM,QAAQ;AACd,eAAW,QAAQ;AACnB,YAAQ,QAAQ;AAChB,sBAAkB;AAClB,UAAM,wBAAwB;AAC9B,UAAM,sBAAsB;AAAA,MAC1B,QAAQ,OAAO;AAAA,MACf,SAAS,CAAC;AAAA,IACZ;AACA,UAAM,UAAU,QAAQ,OAAO,OAAO;AACtC,QAAI,SAAS;AACX,YAAM,UAAU,gBAAgB,oBAAoB,OAAO;AAC3D,YAAM,QAAQ,OAAO,eAAe,OAAO;AAC3C,UAAI,CAAC,OAAO,eAAe,YAAY,UAAU,OAAO,aAAa,MAAM,QAAQ,KAAK,MAAM,EAAE,mBAAmB;AACjH,eAAO,cAAc;AACvB,UAAI,OAAO;AACT,gBAAQ,cAAc,KAAK,MAAM,eAAe,OAAO,WAAW,MAAM,OAAO,MAAM,OAAO;AAC9F,0BAAoB,OAAO,OAAO,gBAAgB,SAAS,KAAK,UAAU,OAAO,IAAI;AAAA,IACvF;AACA,QAAI,aAAa;AACjB,UAAM,UAAU;AAAA,MACd,KAAK,QAAQ,GAAG;AAAA,MAChB,SAAS;AAAA,QACP,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,MACA,QAAQ,MAAM;AACZ,qBAAa;AAAA,MACf;AAAA,IACF;AACA,QAAI,QAAQ;AACV,aAAO,OAAO,SAAS,MAAM,QAAQ,YAAY,OAAO,CAAC;AAC3D,QAAI,cAAc,CAAC,OAAO;AACxB,cAAQ,KAAK;AACb,aAAO,QAAQ,QAAQ,IAAI;AAAA,IAC7B;AACA,QAAI,eAAe;AACnB,QAAI;AACF,YAAM,MAAM;AACd,WAAO;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,QACE,GAAG;AAAA,QACH,GAAG,QAAQ;AAAA,QACX,SAAS;AAAA,UACP,GAAG,gBAAgB,oBAAoB,OAAO;AAAA,UAC9C,GAAG,iBAAiB,KAAK,QAAQ,YAAY,OAAO,SAAS,GAAG,OAAO;AAAA,QACzE;AAAA,MACF;AAAA,IACF,EAAE,KAAK,OAAO,kBAAkB;AAC9B,eAAS,QAAQ;AACjB,iBAAW,QAAQ,cAAc;AACjC,qBAAe,MAAM,cAAc,MAAM,EAAE,OAAO,IAAI,EAAE;AACxD,UAAI,CAAC,cAAc,IAAI;AACrB,aAAK,QAAQ,eAAe;AAC5B,cAAM,IAAI,MAAM,cAAc,UAAU;AAAA,MAC1C;AACA,UAAI,QAAQ,YAAY;AACtB,SAAC,EAAE,MAAM,aAAa,IAAI,MAAM,QAAQ,WAAW;AAAA,UACjD,MAAM;AAAA,UACN,UAAU;AAAA,UACV;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AACA,WAAK,QAAQ;AACb,oBAAc,QAAQ,aAAa;AACnC,aAAO;AAAA,IACT,CAAC,EAAE,MAAM,OAAO,eAAe;AAC7B,UAAI,YAAY,WAAW,WAAW,WAAW;AACjD,UAAI,QAAQ,cAAc;AACxB,SAAC,EAAE,OAAO,WAAW,MAAM,aAAa,IAAI,MAAM,QAAQ,aAAa;AAAA,UACrE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,UAAU,SAAS;AAAA,UACnB;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,QAAQ;AACd,UAAI,QAAQ;AACV,aAAK,QAAQ;AACf,iBAAW,QAAQ,UAAU;AAC7B,UAAI;AACF,cAAM;AACR,aAAO;AAAA,IACT,CAAC,EAAE,QAAQ,MAAM;AACf,UAAI,0BAA0B;AAC5B,gBAAQ,KAAK;AACf,UAAI;AACF,cAAM,KAAK;AACb,mBAAa,QAAQ,IAAI;AAAA,IAC3B,CAAC;AAAA,EACH;AACA,QAAM,UAAUO,OAAM,QAAQ,OAAO;AACrC;AAAA,IACE;AAAA,MACE;AAAA,MACAA,OAAM,GAAG;AAAA,IACX;AAAA,IACA,CAAC,CAAC,QAAQ,MAAM,YAAY,QAAQ;AAAA,IACpC,EAAE,MAAM,KAAK;AAAA,EACf;AACA,QAAM,QAAQ;AAAA,IACZ,YAAY,SAAS,UAAU;AAAA,IAC/B,YAAY,SAAS,UAAU;AAAA,IAC/B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,iBAAiB,cAAc;AAAA,IAC/B,cAAc,WAAW;AAAA,IACzB,gBAAgB,aAAa;AAAA;AAAA,IAE7B,KAAK,UAAU,KAAK;AAAA,IACpB,KAAK,UAAU,KAAK;AAAA,IACpB,MAAM,UAAU,MAAM;AAAA,IACtB,QAAQ,UAAU,QAAQ;AAAA,IAC1B,OAAO,UAAU,OAAO;AAAA,IACxB,MAAM,UAAU,MAAM;AAAA,IACtB,SAAS,UAAU,SAAS;AAAA;AAAA,IAE5B,MAAM,QAAQ,MAAM;AAAA,IACpB,MAAM,QAAQ,MAAM;AAAA,IACpB,MAAM,QAAQ,MAAM;AAAA,IACpB,aAAa,QAAQ,aAAa;AAAA,IAClC,UAAU,QAAQ,UAAU;AAAA,EAC9B;AACA,WAAS,UAAU,QAAQ;AACzB,WAAO,CAAC,SAAS,gBAAgB;AAC/B,UAAI,CAAC,WAAW,OAAO;AACrB,eAAO,SAAS;AAChB,eAAO,UAAU;AACjB,eAAO,cAAc;AACrB,YAAI,MAAM,OAAO,OAAO,GAAG;AACzB;AAAA,YACE;AAAA,cACE;AAAA,cACAA,OAAM,OAAO,OAAO;AAAA,YACtB;AAAA,YACA,CAAC,CAAC,QAAQ,MAAM,YAAY,QAAQ;AAAA,YACpC,EAAE,MAAM,KAAK;AAAA,UACf;AAAA,QACF;AACA,eAAO;AAAA,UACL,GAAG;AAAA,UACH,KAAK,aAAa,YAAY;AAC5B,mBAAO,kBAAkB,EAAE,KAAK,aAAa,UAAU;AAAA,UACzD;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACA,WAAS,oBAAoB;AAC3B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,YAAM,UAAU,EAAE,KAAK,IAAI,EAAE,KAAK,MAAM,QAAQ,KAAK,CAAC,EAAE,MAAM,MAAM;AAAA,IACtE,CAAC;AAAA,EACH;AACA,WAAS,QAAQ,MAAM;AACrB,WAAO,MAAM;AACX,UAAI,CAAC,WAAW,OAAO;AACrB,eAAO,OAAO;AACd,eAAO;AAAA,UACL,GAAG;AAAA,UACH,KAAK,aAAa,YAAY;AAC5B,mBAAO,kBAAkB,EAAE,KAAK,aAAa,UAAU;AAAA,UACzD;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,QAAQ;AACV,YAAQ,QAAQ,EAAE,KAAK,MAAM,QAAQ,CAAC;AACxC,SAAO;AAAA,IACL,GAAG;AAAA,IACH,KAAK,aAAa,YAAY;AAC5B,aAAO,kBAAkB,EAAE,KAAK,aAAa,UAAU;AAAA,IACzD;AAAA,EACF;AACF;AACA,SAAS,UAAU,OAAO,KAAK;AAC7B,MAAI,CAAC,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,WAAW,GAAG,GAAG;AAChD,WAAO,GAAG,KAAK,IAAI,GAAG;AAAA,EACxB;AACA,MAAI,MAAM,SAAS,GAAG,KAAK,IAAI,WAAW,GAAG,GAAG;AAC9C,WAAO,GAAG,MAAM,MAAM,GAAG,EAAE,CAAC,GAAG,GAAG;AAAA,EACpC;AACA,SAAO,GAAG,KAAK,GAAG,GAAG;AACvB;AAEA,IAAM,kBAAkB;AAAA,EACtB,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,WAAW;AACb;AACA,SAAS,oBAAoB,OAAO;AAClC,MAAI,CAAC;AACH,WAAO;AACT,MAAI,iBAAiB;AACnB,WAAO;AACT,QAAM,KAAK,IAAI,aAAa;AAC5B,aAAW,QAAQ,OAAO;AACxB,OAAG,MAAM,IAAI,IAAI;AAAA,EACnB;AACA,SAAO,GAAG;AACZ;AACA,SAAS,cAAc,UAAU,CAAC,GAAG;AACnC,QAAM;AAAA,IACJ,UAAAP,YAAW;AAAA,EACb,IAAI;AACJ,QAAM,QAAQ,IAAI,oBAAoB,QAAQ,YAAY,CAAC;AAC3D,QAAM,EAAE,IAAI,UAAU,SAAS,cAAc,IAAI,gBAAgB;AACjE,QAAM,EAAE,IAAI,UAAU,SAAS,cAAc,IAAI,gBAAgB;AACjE,MAAI;AACJ,MAAIA,WAAU;AACZ,YAAQA,UAAS,cAAc,OAAO;AACtC,UAAM,OAAO;AACb,UAAM,WAAW,CAAC,UAAU;AAC1B,YAAM,SAAS,MAAM;AACrB,YAAM,QAAQ,OAAO;AACrB,oBAAc,MAAM,KAAK;AAAA,IAC3B;AACA,UAAM,WAAW,MAAM;AACrB,oBAAc;AAAA,IAChB;AAAA,EACF;AACA,QAAM,QAAQ,MAAM;AAClB,UAAM,QAAQ;AACd,QAAI,SAAS,MAAM,OAAO;AACxB,YAAM,QAAQ;AACd,oBAAc,IAAI;AAAA,IACpB;AAAA,EACF;AACA,QAAM,OAAO,CAAC,iBAAiB;AAC7B,QAAI,CAAC;AACH;AACF,UAAM,WAAW;AAAA,MACf,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AACA,UAAM,WAAW,SAAS;AAC1B,UAAM,SAAS,SAAS;AACxB,UAAM,kBAAkB,SAAS;AACjC,QAAI,OAAO,UAAU,SAAS;AAC5B,YAAM,UAAU,SAAS;AAC3B,QAAI,SAAS;AACX,YAAM;AACR,UAAM,MAAM;AAAA,EACd;AACA,SAAO;AAAA,IACL,OAAO,SAAS,KAAK;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,oBAAoB,UAAU,CAAC,GAAG;AACzC,QAAM;AAAA,IACJ,QAAQ,UAAU;AAAA,IAClB,WAAW;AAAA,EACb,IAAI;AACJ,QAAMD,UAAS;AACf,QAAM,cAAc,aAAa,MAAMA,WAAU,wBAAwBA,WAAU,wBAAwBA,OAAM;AACjH,QAAM,aAAa,WAAW;AAC9B,QAAM,OAAO,WAAW;AACxB,QAAM,OAAO,WAAW;AACxB,QAAM,WAAW,SAAS,MAAM;AAC9B,QAAI,IAAI;AACR,YAAQ,MAAM,KAAK,KAAK,UAAU,OAAO,SAAS,GAAG,SAAS,OAAO,KAAK;AAAA,EAC5E,CAAC;AACD,QAAM,WAAW,SAAS,MAAM;AAC9B,QAAI,IAAI;AACR,YAAQ,MAAM,KAAK,KAAK,UAAU,OAAO,SAAS,GAAG,SAAS,OAAO,KAAK;AAAA,EAC5E,CAAC;AACD,QAAM,WAAW,SAAS,MAAM;AAC9B,QAAI,IAAI;AACR,YAAQ,MAAM,KAAK,KAAK,UAAU,OAAO,SAAS,GAAG,SAAS,OAAO,KAAK;AAAA,EAC5E,CAAC;AACD,QAAM,mBAAmB,SAAS,MAAM;AACtC,QAAI,IAAI;AACR,YAAQ,MAAM,KAAK,KAAK,UAAU,OAAO,SAAS,GAAG,iBAAiB,OAAO,KAAK;AAAA,EACpF,CAAC;AACD,iBAAe,KAAK,WAAW,CAAC,GAAG;AACjC,QAAI,CAAC,YAAY;AACf;AACF,UAAM,CAAC,MAAM,IAAI,MAAMA,QAAO,mBAAmB,EAAE,GAAG,QAAQ,OAAO,GAAG,GAAG,SAAS,CAAC;AACrF,eAAW,QAAQ;AACnB,UAAM,WAAW;AAAA,EACnB;AACA,iBAAe,OAAO,WAAW,CAAC,GAAG;AACnC,QAAI,CAAC,YAAY;AACf;AACF,eAAW,QAAQ,MAAMA,QAAO,mBAAmB,EAAE,GAAG,SAAS,GAAG,SAAS,CAAC;AAC9E,SAAK,QAAQ;AACb,UAAM,WAAW;AAAA,EACnB;AACA,iBAAe,KAAK,WAAW,CAAC,GAAG;AACjC,QAAI,CAAC,YAAY;AACf;AACF,QAAI,CAAC,WAAW;AACd,aAAO,OAAO,QAAQ;AACxB,QAAI,KAAK,OAAO;AACd,YAAM,iBAAiB,MAAM,WAAW,MAAM,eAAe;AAC7D,YAAM,eAAe,MAAM,KAAK,KAAK;AACrC,YAAM,eAAe,MAAM;AAAA,IAC7B;AACA,UAAM,WAAW;AAAA,EACnB;AACA,iBAAe,OAAO,WAAW,CAAC,GAAG;AACnC,QAAI,CAAC,YAAY;AACf;AACF,eAAW,QAAQ,MAAMA,QAAO,mBAAmB,EAAE,GAAG,SAAS,GAAG,SAAS,CAAC;AAC9E,QAAI,KAAK,OAAO;AACd,YAAM,iBAAiB,MAAM,WAAW,MAAM,eAAe;AAC7D,YAAM,eAAe,MAAM,KAAK,KAAK;AACrC,YAAM,eAAe,MAAM;AAAA,IAC7B;AACA,UAAM,WAAW;AAAA,EACnB;AACA,iBAAe,aAAa;AAC1B,QAAI;AACJ,SAAK,QAAQ,QAAQ,KAAK,WAAW,UAAU,OAAO,SAAS,GAAG,QAAQ;AAAA,EAC5E;AACA,iBAAe,aAAa;AAC1B,QAAI,IAAI;AACR,UAAM,WAAW;AACjB,UAAM,OAAO,QAAQ,QAAQ;AAC7B,QAAI,SAAS;AACX,WAAK,QAAQ,QAAQ,KAAK,KAAK,UAAU,OAAO,SAAS,GAAG,KAAK;AAAA,aAC1D,SAAS;AAChB,WAAK,QAAQ,QAAQ,KAAK,KAAK,UAAU,OAAO,SAAS,GAAG,YAAY;AAAA,aACjE,SAAS;AAChB,WAAK,QAAQ,KAAK;AAAA,EACtB;AACA,QAAM,MAAM,QAAQ,QAAQ,GAAG,UAAU;AACzC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,SAAS,QAAQ,UAAU,CAAC,GAAG;AACtC,QAAM,EAAE,eAAe,OAAO,eAAe,OAAO,gBAAgB,MAAM,IAAI;AAC9E,QAAM,eAAe,WAAW,KAAK;AACrC,QAAM,gBAAgB,SAAS,MAAM,aAAa,MAAM,CAAC;AACzD,QAAM,kBAAkB,EAAE,SAAS,KAAK;AACxC,mBAAiB,eAAe,SAAS,CAAC,UAAU;AAClD,QAAI,IAAI;AACR,QAAI,CAAC,kBAAkB,MAAM,KAAK,MAAM,QAAQ,YAAY,OAAO,SAAS,GAAG,KAAK,IAAI,gBAAgB;AACtG,mBAAa,QAAQ;AAAA,EACzB,GAAG,eAAe;AAClB,mBAAiB,eAAe,QAAQ,MAAM,aAAa,QAAQ,OAAO,eAAe;AACzF,QAAM,UAAU,SAAS;AAAA,IACvB,KAAK,MAAM,aAAa;AAAA,IACxB,IAAI,OAAO;AACT,UAAI,IAAI;AACR,UAAI,CAAC,SAAS,aAAa;AACzB,SAAC,KAAK,cAAc,UAAU,OAAO,SAAS,GAAG,KAAK;AAAA,eAC/C,SAAS,CAAC,aAAa;AAC9B,SAAC,KAAK,cAAc,UAAU,OAAO,SAAS,GAAG,MAAM,EAAE,cAAc,CAAC;AAAA,IAC5E;AAAA,EACF,CAAC;AACD;AAAA,IACE;AAAA,IACA,MAAM;AACJ,cAAQ,QAAQ;AAAA,IAClB;AAAA,IACA,EAAE,WAAW,MAAM,OAAO,OAAO;AAAA,EACnC;AACA,SAAO,EAAE,QAAQ;AACnB;AAEA,IAAM,iBAAiB;AACvB,IAAM,kBAAkB;AACxB,IAAM,4BAA4B;AAClC,SAAS,eAAe,QAAQ,UAAU,CAAC,GAAG;AAC5C,QAAM,EAAE,QAAAA,UAAS,cAAc,IAAI;AACnC,QAAM,gBAAgB,SAAS,MAAM,aAAa,MAAM,CAAC;AACzD,QAAM,WAAW,WAAW,KAAK;AACjC,QAAM,UAAU,SAAS,MAAM,SAAS,KAAK;AAC7C,QAAM,gBAAgB,iBAAiB,OAAO;AAC9C,MAAI,CAACA,WAAU,CAAC,cAAc,OAAO;AACnC,WAAO,EAAE,QAAQ;AAAA,EACnB;AACA,QAAM,kBAAkB,EAAE,SAAS,KAAK;AACxC,mBAAiB,eAAe,gBAAgB,MAAM,SAAS,QAAQ,MAAM,eAAe;AAC5F,mBAAiB,eAAe,iBAAiB,MAAM;AACrD,QAAI,IAAI,IAAI;AACZ,WAAO,SAAS,SAAS,MAAM,MAAM,KAAK,cAAc,UAAU,OAAO,SAAS,GAAG,YAAY,OAAO,SAAS,GAAG,KAAK,IAAI,yBAAyB,MAAM,OAAO,KAAK;AAAA,EAC1K,GAAG,eAAe;AAClB,SAAO,EAAE,QAAQ;AACnB;AAEA,SAAS,OAAO,SAAS;AACvB,MAAI;AACJ,QAAM,MAAM,WAAW,CAAC;AACxB,MAAI,OAAO,gBAAgB;AACzB,WAAO;AACT,QAAM,SAAS,KAAK,WAAW,OAAO,SAAS,QAAQ,UAAU,OAAO,KAAK;AAC7E,MAAI,OAAO,YAAY,IAAI;AAC3B,MAAI,QAAQ;AACZ,WAAS,MAAM;AACb,aAAS;AACT,QAAI,SAAS,OAAO;AAClB,YAAMa,OAAM,YAAY,IAAI;AAC5B,YAAM,OAAOA,OAAM;AACnB,UAAI,QAAQ,KAAK,MAAM,OAAO,OAAO,MAAM;AAC3C,aAAOA;AACP,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,IAAM,gBAAgB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,SAAS,cAAc,QAAQ,UAAU,CAAC,GAAG;AAC3C,QAAM;AAAA,IACJ,UAAAZ,YAAW;AAAA,IACX,WAAW;AAAA,EACb,IAAI;AACJ,QAAM,YAAY,SAAS,MAAM;AAC/B,QAAI;AACJ,YAAQ,KAAK,aAAa,MAAM,MAAM,OAAO,KAAKA,aAAY,OAAO,SAASA,UAAS;AAAA,EACzF,CAAC;AACD,QAAM,eAAe,WAAW,KAAK;AACrC,QAAM,gBAAgB,SAAS,MAAM;AACnC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,EAAE,KAAK,CAAC,MAAMA,aAAY,KAAKA,aAAY,UAAU,SAAS,KAAK,UAAU,KAAK;AAAA,EACpF,CAAC;AACD,QAAM,aAAa,SAAS,MAAM;AAChC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,EAAE,KAAK,CAAC,MAAMA,aAAY,KAAKA,aAAY,UAAU,SAAS,KAAK,UAAU,KAAK;AAAA,EACpF,CAAC;AACD,QAAM,oBAAoB,SAAS,MAAM;AACvC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,EAAE,KAAK,CAAC,MAAMA,aAAY,KAAKA,aAAY,UAAU,SAAS,KAAK,UAAU,KAAK;AAAA,EACpF,CAAC;AACD,QAAM,0BAA0B;AAAA,IAC9B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,EAAE,KAAK,CAAC,MAAMA,aAAY,KAAKA,SAAQ;AACvC,QAAM,cAAc,aAAa,MAAM,UAAU,SAASA,aAAY,cAAc,UAAU,UAAU,WAAW,UAAU,UAAU,kBAAkB,UAAU,MAAM;AACzK,QAAM,6BAA6B,MAAM;AACvC,QAAI;AACF,cAAQA,aAAY,OAAO,SAASA,UAAS,uBAAuB,OAAO,UAAU;AACvF,WAAO;AAAA,EACT;AACA,QAAM,sBAAsB,MAAM;AAChC,QAAI,kBAAkB,OAAO;AAC3B,UAAIA,aAAYA,UAAS,kBAAkB,KAAK,KAAK,MAAM;AACzD,eAAOA,UAAS,kBAAkB,KAAK;AAAA,MACzC,OAAO;AACL,cAAM,UAAU,UAAU;AAC1B,aAAK,WAAW,OAAO,SAAS,QAAQ,kBAAkB,KAAK,MAAM,MAAM;AACzE,iBAAO,QAAQ,QAAQ,kBAAkB,KAAK,CAAC;AAAA,QACjD;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,iBAAe,OAAO;AACpB,QAAI,CAAC,YAAY,SAAS,CAAC,aAAa;AACtC;AACF,QAAI,WAAW,OAAO;AACpB,WAAKA,aAAY,OAAO,SAASA,UAAS,WAAW,KAAK,MAAM,MAAM;AACpE,cAAMA,UAAS,WAAW,KAAK,EAAE;AAAA,MACnC,OAAO;AACL,cAAM,UAAU,UAAU;AAC1B,aAAK,WAAW,OAAO,SAAS,QAAQ,WAAW,KAAK,MAAM;AAC5D,gBAAM,QAAQ,WAAW,KAAK,EAAE;AAAA,MACpC;AAAA,IACF;AACA,iBAAa,QAAQ;AAAA,EACvB;AACA,iBAAe,QAAQ;AACrB,QAAI,CAAC,YAAY,SAAS,aAAa;AACrC;AACF,QAAI,oBAAoB;AACtB,YAAM,KAAK;AACb,UAAM,UAAU,UAAU;AAC1B,QAAI,cAAc,UAAU,WAAW,OAAO,SAAS,QAAQ,cAAc,KAAK,MAAM,MAAM;AAC5F,YAAM,QAAQ,cAAc,KAAK,EAAE;AACnC,mBAAa,QAAQ;AAAA,IACvB;AAAA,EACF;AACA,iBAAe,SAAS;AACtB,WAAO,aAAa,QAAQ,KAAK,IAAI,MAAM;AAAA,EAC7C;AACA,QAAM,kBAAkB,MAAM;AAC5B,UAAM,2BAA2B,oBAAoB;AACrD,QAAI,CAAC,4BAA4B,4BAA4B,2BAA2B;AACtF,mBAAa,QAAQ;AAAA,EACzB;AACA,QAAM,kBAAkB,EAAE,SAAS,OAAO,SAAS,KAAK;AACxD,mBAAiBA,WAAU,eAAe,iBAAiB,eAAe;AAC1E,mBAAiB,MAAM,aAAa,SAAS,GAAG,eAAe,iBAAiB,eAAe;AAC/F,MAAI;AACF,sBAAkB,IAAI;AACxB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,8BAA8B,SAAS;AAC9C,SAAO,SAAS,MAAM;AACpB,QAAI,QAAQ,OAAO;AACjB,aAAO;AAAA,QACL,SAAS;AAAA,UACP,GAAG,QAAQ,MAAM,QAAQ,CAAC;AAAA,UAC1B,GAAG,QAAQ,MAAM,QAAQ,CAAC;AAAA,UAC1B,GAAG,QAAQ,MAAM,QAAQ,CAAC;AAAA,UAC1B,GAAG,QAAQ,MAAM,QAAQ,CAAC;AAAA,QAC5B;AAAA,QACA,QAAQ;AAAA,UACN,MAAM,QAAQ,MAAM,QAAQ,CAAC;AAAA,UAC7B,OAAO,QAAQ,MAAM,QAAQ,CAAC;AAAA,QAChC;AAAA,QACA,UAAU;AAAA,UACR,MAAM,QAAQ,MAAM,QAAQ,CAAC;AAAA,UAC7B,OAAO,QAAQ,MAAM,QAAQ,CAAC;AAAA,QAChC;AAAA,QACA,OAAO;AAAA,UACL,MAAM;AAAA,YACJ,YAAY,QAAQ,MAAM,KAAK,CAAC;AAAA,YAChC,UAAU,QAAQ,MAAM,KAAK,CAAC;AAAA,YAC9B,QAAQ,QAAQ,MAAM,QAAQ,EAAE;AAAA,UAClC;AAAA,UACA,OAAO;AAAA,YACL,YAAY,QAAQ,MAAM,KAAK,CAAC;AAAA,YAChC,UAAU,QAAQ,MAAM,KAAK,CAAC;AAAA,YAC9B,QAAQ,QAAQ,MAAM,QAAQ,EAAE;AAAA,UAClC;AAAA,QACF;AAAA,QACA,MAAM;AAAA,UACJ,IAAI,QAAQ,MAAM,QAAQ,EAAE;AAAA,UAC5B,MAAM,QAAQ,MAAM,QAAQ,EAAE;AAAA,UAC9B,MAAM,QAAQ,MAAM,QAAQ,EAAE;AAAA,UAC9B,OAAO,QAAQ,MAAM,QAAQ,EAAE;AAAA,QACjC;AAAA,QACA,MAAM,QAAQ,MAAM,QAAQ,CAAC;AAAA,QAC7B,OAAO,QAAQ,MAAM,QAAQ,CAAC;AAAA,MAChC;AAAA,IACF;AACA,WAAO;AAAA,EACT,CAAC;AACH;AACA,SAAS,WAAW,UAAU,CAAC,GAAG;AAChC,QAAM;AAAA,IACJ,WAAAE,aAAY;AAAA,EACd,IAAI;AACJ,QAAM,cAAc,aAAa,MAAMA,cAAa,iBAAiBA,UAAS;AAC9E,QAAM,WAAW,IAAI,CAAC,CAAC;AACvB,QAAM,kBAAkB,gBAAgB;AACxC,QAAM,qBAAqB,gBAAgB;AAC3C,QAAM,mBAAmB,CAAC,YAAY;AACpC,UAAM,kBAAkB,CAAC;AACzB,UAAM,oBAAoB,uBAAuB,UAAU,QAAQ,oBAAoB;AACvF,QAAI;AACF,sBAAgB,KAAK,iBAAiB;AACxC,QAAI,QAAQ;AACV,sBAAgB,KAAK,GAAG,QAAQ,eAAe;AACjD,WAAO;AAAA,MACL,IAAI,QAAQ;AAAA,MACZ,OAAO,QAAQ;AAAA,MACf,WAAW,QAAQ;AAAA,MACnB,SAAS,QAAQ;AAAA,MACjB,WAAW,QAAQ;AAAA,MACnB,mBAAmB,QAAQ;AAAA,MAC3B;AAAA,MACA,MAAM,QAAQ,KAAK,IAAI,CAAC,SAAS,IAAI;AAAA,MACrC,SAAS,QAAQ,QAAQ,IAAI,CAAC,YAAY,EAAE,SAAS,OAAO,SAAS,SAAS,OAAO,SAAS,OAAO,OAAO,MAAM,EAAE;AAAA,IACtH;AAAA,EACF;AACA,QAAM,qBAAqB,MAAM;AAC/B,UAAM,aAAaA,cAAa,OAAO,SAASA,WAAU,YAAY,MAAM,CAAC;AAC7E,eAAW,WAAW,WAAW;AAC/B,UAAI,WAAW,SAAS,MAAM,QAAQ,KAAK;AACzC,iBAAS,MAAM,QAAQ,KAAK,IAAI,iBAAiB,OAAO;AAAA,IAC5D;AAAA,EACF;AACA,QAAM,EAAE,UAAU,OAAO,OAAO,IAAI,SAAS,kBAAkB;AAC/D,QAAM,qBAAqB,CAAC,YAAY;AACtC,QAAI,CAAC,SAAS,MAAM,KAAK,CAAC,EAAE,MAAM,MAAM,UAAU,QAAQ,KAAK,GAAG;AAChE,eAAS,MAAM,KAAK,iBAAiB,OAAO,CAAC;AAC7C,sBAAgB,QAAQ,QAAQ,KAAK;AAAA,IACvC;AACA,WAAO;AAAA,EACT;AACA,QAAM,wBAAwB,CAAC,YAAY;AACzC,aAAS,QAAQ,SAAS,MAAM,OAAO,CAAC,MAAM,EAAE,UAAU,QAAQ,KAAK;AACvE,uBAAmB,QAAQ,QAAQ,KAAK;AAAA,EAC1C;AACA,QAAM,kBAAkB,EAAE,SAAS,KAAK;AACxC,mBAAiB,oBAAoB,CAAC,MAAM,mBAAmB,EAAE,OAAO,GAAG,eAAe;AAC1F,mBAAiB,uBAAuB,CAAC,MAAM,sBAAsB,EAAE,OAAO,GAAG,eAAe;AAChG,eAAa,MAAM;AACjB,UAAM,aAAaA,cAAa,OAAO,SAASA,WAAU,YAAY,MAAM,CAAC;AAC7E,eAAW,WAAW,WAAW;AAC/B,UAAI,WAAW,SAAS,MAAM,QAAQ,KAAK;AACzC,2BAAmB,OAAO;AAAA,IAC9B;AAAA,EACF,CAAC;AACD,QAAM;AACN,SAAO;AAAA,IACL;AAAA,IACA,aAAa,gBAAgB;AAAA,IAC7B,gBAAgB,mBAAmB;AAAA,IACnC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,eAAe,UAAU,CAAC,GAAG;AACpC,QAAM;AAAA,IACJ,qBAAqB;AAAA,IACrB,aAAa;AAAA,IACb,UAAU;AAAA,IACV,WAAAA,aAAY;AAAA,IACZ,YAAY;AAAA,EACd,IAAI;AACJ,QAAM,cAAc,aAAa,MAAMA,cAAa,iBAAiBA,UAAS;AAC9E,QAAM,YAAY,WAAW,IAAI;AACjC,QAAM,QAAQ,WAAW,IAAI;AAC7B,QAAM,SAAS,IAAI;AAAA,IACjB,UAAU;AAAA,IACV,UAAU,OAAO;AAAA,IACjB,WAAW,OAAO;AAAA,IAClB,UAAU;AAAA,IACV,kBAAkB;AAAA,IAClB,SAAS;AAAA,IACT,OAAO;AAAA,EACT,CAAC;AACD,WAAS,eAAe,UAAU;AAChC,cAAU,QAAQ,SAAS;AAC3B,WAAO,QAAQ,SAAS;AACxB,UAAM,QAAQ;AAAA,EAChB;AACA,MAAI;AACJ,WAAS,SAAS;AAChB,QAAI,YAAY,OAAO;AACrB,gBAAUA,WAAU,YAAY;AAAA,QAC9B;AAAA,QACA,CAAC,QAAQ,MAAM,QAAQ;AAAA,QACvB;AAAA,UACE;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI;AACF,WAAO;AACT,WAAS,QAAQ;AACf,QAAI,WAAWA;AACb,MAAAA,WAAU,YAAY,WAAW,OAAO;AAAA,EAC5C;AACA,oBAAkB,MAAM;AACtB,UAAM;AAAA,EACR,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,kBAAkB,CAAC,aAAa,aAAa,UAAU,WAAW,cAAc,OAAO;AAC7F,IAAM,YAAY;AAClB,SAAS,QAAQ,UAAU,WAAW,UAAU,CAAC,GAAG;AAClD,QAAM;AAAA,IACJ,eAAe;AAAA,IACf,4BAA4B;AAAA,IAC5B,QAAAC,UAAS;AAAA,IACT,QAAAJ,UAAS;AAAA,IACT,cAAc,eAAe,EAAE;AAAA,EACjC,IAAI;AACJ,QAAM,OAAO,WAAW,YAAY;AACpC,QAAM,aAAa,WAAW,UAAU,CAAC;AACzC,MAAI;AACJ,QAAM,QAAQ,MAAM;AAClB,SAAK,QAAQ;AACb,iBAAa,KAAK;AAClB,YAAQ,WAAW,MAAM,KAAK,QAAQ,MAAM,OAAO;AAAA,EACrD;AACA,QAAM,UAAU;AAAA,IACd;AAAA,IACA,MAAM;AACJ,iBAAW,QAAQ,UAAU;AAC7B,YAAM;AAAA,IACR;AAAA,EACF;AACA,MAAIA,SAAQ;AACV,UAAMC,YAAWD,QAAO;AACxB,UAAM,kBAAkB,EAAE,SAAS,KAAK;AACxC,eAAW,SAASI;AAClB,uBAAiBJ,SAAQ,OAAO,SAAS,eAAe;AAC1D,QAAI,2BAA2B;AAC7B,uBAAiBC,WAAU,oBAAoB,MAAM;AACnD,YAAI,CAACA,UAAS;AACZ,kBAAQ;AAAA,MACZ,GAAG,eAAe;AAAA,IACpB;AACA,UAAM;AAAA,EACR;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,eAAe,UAAU,SAAS;AAChC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAM,MAAM,IAAI,MAAM;AACtB,UAAM,EAAE,KAAK,QAAQ,OAAO,OAAO,OAAO,SAAS,aAAa,gBAAgB,OAAO,QAAQ,UAAU,eAAe,OAAO,OAAO,IAAI;AAC1I,QAAI,MAAM;AACV,QAAI,UAAU;AACZ,UAAI,SAAS;AACf,QAAI,SAAS;AACX,UAAI,QAAQ;AACd,QAAI,SAAS;AACX,UAAI,YAAY;AAClB,QAAI,WAAW;AACb,UAAI,UAAU;AAChB,QAAI,eAAe;AACjB,UAAI,cAAc;AACpB,QAAI,kBAAkB;AACpB,UAAI,iBAAiB;AACvB,QAAI,SAAS;AACX,UAAI,QAAQ;AACd,QAAI,UAAU;AACZ,UAAI,SAAS;AACf,QAAI,YAAY;AACd,UAAI,WAAW;AACjB,QAAI,iBAAiB;AACnB,UAAI,gBAAgB;AACtB,QAAI,SAAS;AACX,UAAI,QAAQ;AACd,QAAI,UAAU;AACZ,UAAI,SAAS;AACf,QAAI,SAAS,MAAM,QAAQ,GAAG;AAC9B,QAAI,UAAU;AAAA,EAChB,CAAC;AACH;AACA,SAAS,SAAS,SAAS,oBAAoB,CAAC,GAAG;AACjD,QAAM,QAAQ;AAAA,IACZ,MAAM,UAAU,QAAQ,OAAO,CAAC;AAAA,IAChC;AAAA,IACA;AAAA,MACE,gBAAgB;AAAA,MAChB,GAAG;AAAA,IACL;AAAA,EACF;AACA;AAAA,IACE,MAAM,QAAQ,OAAO;AAAA,IACrB,MAAM,MAAM,QAAQ,kBAAkB,KAAK;AAAA,IAC3C,EAAE,MAAM,KAAK;AAAA,EACf;AACA,SAAO;AACT;AAEA,SAAS,eAAe,IAAI;AAC1B,MAAI,OAAO,WAAW,eAAe,cAAc;AACjD,WAAO,GAAG,SAAS;AACrB,MAAI,OAAO,aAAa,eAAe,cAAc;AACnD,WAAO,GAAG;AACZ,SAAO;AACT;AAEA,IAAM,iCAAiC;AACvC,SAAS,UAAU,SAAS,UAAU,CAAC,GAAG;AACxC,QAAM;AAAA,IACJ,WAAW;AAAA,IACX,OAAO;AAAA,IACP,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,MACL,QAAQ;AAAA,IACV;AAAA,IACA,uBAAuB;AAAA,MACrB,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,IACX,QAAAD,UAAS;AAAA,IACT,UAAU,CAAC,MAAM;AACf,cAAQ,MAAM,CAAC;AAAA,IACjB;AAAA,EACF,IAAI;AACJ,QAAM,YAAY,WAAW,CAAC;AAC9B,QAAM,YAAY,WAAW,CAAC;AAC9B,QAAM,IAAI,SAAS;AAAA,IACjB,MAAM;AACJ,aAAO,UAAU;AAAA,IACnB;AAAA,IACA,IAAI,IAAI;AACN,eAAS,IAAI,MAAM;AAAA,IACrB;AAAA,EACF,CAAC;AACD,QAAM,IAAI,SAAS;AAAA,IACjB,MAAM;AACJ,aAAO,UAAU;AAAA,IACnB;AAAA,IACA,IAAI,IAAI;AACN,eAAS,QAAQ,EAAE;AAAA,IACrB;AAAA,EACF,CAAC;AACD,WAAS,SAAS,IAAI,IAAI;AACxB,QAAI,IAAI,IAAI,IAAI;AAChB,QAAI,CAACA;AACH;AACF,UAAM,WAAW,QAAQ,OAAO;AAChC,QAAI,CAAC;AACH;AACF,KAAC,KAAK,oBAAoB,WAAWA,QAAO,SAAS,OAAO,aAAa,OAAO,SAAS,GAAG,SAAS;AAAA,MACnG,MAAM,KAAK,QAAQ,EAAE,MAAM,OAAO,KAAK,EAAE;AAAA,MACzC,OAAO,KAAK,QAAQ,EAAE,MAAM,OAAO,KAAK,EAAE;AAAA,MAC1C,UAAU,QAAQ,QAAQ;AAAA,IAC5B,CAAC;AACD,UAAM,oBAAoB,KAAK,YAAY,OAAO,SAAS,SAAS,aAAa,OAAO,SAAS,GAAG,qBAAqB,YAAY,OAAO,SAAS,SAAS,oBAAoB;AAClL,QAAI,KAAK;AACP,gBAAU,QAAQ,gBAAgB;AACpC,QAAI,KAAK;AACP,gBAAU,QAAQ,gBAAgB;AAAA,EACtC;AACA,QAAM,cAAc,WAAW,KAAK;AACpC,QAAM,eAAe,SAAS;AAAA,IAC5B,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,IACL,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,aAAa,SAAS;AAAA,IAC1B,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,IACL,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,cAAc,CAAC,MAAM;AACzB,QAAI,CAAC,YAAY;AACf;AACF,gBAAY,QAAQ;AACpB,eAAW,OAAO;AAClB,eAAW,QAAQ;AACnB,eAAW,MAAM;AACjB,eAAW,SAAS;AACpB,WAAO,CAAC;AAAA,EACV;AACA,QAAM,uBAAuB,cAAc,aAAa,WAAW,IAAI;AACvE,QAAM,kBAAkB,CAAC,WAAW;AAClC,QAAI;AACJ,QAAI,CAACA;AACH;AACF,UAAM,OAAO,KAAK,UAAU,OAAO,SAAS,OAAO,aAAa,OAAO,SAAS,GAAG,qBAAqB,UAAU,OAAO,SAAS,OAAO,oBAAoB,aAAa,MAAM;AAChL,UAAM,EAAE,SAAS,eAAe,UAAU,IAAI,iBAAiB,EAAE;AACjE,UAAM,qBAAqB,cAAc,QAAQ,KAAK;AACtD,UAAM,aAAa,GAAG;AACtB,eAAW,OAAO,aAAa,UAAU;AACzC,eAAW,QAAQ,aAAa,UAAU;AAC1C,UAAM,OAAO,KAAK,IAAI,aAAa,kBAAkB,MAAM,OAAO,QAAQ;AAC1E,UAAM,QAAQ,KAAK,IAAI,aAAa,kBAAkB,IAAI,GAAG,eAAe,GAAG,eAAe,OAAO,SAAS,KAAK;AACnH,QAAI,YAAY,UAAU,kBAAkB,eAAe;AACzD,mBAAa,OAAO;AACpB,mBAAa,QAAQ;AAAA,IACvB,OAAO;AACL,mBAAa,OAAO;AACpB,mBAAa,QAAQ;AAAA,IACvB;AACA,cAAU,QAAQ;AAClB,QAAI,YAAY,GAAG;AACnB,QAAI,WAAWA,QAAO,YAAY,CAAC;AACjC,kBAAYA,QAAO,SAAS,KAAK;AACnC,eAAW,MAAM,YAAY,UAAU;AACvC,eAAW,SAAS,YAAY,UAAU;AAC1C,UAAM,MAAM,KAAK,IAAI,SAAS,MAAM,OAAO,OAAO;AAClD,UAAM,SAAS,KAAK,IAAI,SAAS,IAAI,GAAG,gBAAgB,GAAG,gBAAgB,OAAO,UAAU,KAAK;AACjG,QAAI,YAAY,UAAU,kBAAkB,kBAAkB;AAC5D,mBAAa,MAAM;AACnB,mBAAa,SAAS;AAAA,IACxB,OAAO;AACL,mBAAa,MAAM;AACnB,mBAAa,SAAS;AAAA,IACxB;AACA,cAAU,QAAQ;AAAA,EACpB;AACA,QAAM,kBAAkB,CAAC,MAAM;AAC7B,QAAI;AACJ,QAAI,CAACA;AACH;AACF,UAAM,eAAe,KAAK,EAAE,OAAO,oBAAoB,OAAO,KAAK,EAAE;AACrE,oBAAgB,WAAW;AAC3B,gBAAY,QAAQ;AACpB,yBAAqB,CAAC;AACtB,aAAS,CAAC;AAAA,EACZ;AACA;AAAA,IACE;AAAA,IACA;AAAA,IACA,WAAW,cAAc,iBAAiB,UAAU,MAAM,KAAK,IAAI;AAAA,IACnE;AAAA,EACF;AACA,eAAa,MAAM;AACjB,QAAI;AACF,YAAM,WAAW,QAAQ,OAAO;AAChC,UAAI,CAAC;AACH;AACF,sBAAgB,QAAQ;AAAA,IAC1B,SAAS,GAAG;AACV,cAAQ,CAAC;AAAA,IACX;AAAA,EACF,CAAC;AACD;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AACR,YAAM,WAAW,QAAQ,OAAO;AAChC,UAAIA,WAAU;AACZ,wBAAgB,QAAQ;AAAA,IAC5B;AAAA,EACF;AACF;AAEA,SAAS,kBAAkB,SAAS,YAAY,UAAU,CAAC,GAAG;AAC5D,MAAI;AACJ,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,cAAc,MAAM;AAAA,EACtB,IAAI;AACJ,QAAM,QAAQ,SAAS;AAAA,IACrB;AAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH,QAAQ;AAAA,QACN,CAAC,SAAS,IAAI,KAAK,QAAQ,aAAa,OAAO,KAAK;AAAA,QACpD,GAAG,QAAQ;AAAA,MACb;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,UAAU,IAAI;AACpB,QAAM,YAAY,SAAS,MAAM,CAAC,CAAC,QAAQ,KAAK;AAChD,QAAM,kBAAkB,SAAS,MAAM;AACrC,WAAO,eAAe,QAAQ,OAAO,CAAC;AAAA,EACxC,CAAC;AACD,QAAM,mBAAmB,qBAAqB,eAAe;AAC7D,WAAS,eAAe;AACtB,UAAM,QAAQ;AACd,QAAI,CAAC,gBAAgB,SAAS,CAAC,iBAAiB,SAAS,CAAC,YAAY,gBAAgB,KAAK;AACzF;AACF,UAAM,EAAE,cAAc,cAAc,aAAa,YAAY,IAAI,gBAAgB;AACjF,UAAM,aAAa,cAAc,YAAY,cAAc,QAAQ,gBAAgB,eAAe,eAAe;AACjH,QAAI,MAAM,aAAa,SAAS,KAAK,YAAY;AAC/C,UAAI,CAAC,QAAQ,OAAO;AAClB,gBAAQ,QAAQ,QAAQ,IAAI;AAAA,UAC1B,WAAW,KAAK;AAAA,UAChB,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,QAAQ,CAAC;AAAA,QACxD,CAAC,EAAE,QAAQ,MAAM;AACf,kBAAQ,QAAQ;AAChB,mBAAS,MAAM,aAAa,CAAC;AAAA,QAC/B,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACA,QAAM,OAAO;AAAA,IACX,MAAM,CAAC,MAAM,aAAa,SAAS,GAAG,iBAAiB,KAAK;AAAA,IAC5D;AAAA,IACA,EAAE,WAAW,KAAK;AAAA,EACpB;AACA,iBAAe,IAAI;AACnB,SAAO;AAAA,IACL;AAAA,IACA,QAAQ;AACN,eAAS,MAAM,aAAa,CAAC;AAAA,IAC/B;AAAA,EACF;AACF;AAEA,IAAM,gBAAgB,CAAC,aAAa,WAAW,WAAW,OAAO;AACjE,SAAS,eAAe,UAAU,UAAU,CAAC,GAAG;AAC9C,QAAM;AAAA,IACJ,QAAAI,UAAS;AAAA,IACT,UAAAH,YAAW;AAAA,IACX,UAAU;AAAA,EACZ,IAAI;AACJ,QAAM,QAAQ,WAAW,OAAO;AAChC,MAAIA,WAAU;AACZ,IAAAG,QAAO,QAAQ,CAAC,kBAAkB;AAChC,uBAAiBH,WAAU,eAAe,CAAC,QAAQ;AACjD,YAAI,OAAO,IAAI,qBAAqB;AAClC,gBAAM,QAAQ,IAAI,iBAAiB,QAAQ;AAAA,MAC/C,GAAG,EAAE,SAAS,KAAK,CAAC;AAAA,IACtB,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAEA,SAAS,gBAAgB,KAAK,cAAc,UAAU,CAAC,GAAG;AACxD,QAAM,EAAE,QAAAD,UAAS,cAAc,IAAI;AACnC,SAAO,WAAW,KAAK,cAAcA,WAAU,OAAO,SAASA,QAAO,cAAc,OAAO;AAC7F;AAEA,IAAM,2BAA2B;AAAA,EAC/B,MAAM;AAAA,EACN,SAAS;AAAA,EACT,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,IAAI;AAAA,EACJ,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AACT;AAEA,SAAS,aAAa,UAAU,CAAC,GAAG;AAClC,QAAM;AAAA,IACJ,UAAU,cAAc;AAAA,IACxB,SAAS;AAAA,IACT,WAAW;AAAA,IACX,UAAU;AAAA,IACV,eAAe;AAAA,EACjB,IAAI;AACJ,QAAM,UAAU,SAAyB,oBAAI,IAAI,CAAC;AAClD,QAAM,MAAM;AAAA,IACV,SAAS;AACP,aAAO,CAAC;AAAA,IACV;AAAA,IACA;AAAA,EACF;AACA,QAAM,OAAO,cAAc,SAAS,GAAG,IAAI;AAC3C,QAAM,WAA2B,oBAAI,IAAI;AACzC,QAAM,WAA2B,oBAAI,IAAI;AACzC,WAAS,QAAQ,KAAK,OAAO;AAC3B,QAAI,OAAO,MAAM;AACf,UAAI;AACF,aAAK,GAAG,IAAI;AAAA;AAEZ,aAAK,GAAG,EAAE,QAAQ;AAAA,IACtB;AAAA,EACF;AACA,WAAS,QAAQ;AACf,YAAQ,MAAM;AACd,eAAW,OAAO;AAChB,cAAQ,KAAK,KAAK;AAAA,EACtB;AACA,WAAS,WAAW,GAAG,OAAO;AAC5B,QAAI,IAAI;AACR,UAAM,OAAO,KAAK,EAAE,QAAQ,OAAO,SAAS,GAAG,YAAY;AAC3D,UAAM,QAAQ,KAAK,EAAE,SAAS,OAAO,SAAS,GAAG,YAAY;AAC7D,UAAM,SAAS,CAAC,MAAM,GAAG,EAAE,OAAO,OAAO;AACzC,QAAI,KAAK;AACP,UAAI;AACF,gBAAQ,IAAI,GAAG;AAAA;AAEf,gBAAQ,OAAO,GAAG;AAAA,IACtB;AACA,eAAW,QAAQ,QAAQ;AACzB,eAAS,IAAI,IAAI;AACjB,cAAQ,MAAM,KAAK;AAAA,IACrB;AACA,QAAI,QAAQ,UAAU,CAAC,OAAO;AAC5B,eAAS,QAAQ,CAAC,SAAS;AACzB,gBAAQ,OAAO,IAAI;AACnB,gBAAQ,MAAM,KAAK;AAAA,MACrB,CAAC;AACD,eAAS,MAAM;AAAA,IACjB,WAAW,OAAO,EAAE,qBAAqB,cAAc,EAAE,iBAAiB,MAAM,KAAK,OAAO;AAC1F,OAAC,GAAG,SAAS,GAAG,MAAM,EAAE,QAAQ,CAAC,SAAS,SAAS,IAAI,IAAI,CAAC;AAAA,IAC9D;AAAA,EACF;AACA,mBAAiB,QAAQ,WAAW,CAAC,MAAM;AACzC,eAAW,GAAG,IAAI;AAClB,WAAO,aAAa,CAAC;AAAA,EACvB,GAAG,EAAE,QAAQ,CAAC;AACd,mBAAiB,QAAQ,SAAS,CAAC,MAAM;AACvC,eAAW,GAAG,KAAK;AACnB,WAAO,aAAa,CAAC;AAAA,EACvB,GAAG,EAAE,QAAQ,CAAC;AACd,mBAAiB,QAAQ,OAAO,EAAE,QAAQ,CAAC;AAC3C,mBAAiB,SAAS,OAAO,EAAE,QAAQ,CAAC;AAC5C,QAAM,QAAQ,IAAI;AAAA,IAChB;AAAA,IACA;AAAA,MACE,IAAI,SAAS,MAAM,KAAK;AACtB,YAAI,OAAO,SAAS;AAClB,iBAAO,QAAQ,IAAI,SAAS,MAAM,GAAG;AACvC,eAAO,KAAK,YAAY;AACxB,YAAI,QAAQ;AACV,iBAAO,SAAS,IAAI;AACtB,YAAI,EAAE,QAAQ,OAAO;AACnB,cAAI,QAAQ,KAAK,IAAI,GAAG;AACtB,kBAAMc,QAAO,KAAK,MAAM,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC;AACrD,iBAAK,IAAI,IAAI,SAAS,MAAMA,MAAK,IAAI,CAAC,QAAQ,QAAQ,MAAM,GAAG,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC;AAAA,UACnF,OAAO;AACL,iBAAK,IAAI,IAAI,WAAW,KAAK;AAAA,UAC/B;AAAA,QACF;AACA,cAAM,IAAI,QAAQ,IAAI,SAAS,MAAM,GAAG;AACxC,eAAO,cAAc,QAAQ,CAAC,IAAI;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,WAAW,QAAQ,IAAI;AAC9B,MAAI,QAAQ,MAAM;AAChB,OAAG,QAAQ,MAAM,CAAC;AACtB;AACA,SAAS,iBAAiB,YAAY;AACpC,MAAI,SAAS,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE;AACvC,aAAS,CAAC,GAAG,QAAQ,CAAC,WAAW,MAAM,CAAC,GAAG,WAAW,IAAI,CAAC,CAAC,CAAC;AAC/D,SAAO;AACT;AACA,SAAS,cAAc,QAAQ;AAC7B,SAAO,MAAM,KAAK,MAAM,EAAE,IAAI,CAAC,EAAE,OAAO,MAAM,UAAU,MAAM,YAAY,MAAM,gCAAgC,GAAG,QAAQ,EAAE,IAAI,OAAO,MAAM,UAAU,MAAM,YAAY,MAAM,gCAAgC,EAAE;AACpN;AACA,IAAM,iBAAiB;AAAA,EACrB,KAAK;AAAA,EACL,QAAQ,CAAC;AACX;AACA,SAAS,iBAAiB,QAAQ,UAAU,CAAC,GAAG;AAC9C,WAASN,OAAM,MAAM;AACrB,YAAU;AAAA,IACR,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM;AAAA,IACJ,UAAAP,YAAW;AAAA,EACb,IAAI;AACJ,QAAM,kBAAkB,EAAE,SAAS,KAAK;AACxC,QAAM,cAAc,WAAW,CAAC;AAChC,QAAM,WAAW,WAAW,CAAC;AAC7B,QAAM,UAAU,WAAW,KAAK;AAChC,QAAM,SAAS,WAAW,CAAC;AAC3B,QAAM,UAAU,WAAW,KAAK;AAChC,QAAM,QAAQ,WAAW,KAAK;AAC9B,QAAM,UAAU,WAAW,KAAK;AAChC,QAAM,OAAO,WAAW,CAAC;AACzB,QAAM,UAAU,WAAW,KAAK;AAChC,QAAM,WAAW,IAAI,CAAC,CAAC;AACvB,QAAM,SAAS,IAAI,CAAC,CAAC;AACrB,QAAM,gBAAgB,WAAW,EAAE;AACnC,QAAM,qBAAqB,WAAW,KAAK;AAC3C,QAAM,QAAQ,WAAW,KAAK;AAC9B,QAAM,2BAA2BA,aAAY,6BAA6BA;AAC1E,QAAM,mBAAmB,gBAAgB;AACzC,QAAM,qBAAqB,gBAAgB;AAC3C,QAAM,eAAe,CAAC,UAAU;AAC9B,eAAW,QAAQ,CAAC,OAAO;AACzB,UAAI,OAAO;AACT,cAAM,KAAK,OAAO,UAAU,WAAW,QAAQ,MAAM;AACrD,WAAG,WAAW,EAAE,EAAE,OAAO;AAAA,MAC3B,OAAO;AACL,iBAAS,IAAI,GAAG,IAAI,GAAG,WAAW,QAAQ,EAAE;AAC1C,aAAG,WAAW,CAAC,EAAE,OAAO;AAAA,MAC5B;AACA,oBAAc,QAAQ;AAAA,IACxB,CAAC;AAAA,EACH;AACA,QAAM,cAAc,CAAC,OAAO,gBAAgB,SAAS;AACnD,eAAW,QAAQ,CAAC,OAAO;AACzB,YAAM,KAAK,OAAO,UAAU,WAAW,QAAQ,MAAM;AACrD,UAAI;AACF,qBAAa;AACf,SAAG,WAAW,EAAE,EAAE,OAAO;AACzB,oBAAc,QAAQ;AAAA,IACxB,CAAC;AAAA,EACH;AACA,QAAM,yBAAyB,MAAM;AACnC,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,iBAAW,QAAQ,OAAO,OAAO;AAC/B,YAAI,0BAA0B;AAC5B,cAAI,CAAC,mBAAmB,OAAO;AAC7B,eAAG,wBAAwB,EAAE,KAAK,OAAO,EAAE,MAAM,MAAM;AAAA,UACzD,OAAO;AACL,YAAAA,UAAS,qBAAqB,EAAE,KAAK,OAAO,EAAE,MAAM,MAAM;AAAA,UAC5D;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,cAAY,MAAM;AAChB,QAAI,CAACA;AACH;AACF,UAAM,KAAK,QAAQ,MAAM;AACzB,QAAI,CAAC;AACH;AACF,UAAM,MAAM,QAAQ,QAAQ,GAAG;AAC/B,QAAI,UAAU,CAAC;AACf,QAAI,CAAC;AACH;AACF,QAAI,OAAO,QAAQ;AACjB,gBAAU,CAAC,EAAE,IAAI,CAAC;AAAA,aACX,MAAM,QAAQ,GAAG;AACxB,gBAAU;AAAA,aACH,SAAS,GAAG;AACnB,gBAAU,CAAC,GAAG;AAChB,OAAG,iBAAiB,QAAQ,EAAE,QAAQ,CAAC,MAAM;AAC3C,QAAE,OAAO;AAAA,IACX,CAAC;AACD,YAAQ,QAAQ,CAAC,EAAE,KAAK,MAAM,MAAM,MAAM,MAAM;AAC9C,YAAM,SAASA,UAAS,cAAc,QAAQ;AAC9C,aAAO,aAAa,OAAO,IAAI;AAC/B,aAAO,aAAa,QAAQ,QAAQ,EAAE;AACtC,aAAO,aAAa,SAAS,SAAS,EAAE;AACxC,uBAAiB,QAAQ,SAAS,iBAAiB,SAAS,eAAe;AAC3E,SAAG,YAAY,MAAM;AAAA,IACvB,CAAC;AACD,OAAG,KAAK;AAAA,EACV,CAAC;AACD,QAAM,CAAC,QAAQ,MAAM,GAAG,MAAM;AAC5B,UAAM,KAAK,QAAQ,MAAM;AACzB,QAAI,CAAC;AACH;AACF,OAAG,SAAS,OAAO;AAAA,EACrB,CAAC;AACD,QAAM,CAAC,QAAQ,KAAK,GAAG,MAAM;AAC3B,UAAM,KAAK,QAAQ,MAAM;AACzB,QAAI,CAAC;AACH;AACF,OAAG,QAAQ,MAAM;AAAA,EACnB,CAAC;AACD,QAAM,CAAC,QAAQ,IAAI,GAAG,MAAM;AAC1B,UAAM,KAAK,QAAQ,MAAM;AACzB,QAAI,CAAC;AACH;AACF,OAAG,eAAe,KAAK;AAAA,EACzB,CAAC;AACD,cAAY,MAAM;AAChB,QAAI,CAACA;AACH;AACF,UAAM,aAAa,QAAQ,QAAQ,MAAM;AACzC,UAAM,KAAK,QAAQ,MAAM;AACzB,QAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC;AACxC;AACF,OAAG,iBAAiB,OAAO,EAAE,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC;AACtD,eAAW,QAAQ,CAAC,EAAE,SAAS,WAAW,MAAM,OAAO,KAAK,QAAQ,GAAG,MAAM;AAC3E,YAAM,QAAQA,UAAS,cAAc,OAAO;AAC5C,YAAM,UAAU,aAAa;AAC7B,YAAM,OAAO;AACb,YAAM,QAAQ;AACd,YAAM,MAAM;AACZ,YAAM,UAAU;AAChB,UAAI,MAAM;AACR,sBAAc,QAAQ;AACxB,SAAG,YAAY,KAAK;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACD,QAAM,EAAE,eAAe,yBAAyB,IAAI,eAAe,aAAa,CAAC,SAAS;AACxF,UAAM,KAAK,QAAQ,MAAM;AACzB,QAAI,CAAC;AACH;AACF,OAAG,cAAc;AAAA,EACnB,CAAC;AACD,QAAM,EAAE,eAAe,qBAAqB,IAAI,eAAe,SAAS,CAAC,cAAc;AACrF,UAAM,KAAK,QAAQ,MAAM;AACzB,QAAI,CAAC;AACH;AACF,QAAI,WAAW;AACb,SAAG,KAAK,EAAE,MAAM,CAAC,MAAM;AACrB,2BAAmB,QAAQ,CAAC;AAC5B,cAAM;AAAA,MACR,CAAC;AAAA,IACH,OAAO;AACL,SAAG,MAAM;AAAA,IACX;AAAA,EACF,CAAC;AACD;AAAA,IACE;AAAA,IACA;AAAA,IACA,MAAM,yBAAyB,MAAM,YAAY,QAAQ,QAAQ,MAAM,EAAE,WAAW;AAAA,IACpF;AAAA,EACF;AACA;AAAA,IACE;AAAA,IACA;AAAA,IACA,MAAM,SAAS,QAAQ,QAAQ,MAAM,EAAE;AAAA,IACvC;AAAA,EACF;AACA;AAAA,IACE;AAAA,IACA;AAAA,IACA,MAAM,SAAS,QAAQ,iBAAiB,QAAQ,MAAM,EAAE,QAAQ;AAAA,IAChE;AAAA,EACF;AACA;AAAA,IACE;AAAA,IACA;AAAA,IACA,MAAM,QAAQ,QAAQ;AAAA,IACtB;AAAA,EACF;AACA;AAAA,IACE;AAAA,IACA;AAAA,IACA,MAAM,QAAQ,QAAQ;AAAA,IACtB;AAAA,EACF;AACA;AAAA,IACE;AAAA,IACA,CAAC,WAAW,WAAW;AAAA,IACvB,MAAM;AACJ,cAAQ,QAAQ;AAChB,2BAAqB,MAAM,QAAQ,QAAQ,KAAK;AAAA,IAClD;AAAA,IACA;AAAA,EACF;AACA;AAAA,IACE;AAAA,IACA;AAAA,IACA,MAAM,QAAQ,QAAQ;AAAA,IACtB;AAAA,EACF;AACA;AAAA,IACE;AAAA,IACA;AAAA,IACA,MAAM;AACJ,cAAQ,QAAQ;AAChB,YAAM,QAAQ;AACd,2BAAqB,MAAM,QAAQ,QAAQ,IAAI;AAAA,IACjD;AAAA,IACA;AAAA,EACF;AACA;AAAA,IACE;AAAA,IACA;AAAA,IACA,MAAM,KAAK,QAAQ,QAAQ,MAAM,EAAE;AAAA,IACnC;AAAA,EACF;AACA;AAAA,IACE;AAAA,IACA;AAAA,IACA,MAAM,QAAQ,QAAQ;AAAA,IACtB;AAAA,EACF;AACA;AAAA,IACE;AAAA,IACA;AAAA,IACA,MAAM,MAAM,QAAQ;AAAA,IACpB;AAAA,EACF;AACA;AAAA,IACE;AAAA,IACA;AAAA,IACA,MAAM,qBAAqB,MAAM,QAAQ,QAAQ,KAAK;AAAA,IACtD;AAAA,EACF;AACA;AAAA,IACE;AAAA,IACA;AAAA,IACA,MAAM,qBAAqB,MAAM,QAAQ,QAAQ,IAAI;AAAA,IACrD;AAAA,EACF;AACA;AAAA,IACE;AAAA,IACA;AAAA,IACA,MAAM,mBAAmB,QAAQ;AAAA,IACjC;AAAA,EACF;AACA;AAAA,IACE;AAAA,IACA;AAAA,IACA,MAAM,mBAAmB,QAAQ;AAAA,IACjC;AAAA,EACF;AACA;AAAA,IACE;AAAA,IACA;AAAA,IACA,MAAM;AACJ,YAAM,KAAK,QAAQ,MAAM;AACzB,UAAI,CAAC;AACH;AACF,aAAO,QAAQ,GAAG;AAClB,YAAM,QAAQ,GAAG;AAAA,IACnB;AAAA,IACA;AAAA,EACF;AACA,QAAM,YAAY,CAAC;AACnB,QAAM,OAAO,MAAM,CAAC,MAAM,GAAG,MAAM;AACjC,UAAM,KAAK,QAAQ,MAAM;AACzB,QAAI,CAAC;AACH;AACF,SAAK;AACL,cAAU,CAAC,IAAI,iBAAiB,GAAG,YAAY,YAAY,MAAM,OAAO,QAAQ,cAAc,GAAG,UAAU,GAAG,eAAe;AAC7H,cAAU,CAAC,IAAI,iBAAiB,GAAG,YAAY,eAAe,MAAM,OAAO,QAAQ,cAAc,GAAG,UAAU,GAAG,eAAe;AAChI,cAAU,CAAC,IAAI,iBAAiB,GAAG,YAAY,UAAU,MAAM,OAAO,QAAQ,cAAc,GAAG,UAAU,GAAG,eAAe;AAAA,EAC7H,CAAC;AACD,oBAAkB,MAAM,UAAU,QAAQ,CAAC,aAAa,SAAS,CAAC,CAAC;AACnE,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA,eAAe,iBAAiB;AAAA,IAChC,iBAAiB,mBAAmB;AAAA,EACtC;AACF;AAEA,SAAS,WAAW,UAAU,SAAS;AACrC,QAAM,YAAY,MAAM;AACtB,QAAI,WAAW,OAAO,SAAS,QAAQ;AACrC,aAAO,gBAAgB,QAAQ,KAAK;AACtC,WAAO,gBAAgC,oBAAI,IAAI,CAAC;AAAA,EAClD;AACA,QAAM,QAAQ,UAAU;AACxB,QAAM,cAAc,IAAI,UAAU,WAAW,OAAO,SAAS,QAAQ,UAAU,QAAQ,OAAO,GAAG,IAAI,IAAI,KAAK,UAAU,IAAI;AAC5H,QAAM,YAAY,CAAC,QAAQ,SAAS;AAClC,UAAM,IAAI,KAAK,SAAS,GAAG,IAAI,CAAC;AAChC,WAAO,MAAM,IAAI,GAAG;AAAA,EACtB;AACA,QAAM,WAAW,IAAI,SAAS,UAAU,YAAY,GAAG,IAAI,GAAG,GAAG,IAAI;AACrE,QAAM,aAAa,IAAI,SAAS;AAC9B,UAAM,OAAO,YAAY,GAAG,IAAI,CAAC;AAAA,EACnC;AACA,QAAM,YAAY,MAAM;AACtB,UAAM,MAAM;AAAA,EACd;AACA,QAAM,WAAW,IAAI,SAAS;AAC5B,UAAM,MAAM,YAAY,GAAG,IAAI;AAC/B,QAAI,MAAM,IAAI,GAAG;AACf,aAAO,MAAM,IAAI,GAAG;AACtB,WAAO,UAAU,KAAK,GAAG,IAAI;AAAA,EAC/B;AACA,WAAS,OAAO;AAChB,WAAS,SAAS;AAClB,WAAS,QAAQ;AACjB,WAAS,cAAc;AACvB,WAAS,QAAQ;AACjB,SAAO;AACT;AAEA,SAAS,UAAU,UAAU,CAAC,GAAG;AAC/B,QAAM,SAAS,IAAI;AACnB,QAAM,cAAc,aAAa,MAAM,OAAO,gBAAgB,eAAe,YAAY,WAAW;AACpG,MAAI,YAAY,OAAO;AACrB,UAAM,EAAE,WAAW,IAAI,IAAI;AAC3B,kBAAc,MAAM;AAClB,aAAO,QAAQ,YAAY;AAAA,IAC7B,GAAG,UAAU,EAAE,WAAW,QAAQ,WAAW,mBAAmB,QAAQ,kBAAkB,CAAC;AAAA,EAC7F;AACA,SAAO,EAAE,aAAa,OAAO;AAC/B;AAEA,IAAM,4BAA4B;AAAA,EAChC,MAAM,CAAC,UAAU,CAAC,MAAM,OAAO,MAAM,KAAK;AAAA,EAC1C,QAAQ,CAAC,UAAU,CAAC,MAAM,SAAS,MAAM,OAAO;AAAA,EAChD,QAAQ,CAAC,UAAU,CAAC,MAAM,SAAS,MAAM,OAAO;AAAA,EAChD,UAAU,CAAC,UAAU,iBAAiB,aAAa,CAAC,MAAM,WAAW,MAAM,SAAS,IAAI;AAC1F;AACA,SAAS,SAAS,UAAU,CAAC,GAAG;AAC9B,QAAM;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,mBAAmB;AAAA,IACnB,eAAe,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IAC5B,QAAAD,UAAS;AAAA,IACT,SAASA;AAAA,IACT,SAAS;AAAA,IACT;AAAA,EACF,IAAI;AACJ,MAAI,kBAAkB;AACtB,MAAI,eAAe;AACnB,MAAI,eAAe;AACnB,QAAM,IAAI,WAAW,aAAa,CAAC;AACnC,QAAM,IAAI,WAAW,aAAa,CAAC;AACnC,QAAM,aAAa,WAAW,IAAI;AAClC,QAAM,YAAY,OAAO,SAAS,aAAa,OAAO,0BAA0B,IAAI;AACpF,QAAM,eAAe,CAAC,UAAU;AAC9B,UAAM,SAAS,UAAU,KAAK;AAC9B,sBAAkB;AAClB,QAAI,QAAQ;AACV,OAAC,EAAE,OAAO,EAAE,KAAK,IAAI;AACrB,iBAAW,QAAQ;AAAA,IACrB;AACA,QAAIA,SAAQ;AACV,qBAAeA,QAAO;AACtB,qBAAeA,QAAO;AAAA,IACxB;AAAA,EACF;AACA,QAAM,eAAe,CAAC,UAAU;AAC9B,QAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,YAAM,SAAS,UAAU,MAAM,QAAQ,CAAC,CAAC;AACzC,UAAI,QAAQ;AACV,SAAC,EAAE,OAAO,EAAE,KAAK,IAAI;AACrB,mBAAW,QAAQ;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AACA,QAAM,gBAAgB,MAAM;AAC1B,QAAI,CAAC,mBAAmB,CAACA;AACvB;AACF,UAAM,MAAM,UAAU,eAAe;AACrC,QAAI,2BAA2B,cAAc,KAAK;AAChD,QAAE,QAAQ,IAAI,CAAC,IAAIA,QAAO,UAAU;AACpC,QAAE,QAAQ,IAAI,CAAC,IAAIA,QAAO,UAAU;AAAA,IACtC;AAAA,EACF;AACA,QAAM,QAAQ,MAAM;AAClB,MAAE,QAAQ,aAAa;AACvB,MAAE,QAAQ,aAAa;AAAA,EACzB;AACA,QAAM,sBAAsB,cAAc,CAAC,UAAU,YAAY,MAAM,aAAa,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,aAAa,KAAK;AAC/H,QAAM,sBAAsB,cAAc,CAAC,UAAU,YAAY,MAAM,aAAa,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,aAAa,KAAK;AAC/H,QAAM,uBAAuB,cAAc,MAAM,YAAY,MAAM,cAAc,GAAG,CAAC,CAAC,IAAI,MAAM,cAAc;AAC9G,MAAI,QAAQ;AACV,UAAM,kBAAkB,EAAE,SAAS,KAAK;AACxC,qBAAiB,QAAQ,CAAC,aAAa,UAAU,GAAG,qBAAqB,eAAe;AACxF,QAAI,SAAS,SAAS,YAAY;AAChC,uBAAiB,QAAQ,CAAC,cAAc,WAAW,GAAG,qBAAqB,eAAe;AAC1F,UAAI;AACF,yBAAiB,QAAQ,YAAY,OAAO,eAAe;AAAA,IAC/D;AACA,QAAI,UAAU,SAAS;AACrB,uBAAiBA,SAAQ,UAAU,sBAAsB,eAAe;AAAA,EAC5E;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,kBAAkB,QAAQ,UAAU,CAAC,GAAG;AAC/C,QAAM;AAAA,IACJ,gBAAgB;AAAA,IAChB,QAAAA,UAAS;AAAA,EACX,IAAI;AACJ,QAAM,OAAO,QAAQ,QAAQ;AAC7B,QAAM,EAAE,GAAG,GAAG,WAAW,IAAI,SAAS,OAAO;AAC7C,QAAM,YAAY,WAAW,UAAU,OAAO,SAASA,WAAU,OAAO,SAASA,QAAO,SAAS,IAAI;AACrG,QAAM,WAAW,WAAW,CAAC;AAC7B,QAAM,WAAW,WAAW,CAAC;AAC7B,QAAM,mBAAmB,WAAW,CAAC;AACrC,QAAM,mBAAmB,WAAW,CAAC;AACrC,QAAM,gBAAgB,WAAW,CAAC;AAClC,QAAM,eAAe,WAAW,CAAC;AACjC,QAAM,YAAY,WAAW,IAAI;AACjC,MAAI,OAAO,MAAM;AAAA,EACjB;AACA,MAAIA,SAAQ;AACV,WAAO;AAAA,MACL,CAAC,WAAW,GAAG,CAAC;AAAA,MAChB,MAAM;AACJ,cAAM,KAAK,aAAa,SAAS;AACjC,YAAI,CAAC,MAAM,EAAE,cAAc;AACzB;AACF,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI,GAAG,sBAAsB;AAC7B,yBAAiB,QAAQ,QAAQ,SAAS,SAASA,QAAO,cAAc;AACxE,yBAAiB,QAAQ,OAAO,SAAS,SAASA,QAAO,cAAc;AACvE,sBAAc,QAAQ;AACtB,qBAAa,QAAQ;AACrB,cAAM,MAAM,EAAE,QAAQ,iBAAiB;AACvC,cAAM,MAAM,EAAE,QAAQ,iBAAiB;AACvC,kBAAU,QAAQ,UAAU,KAAK,WAAW,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,SAAS,MAAM;AAC5F,YAAI,iBAAiB,CAAC,UAAU,OAAO;AACrC,mBAAS,QAAQ;AACjB,mBAAS,QAAQ;AAAA,QACnB;AAAA,MACF;AAAA,MACA,EAAE,WAAW,KAAK;AAAA,IACpB;AACA;AAAA,MACE;AAAA,MACA;AAAA,MACA,MAAM,UAAU,QAAQ;AAAA,MACxB,EAAE,SAAS,KAAK;AAAA,IAClB;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,gBAAgB,UAAU,CAAC,GAAG;AACrC,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,UAAU;AAAA,IACV,eAAe;AAAA,IACf,QAAAA,UAAS;AAAA,EACX,IAAI;AACJ,QAAM,UAAU,WAAW,YAAY;AACvC,QAAM,aAAa,WAAW,IAAI;AAClC,MAAI,CAACA,SAAQ;AACX,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,YAAY,CAAC,YAAY,CAAC,UAAU;AACxC,QAAI;AACJ,YAAQ,QAAQ;AAChB,eAAW,QAAQ;AACnB,KAAC,KAAK,QAAQ,cAAc,OAAO,SAAS,GAAG,KAAK,SAAS,KAAK;AAAA,EACpE;AACA,QAAM,aAAa,CAAC,UAAU;AAC5B,QAAI;AACJ,YAAQ,QAAQ;AAChB,eAAW,QAAQ;AACnB,KAAC,KAAK,QAAQ,eAAe,OAAO,SAAS,GAAG,KAAK,SAAS,KAAK;AAAA,EACrE;AACA,QAAM,SAAS,SAAS,MAAM,aAAa,QAAQ,MAAM,KAAKA,OAAM;AACpE,QAAM,kBAAkB,EAAE,SAAS,MAAM,QAAQ;AACjD,mBAAiB,QAAQ,aAAa,UAAU,OAAO,GAAG,eAAe;AACzE,mBAAiBA,SAAQ,cAAc,YAAY,eAAe;AAClE,mBAAiBA,SAAQ,WAAW,YAAY,eAAe;AAC/D,MAAI,MAAM;AACR,qBAAiB,QAAQ,aAAa,UAAU,OAAO,GAAG,eAAe;AACzE,qBAAiBA,SAAQ,QAAQ,YAAY,eAAe;AAC5D,qBAAiBA,SAAQ,WAAW,YAAY,eAAe;AAAA,EACjE;AACA,MAAI,OAAO;AACT,qBAAiB,QAAQ,cAAc,UAAU,OAAO,GAAG,eAAe;AAC1E,qBAAiBA,SAAQ,YAAY,YAAY,eAAe;AAChE,qBAAiBA,SAAQ,eAAe,YAAY,eAAe;AAAA,EACrE;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,qBAAqB,UAAU,CAAC,GAAG;AAC1C,QAAM,EAAE,QAAAA,UAAS,cAAc,IAAI;AACnC,QAAMG,aAAYH,WAAU,OAAO,SAASA,QAAO;AACnD,QAAM,cAAc,aAAa,MAAMG,cAAa,cAAcA,UAAS;AAC3E,QAAM,WAAW,WAAWA,cAAa,OAAO,SAASA,WAAU,QAAQ;AAC3E,mBAAiBH,SAAQ,kBAAkB,MAAM;AAC/C,QAAIG;AACF,eAAS,QAAQA,WAAU;AAAA,EAC/B,GAAG,EAAE,SAAS,KAAK,CAAC;AACpB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,WAAW,UAAU,CAAC,GAAG;AAChC,QAAM,EAAE,QAAAH,UAAS,cAAc,IAAI;AACnC,QAAMG,aAAYH,WAAU,OAAO,SAASA,QAAO;AACnD,QAAM,cAAc,aAAa,MAAMG,cAAa,gBAAgBA,UAAS;AAC7E,QAAM,WAAW,WAAW,IAAI;AAChC,QAAM,WAAW,WAAW,KAAK;AACjC,QAAM,YAAY,WAAW,MAAM;AACnC,QAAM,WAAW,WAAW,MAAM;AAClC,QAAM,WAAW,WAAW,MAAM;AAClC,QAAM,cAAc,WAAW,MAAM;AACrC,QAAM,MAAM,WAAW,MAAM;AAC7B,QAAM,gBAAgB,WAAW,MAAM;AACvC,QAAM,OAAO,WAAW,SAAS;AACjC,QAAM,aAAa,YAAY,SAASA,WAAU;AAClD,WAAS,2BAA2B;AAClC,QAAI,CAACA;AACH;AACF,aAAS,QAAQA,WAAU;AAC3B,cAAU,QAAQ,SAAS,QAAQ,SAAS,KAAK,IAAI;AACrD,aAAS,QAAQ,SAAS,QAAQ,KAAK,IAAI,IAAI;AAC/C,QAAI,YAAY;AACd,eAAS,QAAQ,WAAW;AAC5B,kBAAY,QAAQ,WAAW;AAC/B,oBAAc,QAAQ,WAAW;AACjC,UAAI,QAAQ,WAAW;AACvB,eAAS,QAAQ,WAAW;AAC5B,WAAK,QAAQ,WAAW;AAAA,IAC1B;AAAA,EACF;AACA,QAAM,kBAAkB,EAAE,SAAS,KAAK;AACxC,MAAIH,SAAQ;AACV,qBAAiBA,SAAQ,WAAW,MAAM;AACxC,eAAS,QAAQ;AACjB,gBAAU,QAAQ,KAAK,IAAI;AAAA,IAC7B,GAAG,eAAe;AAClB,qBAAiBA,SAAQ,UAAU,MAAM;AACvC,eAAS,QAAQ;AACjB,eAAS,QAAQ,KAAK,IAAI;AAAA,IAC5B,GAAG,eAAe;AAAA,EACpB;AACA,MAAI;AACF,qBAAiB,YAAY,UAAU,0BAA0B,eAAe;AAClF,2BAAyB;AACzB,SAAO;AAAA,IACL;AAAA,IACA,UAAU,SAAS,QAAQ;AAAA,IAC3B,UAAU,SAAS,QAAQ;AAAA,IAC3B,WAAW,SAAS,SAAS;AAAA,IAC7B,UAAU,SAAS,QAAQ;AAAA,IAC3B,UAAU,SAAS,QAAQ;AAAA,IAC3B,aAAa,SAAS,WAAW;AAAA,IACjC,eAAe,SAAS,aAAa;AAAA,IACrC,KAAK,SAAS,GAAG;AAAA,IACjB,MAAM,SAAS,IAAI;AAAA,EACrB;AACF;AAEA,SAAS,OAAO,UAAU,CAAC,GAAG;AAC5B,QAAM;AAAA,IACJ,UAAU,iBAAiB;AAAA,IAC3B,WAAW;AAAA,EACb,IAAI;AACJ,QAAMa,OAAM,IAAoB,oBAAI,KAAK,CAAC;AAC1C,QAAM,SAAS,MAAMA,KAAI,QAAwB,oBAAI,KAAK;AAC1D,QAAM,WAAW,aAAa,0BAA0B,SAAS,QAAQ,EAAE,WAAW,KAAK,CAAC,IAAI,cAAc,QAAQ,UAAU,EAAE,WAAW,KAAK,CAAC;AACnJ,MAAI,gBAAgB;AAClB,WAAO;AAAA,MACL,KAAAA;AAAA,MACA,GAAG;AAAA,IACL;AAAA,EACF,OAAO;AACL,WAAOA;AAAA,EACT;AACF;AAEA,SAAS,aAAa,QAAQ;AAC5B,QAAM,MAAM,WAAW;AACvB,QAAM,UAAU,MAAM;AACpB,QAAI,IAAI;AACN,UAAI,gBAAgB,IAAI,KAAK;AAC/B,QAAI,QAAQ;AAAA,EACd;AACA;AAAA,IACE,MAAM,QAAQ,MAAM;AAAA,IACpB,CAAC,cAAc;AACb,cAAQ;AACR,UAAI;AACF,YAAI,QAAQ,IAAI,gBAAgB,SAAS;AAAA,IAC7C;AAAA,IACA,EAAE,WAAW,KAAK;AAAA,EACpB;AACA,oBAAkB,OAAO;AACzB,SAAO,SAAS,GAAG;AACrB;AAEA,SAAS,SAAS,OAAO,KAAK,KAAK;AACjC,MAAI,OAAO,UAAU,cAAc,WAAW,KAAK;AACjD,WAAO,SAAS,MAAM,MAAM,QAAQ,KAAK,GAAG,QAAQ,GAAG,GAAG,QAAQ,GAAG,CAAC,CAAC;AACzE,QAAM,SAAS,IAAI,KAAK;AACxB,SAAO,SAAS;AAAA,IACd,MAAM;AACJ,aAAO,OAAO,QAAQ,MAAM,OAAO,OAAO,QAAQ,GAAG,GAAG,QAAQ,GAAG,CAAC;AAAA,IACtE;AAAA,IACA,IAAI,QAAQ;AACV,aAAO,QAAQ,MAAM,QAAQ,QAAQ,GAAG,GAAG,QAAQ,GAAG,CAAC;AAAA,IACzD;AAAA,EACF,CAAC;AACH;AAEA,SAAS,oBAAoB,SAAS;AACpC,QAAM;AAAA,IACJ,QAAQ,OAAO;AAAA,IACf,WAAW;AAAA,IACX,OAAO;AAAA,IACP,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,EACtB,IAAI;AACJ,QAAM,kBAAkB,SAAS,UAAU,GAAG,OAAO,iBAAiB;AACtE,QAAM,YAAY,SAAS,MAAM,KAAK;AAAA,IACpC;AAAA,IACA,KAAK,KAAK,QAAQ,KAAK,IAAI,QAAQ,eAAe,CAAC;AAAA,EACrD,CAAC;AACD,QAAM,cAAc,SAAS,MAAM,GAAG,SAAS;AAC/C,QAAM,cAAc,SAAS,MAAM,YAAY,UAAU,CAAC;AAC1D,QAAM,aAAa,SAAS,MAAM,YAAY,UAAU,UAAU,KAAK;AACvE,MAAI,MAAM,IAAI,GAAG;AACf,YAAQ,MAAM,aAAa;AAAA,MACzB,WAAW,WAAW,IAAI,IAAI,QAAQ;AAAA,IACxC,CAAC;AAAA,EACH;AACA,MAAI,MAAM,QAAQ,GAAG;AACnB,YAAQ,UAAU,iBAAiB;AAAA,MACjC,WAAW,WAAW,QAAQ,IAAI,QAAQ;AAAA,IAC5C,CAAC;AAAA,EACH;AACA,WAAS,OAAO;AACd,gBAAY;AAAA,EACd;AACA,WAAS,OAAO;AACd,gBAAY;AAAA,EACd;AACA,QAAM,cAAc;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,aAAa,MAAM;AACvB,iBAAa,SAAS,WAAW,CAAC;AAAA,EACpC,CAAC;AACD,QAAM,iBAAiB,MAAM;AAC3B,qBAAiB,SAAS,WAAW,CAAC;AAAA,EACxC,CAAC;AACD,QAAM,WAAW,MAAM;AACrB,sBAAkB,SAAS,WAAW,CAAC;AAAA,EACzC,CAAC;AACD,SAAO;AACT;AAEA,SAAS,UAAU,UAAU,CAAC,GAAG;AAC/B,QAAM,EAAE,SAAS,IAAI,WAAW,OAAO;AACvC,SAAO;AACT;AAEA,SAAS,aAAa,UAAU,CAAC,GAAG;AAClC,QAAM,EAAE,QAAAb,UAAS,cAAc,IAAI;AACnC,QAAM,SAAS,WAAW,KAAK;AAC/B,QAAM,UAAU,CAAC,UAAU;AACzB,QAAI,CAACA;AACH;AACF,YAAQ,SAASA,QAAO;AACxB,UAAM,OAAO,MAAM,iBAAiB,MAAM;AAC1C,WAAO,QAAQ,CAAC;AAAA,EAClB;AACA,MAAIA,SAAQ;AACV,UAAM,kBAAkB,EAAE,SAAS,KAAK;AACxC,qBAAiBA,SAAQ,YAAY,SAAS,eAAe;AAC7D,qBAAiBA,QAAO,UAAU,cAAc,SAAS,eAAe;AACxE,qBAAiBA,QAAO,UAAU,cAAc,SAAS,eAAe;AAAA,EAC1E;AACA,SAAO;AACT;AAEA,SAAS,qBAAqB,UAAU,CAAC,GAAG;AAC1C,QAAM;AAAA,IACJ,QAAAA,UAAS;AAAA,EACX,IAAI;AACJ,QAAM,cAAc,aAAa,MAAMA,WAAU,YAAYA,WAAU,iBAAiBA,QAAO,MAAM;AACrG,QAAM,oBAAoB,YAAY,QAAQA,QAAO,OAAO,cAAc,CAAC;AAC3E,QAAM,cAAc,IAAI,kBAAkB,IAAI;AAC9C,QAAM,QAAQ,WAAW,kBAAkB,SAAS,CAAC;AACrD,MAAI,YAAY,OAAO;AACrB,qBAAiBA,SAAQ,qBAAqB,MAAM;AAClD,kBAAY,QAAQ,kBAAkB;AACtC,YAAM,QAAQ,kBAAkB;AAAA,IAClC,GAAG,EAAE,SAAS,KAAK,CAAC;AAAA,EACtB;AACA,QAAM,kBAAkB,CAAC,SAAS;AAChC,QAAI,YAAY,SAAS,OAAO,kBAAkB,SAAS;AACzD,aAAO,kBAAkB,KAAK,IAAI;AACpC,WAAO,QAAQ,OAAO,IAAI,MAAM,eAAe,CAAC;AAAA,EAClD;AACA,QAAM,oBAAoB,MAAM;AAC9B,QAAI,YAAY,SAAS,OAAO,kBAAkB,WAAW;AAC3D,wBAAkB,OAAO;AAAA,EAC7B;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,YAAY,QAAQ,UAAU,CAAC,GAAG;AACzC,QAAM;AAAA,IACJ,8BAA8B,CAAC,MAAM;AAAA,IACrC,8BAA8B,CAAC,MAAM;AAAA,IACrC,kBAAkB,CAAC,MAAM;AAAA,IACzB,kBAAkB,CAAC,MAAM;AAAA,IACzB,QAAAA,UAAS;AAAA,EACX,IAAI;AACJ,QAAM,cAAc,SAAS,qBAAqB,EAAE,QAAAA,QAAO,CAAC,CAAC;AAC7D,QAAM,oBAAoB,SAAS,qBAAqB,EAAE,QAAAA,QAAO,CAAC,CAAC;AACnE,QAAM;AAAA,IACJ,UAAU;AAAA,IACV,UAAU;AAAA,IACV,cAAc;AAAA,IACd,eAAe;AAAA,EACjB,IAAI,kBAAkB,QAAQ,EAAE,eAAe,OAAO,QAAAA,QAAO,CAAC;AAC9D,QAAM,SAAS,SAAS,MAAM;AAC5B,QAAI,YAAY,gBAAgB,YAAY,SAAS,QAAQ,YAAY,UAAU,KAAK,YAAY,SAAS,QAAQ,YAAY,UAAU,IAAI;AAC7I,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT,CAAC;AACD,QAAM,OAAO,SAAS,MAAM;AAC1B,QAAI,OAAO,UAAU,qBAAqB;AACxC,UAAI;AACJ,cAAQ,kBAAkB,aAAa;AAAA,QACrC,KAAK;AACH,kBAAQ,YAAY,QAAQ;AAC5B;AAAA,QACF,KAAK;AACH,kBAAQ,CAAC,YAAY,QAAQ;AAC7B;AAAA,QACF,KAAK;AACH,kBAAQ,CAAC,YAAY,OAAO;AAC5B;AAAA,QACF,KAAK;AACH,kBAAQ,YAAY,OAAO;AAC3B;AAAA,QACF;AACE,kBAAQ,CAAC,YAAY,OAAO;AAAA,MAChC;AACA,aAAO,4BAA4B,KAAK;AAAA,IAC1C,OAAO;AACL,YAAM,QAAQ,EAAE,EAAE,QAAQ,OAAO,QAAQ,KAAK,OAAO;AACrD,aAAO,gBAAgB,KAAK;AAAA,IAC9B;AAAA,EACF,CAAC;AACD,QAAM,OAAO,SAAS,MAAM;AAC1B,QAAI,OAAO,UAAU,qBAAqB;AACxC,UAAI;AACJ,cAAQ,kBAAkB,aAAa;AAAA,QACrC,KAAK;AACH,kBAAQ,YAAY,OAAO;AAC3B;AAAA,QACF,KAAK;AACH,kBAAQ,CAAC,YAAY,OAAO;AAC5B;AAAA,QACF,KAAK;AACH,kBAAQ,YAAY,QAAQ;AAC5B;AAAA,QACF,KAAK;AACH,kBAAQ,CAAC,YAAY,QAAQ;AAC7B;AAAA,QACF;AACE,kBAAQ,YAAY,QAAQ;AAAA,MAChC;AACA,aAAO,4BAA4B,KAAK;AAAA,IAC1C,OAAO;AACL,YAAM,SAAS,EAAE,QAAQ,MAAM,QAAQ,KAAK,MAAM;AAClD,aAAO,gBAAgB,KAAK;AAAA,IAC9B;AAAA,EACF,CAAC;AACD,SAAO,EAAE,MAAM,MAAM,OAAO;AAC9B;AAEA,SAAS,iBAAiB,UAAU,kBAAkB,GAAG;AACvD,QAAM,gBAAgB,WAAW;AACjC,QAAM,SAAS,MAAM;AACnB,UAAM,KAAK,aAAa,OAAO;AAC/B,QAAI;AACF,oBAAc,QAAQ,GAAG;AAAA,EAC7B;AACA,eAAa,MAAM;AACnB,QAAM,MAAM,QAAQ,OAAO,GAAG,MAAM;AACpC,SAAO;AACT;AAEA,SAAS,uBAAuB,SAAS,UAAU;AACjD,QAAM;AAAA,IACJ,QAAAA,UAAS;AAAA,IACT,YAAY;AAAA,IACZ,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,cAAc,aAAa,MAAMA,WAAU,yBAAyBA,OAAM;AAChF,MAAI;AACJ,QAAM,OAAO,MAAM;AACjB,gBAAY,OAAO,SAAS,SAAS,WAAW;AAAA,EAClD;AACA,QAAM,QAAQ,MAAM;AAClB,QAAI,YAAY,OAAO;AACrB,WAAK;AACL,iBAAW,IAAI,oBAAoB,QAAQ;AAC3C,eAAS,QAAQ,kBAAkB;AAAA,IACrC;AAAA,EACF;AACA,oBAAkB,IAAI;AACtB,MAAI;AACF,UAAM;AACR,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,eAAe;AAAA,EACnB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,WAAW;AAAA,EACX,UAAU;AAAA,EACV,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,aAAa;AACf;AACA,IAAM,OAAuB,OAAO,KAAK,YAAY;AACrD,SAAS,WAAW,UAAU,CAAC,GAAG;AAChC,QAAM;AAAA,IACJ,SAAS;AAAA,EACX,IAAI;AACJ,QAAM,WAAW,WAAW,KAAK;AACjC,QAAM,QAAQ,IAAI,QAAQ,gBAAgB,CAAC,CAAC;AAC5C,SAAO,OAAO,MAAM,OAAO,cAAc,MAAM,KAAK;AACpD,QAAM,UAAU,CAAC,UAAU;AACzB,aAAS,QAAQ;AACjB,QAAI,QAAQ,gBAAgB,CAAC,QAAQ,aAAa,SAAS,MAAM,WAAW;AAC1E;AACF,UAAM,QAAQ,WAAW,OAAO,MAAM,KAAK;AAAA,EAC7C;AACA,MAAI,QAAQ;AACV,UAAM,kBAAkB,EAAE,SAAS,KAAK;AACxC,qBAAiB,QAAQ,CAAC,eAAe,eAAe,WAAW,GAAG,SAAS,eAAe;AAC9F,qBAAiB,QAAQ,gBAAgB,MAAM,SAAS,QAAQ,OAAO,eAAe;AAAA,EACxF;AACA,SAAO;AAAA,IACL,GAAGY,QAAO,KAAK;AAAA,IACf;AAAA,EACF;AACF;AAEA,SAAS,eAAe,QAAQ,UAAU,CAAC,GAAG;AAC5C,QAAM,EAAE,UAAAX,YAAW,gBAAgB,IAAI;AACvC,QAAM,cAAc,aAAa,MAAMA,aAAY,wBAAwBA,SAAQ;AACnF,QAAM,UAAU,WAAW;AAC3B,QAAM,iBAAiB,WAAW;AAClC,MAAI;AACJ,MAAI,YAAY,OAAO;AACrB,UAAM,kBAAkB,EAAE,SAAS,KAAK;AACxC,qBAAiBA,WAAU,qBAAqB,MAAM;AACpD,UAAI;AACJ,YAAM,kBAAkB,KAAKA,UAAS,uBAAuB,OAAO,KAAK,QAAQ;AACjF,UAAI,iBAAiB,mBAAmB,eAAe;AACrD,gBAAQ,QAAQA,UAAS;AACzB,YAAI,CAAC,QAAQ;AACX,0BAAgB,eAAe,QAAQ;AAAA,MAC3C;AAAA,IACF,GAAG,eAAe;AAClB,qBAAiBA,WAAU,oBAAoB,MAAM;AACnD,UAAI;AACJ,YAAM,kBAAkB,KAAKA,UAAS,uBAAuB,OAAO,KAAK,QAAQ;AACjF,UAAI,iBAAiB,mBAAmB,eAAe;AACrD,cAAM,SAASA,UAAS,qBAAqB,YAAY;AACzD,cAAM,IAAI,MAAM,aAAa,MAAM,gBAAgB;AAAA,MACrD;AAAA,IACF,GAAG,eAAe;AAAA,EACpB;AACA,iBAAe,KAAK,GAAG;AACrB,QAAI;AACJ,QAAI,CAAC,YAAY;AACf,YAAM,IAAI,MAAM,oDAAoD;AACtE,mBAAe,QAAQ,aAAa,QAAQ,EAAE,gBAAgB;AAC9D,oBAAgB,aAAa,SAAS,KAAK,aAAa,MAAM,MAAM,OAAO,KAAK,eAAe,QAAQ,aAAa,CAAC;AACrH,QAAI,CAAC;AACH,YAAM,IAAI,MAAM,2BAA2B;AAC7C,kBAAc,mBAAmB;AACjC,WAAO,MAAM,MAAM,OAAO,EAAE,KAAK,aAAa;AAAA,EAChD;AACA,iBAAe,SAAS;AACtB,QAAI,CAAC,QAAQ;AACX,aAAO;AACT,IAAAA,UAAS,gBAAgB;AACzB,UAAM,MAAM,OAAO,EAAE,SAAS;AAC9B,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,gBAAgB,QAAQ,UAAU,CAAC,GAAG;AAC7C,QAAM,YAAYO,OAAM,MAAM;AAC9B,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA,oBAAoB;AAAA,EACtB,IAAI;AACJ,QAAM,WAAW,SAAS,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;AACxC,QAAM,iBAAiB,CAAC,GAAG,MAAM;AAC/B,aAAS,IAAI;AACb,aAAS,IAAI;AAAA,EACf;AACA,QAAM,SAAS,SAAS,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;AACtC,QAAM,eAAe,CAAC,GAAG,MAAM;AAC7B,WAAO,IAAI;AACX,WAAO,IAAI;AAAA,EACb;AACA,QAAM,YAAY,SAAS,MAAM,SAAS,IAAI,OAAO,CAAC;AACtD,QAAM,YAAY,SAAS,MAAM,SAAS,IAAI,OAAO,CAAC;AACtD,QAAM,EAAE,KAAK,IAAI,IAAI;AACrB,QAAM,sBAAsB,SAAS,MAAM,IAAI,IAAI,UAAU,KAAK,GAAG,IAAI,UAAU,KAAK,CAAC,KAAK,SAAS;AACvG,QAAM,YAAY,WAAW,KAAK;AAClC,QAAM,gBAAgB,WAAW,KAAK;AACtC,QAAM,YAAY,SAAS,MAAM;AAC/B,QAAI,CAAC,oBAAoB;AACvB,aAAO;AACT,QAAI,IAAI,UAAU,KAAK,IAAI,IAAI,UAAU,KAAK,GAAG;AAC/C,aAAO,UAAU,QAAQ,IAAI,SAAS;AAAA,IACxC,OAAO;AACL,aAAO,UAAU,QAAQ,IAAI,OAAO;AAAA,IACtC;AAAA,EACF,CAAC;AACD,QAAM,iBAAiB,CAAC,MAAM;AAC5B,QAAI,IAAI,IAAI;AACZ,UAAM,oBAAoB,EAAE,YAAY;AACxC,UAAM,kBAAkB,EAAE,YAAY;AACtC,YAAQ,MAAM,MAAM,KAAK,QAAQ,iBAAiB,OAAO,SAAS,GAAG,SAAS,EAAE,WAAW,MAAM,OAAO,KAAK,qBAAqB,oBAAoB,OAAO,KAAK;AAAA,EACpK;AACA,QAAM,kBAAkB,EAAE,SAAS,KAAK;AACxC,QAAM,QAAQ;AAAA,IACZ,iBAAiB,QAAQ,eAAe,CAAC,MAAM;AAC7C,UAAI,CAAC,eAAe,CAAC;AACnB;AACF,oBAAc,QAAQ;AACtB,YAAM,cAAc,EAAE;AACtB,qBAAe,OAAO,SAAS,YAAY,kBAAkB,EAAE,SAAS;AACxE,YAAM,EAAE,SAAS,GAAG,SAAS,EAAE,IAAI;AACnC,qBAAe,GAAG,CAAC;AACnB,mBAAa,GAAG,CAAC;AACjB,sBAAgB,OAAO,SAAS,aAAa,CAAC;AAAA,IAChD,GAAG,eAAe;AAAA,IAClB,iBAAiB,QAAQ,eAAe,CAAC,MAAM;AAC7C,UAAI,CAAC,eAAe,CAAC;AACnB;AACF,UAAI,CAAC,cAAc;AACjB;AACF,YAAM,EAAE,SAAS,GAAG,SAAS,EAAE,IAAI;AACnC,mBAAa,GAAG,CAAC;AACjB,UAAI,CAAC,UAAU,SAAS,oBAAoB;AAC1C,kBAAU,QAAQ;AACpB,UAAI,UAAU;AACZ,mBAAW,OAAO,SAAS,QAAQ,CAAC;AAAA,IACxC,GAAG,eAAe;AAAA,IAClB,iBAAiB,QAAQ,aAAa,CAAC,MAAM;AAC3C,UAAI,CAAC,eAAe,CAAC;AACnB;AACF,UAAI,UAAU;AACZ,sBAAc,OAAO,SAAS,WAAW,GAAG,UAAU,KAAK;AAC7D,oBAAc,QAAQ;AACtB,gBAAU,QAAQ;AAAA,IACpB,GAAG,eAAe;AAAA,EACpB;AACA,eAAa,MAAM;AACjB,QAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAChC,KAAC,MAAM,KAAK,UAAU,UAAU,OAAO,SAAS,GAAG,UAAU,OAAO,SAAS,GAAG,YAAY,gBAAgB,MAAM;AAClH,QAAI,mBAAmB;AACrB,OAAC,MAAM,KAAK,UAAU,UAAU,OAAO,SAAS,GAAG,UAAU,OAAO,SAAS,GAAG,YAAY,uBAAuB,MAAM;AACzH,OAAC,MAAM,KAAK,UAAU,UAAU,OAAO,SAAS,GAAG,UAAU,OAAO,SAAS,GAAG,YAAY,mBAAmB,MAAM;AACrH,OAAC,MAAM,KAAK,UAAU,UAAU,OAAO,SAAS,GAAG,UAAU,OAAO,SAAS,GAAG,YAAY,eAAe,MAAM;AAAA,IACnH;AAAA,EACF,CAAC;AACD,QAAM,OAAO,MAAM,MAAM,QAAQ,CAAC,MAAM,EAAE,CAAC;AAC3C,SAAO;AAAA,IACL,WAAW,SAAS,SAAS;AAAA,IAC7B,WAAW,SAAS,SAAS;AAAA,IAC7B,UAAU,SAAS,QAAQ;AAAA,IAC3B,QAAQ,SAAS,MAAM;AAAA,IACvB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,wBAAwB,SAAS;AACxC,QAAM,UAAU,cAAc,iCAAiC,OAAO;AACtE,QAAM,SAAS,cAAc,gCAAgC,OAAO;AACpE,SAAO,SAAS,MAAM;AACpB,QAAI,OAAO;AACT,aAAO;AACT,QAAI,QAAQ;AACV,aAAO;AACT,WAAO;AAAA,EACT,CAAC;AACH;AAEA,SAAS,qBAAqB,SAAS;AACrC,QAAM,SAAS,cAAc,4BAA4B,OAAO;AAChE,QAAM,SAAS,cAAc,4BAA4B,OAAO;AAChE,QAAM,WAAW,cAAc,8BAA8B,OAAO;AACpE,SAAO,SAAS,MAAM;AACpB,QAAI,OAAO;AACT,aAAO;AACT,QAAI,OAAO;AACT,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,WAAO;AAAA,EACT,CAAC;AACH;AAEA,SAAS,sBAAsB,UAAU,CAAC,GAAG;AAC3C,QAAM,EAAE,QAAAR,UAAS,cAAc,IAAI;AACnC,MAAI,CAACA;AACH,WAAO,IAAI,CAAC,IAAI,CAAC;AACnB,QAAMG,aAAYH,QAAO;AACzB,QAAM,QAAQ,IAAIG,WAAU,SAAS;AACrC,mBAAiBH,SAAQ,kBAAkB,MAAM;AAC/C,UAAM,QAAQG,WAAU;AAAA,EAC1B,GAAG,EAAE,SAAS,KAAK,CAAC;AACpB,SAAO;AACT;AAEA,SAAS,0BAA0B,SAAS;AAC1C,QAAM,YAAY,cAAc,oCAAoC,OAAO;AAC3E,SAAO,SAAS,MAAM;AACpB,QAAI,UAAU;AACZ,aAAO;AACT,WAAO;AAAA,EACT,CAAC;AACH;AAEA,SAAS,gCAAgC,SAAS;AAChD,QAAM,YAAY,cAAc,0CAA0C,OAAO;AACjF,SAAO,SAAS,MAAM;AACpB,QAAI,UAAU;AACZ,aAAO;AACT,WAAO;AAAA,EACT,CAAC;AACH;AAEA,SAAS,YAAY,OAAO,cAAc;AACxC,QAAM,WAAW,WAAW,YAAY;AACxC;AAAA,IACEK,OAAM,KAAK;AAAA,IACX,CAAC,GAAG,aAAa;AACf,eAAS,QAAQ;AAAA,IACnB;AAAA,IACA,EAAE,OAAO,OAAO;AAAA,EAClB;AACA,SAAO,SAAS,QAAQ;AAC1B;AAEA,IAAM,aAAa;AACnB,IAAM,eAAe;AACrB,IAAM,gBAAgB;AACtB,IAAM,cAAc;AACpB,SAAS,oBAAoB;AAC3B,QAAM,MAAM,WAAW,EAAE;AACzB,QAAM,QAAQ,WAAW,EAAE;AAC3B,QAAM,SAAS,WAAW,EAAE;AAC5B,QAAM,OAAO,WAAW,EAAE;AAC1B,MAAI,UAAU;AACZ,UAAM,YAAY,UAAU,UAAU;AACtC,UAAM,cAAc,UAAU,YAAY;AAC1C,UAAM,eAAe,UAAU,aAAa;AAC5C,UAAM,aAAa,UAAU,WAAW;AACxC,cAAU,QAAQ;AAClB,gBAAY,QAAQ;AACpB,iBAAa,QAAQ;AACrB,eAAW,QAAQ;AACnB,WAAO;AACP,qBAAiB,UAAU,cAAc,MAAM,GAAG,EAAE,SAAS,KAAK,CAAC;AAAA,EACrE;AACA,WAAS,SAAS;AAChB,QAAI,QAAQ,SAAS,UAAU;AAC/B,UAAM,QAAQ,SAAS,YAAY;AACnC,WAAO,QAAQ,SAAS,aAAa;AACrC,SAAK,QAAQ,SAAS,WAAW;AAAA,EACnC;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,SAAS,UAAU;AAC1B,SAAO,iBAAiB,SAAS,eAAe,EAAE,iBAAiB,QAAQ;AAC7E;AAEA,SAAS,aAAa,KAAK,WAAW,MAAM,UAAU,CAAC,GAAG;AACxD,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,OAAO;AAAA,IACP,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAAP,YAAW;AAAA,IACX,QAAQ,CAAC;AAAA,EACX,IAAI;AACJ,QAAM,YAAY,WAAW,IAAI;AACjC,MAAI,WAAW;AACf,QAAM,aAAa,CAAC,sBAAsB,IAAI,QAAQ,CAAC,SAAS,WAAW;AACzE,UAAM,qBAAqB,CAAC,QAAQ;AAClC,gBAAU,QAAQ;AAClB,cAAQ,GAAG;AACX,aAAO;AAAA,IACT;AACA,QAAI,CAACA,WAAU;AACb,cAAQ,KAAK;AACb;AAAA,IACF;AACA,QAAI,eAAe;AACnB,QAAI,KAAKA,UAAS,cAAc,eAAe,QAAQ,GAAG,CAAC,IAAI;AAC/D,QAAI,CAAC,IAAI;AACP,WAAKA,UAAS,cAAc,QAAQ;AACpC,SAAG,OAAO;AACV,SAAG,QAAQ;AACX,SAAG,MAAM,QAAQ,GAAG;AACpB,UAAI;AACF,WAAG,QAAQ;AACb,UAAI;AACF,WAAG,cAAc;AACnB,UAAI;AACF,WAAG,WAAW;AAChB,UAAI;AACF,WAAG,iBAAiB;AACtB,aAAO,QAAQ,KAAK,EAAE,QAAQ,CAAC,CAAC,MAAM,KAAK,MAAM,MAAM,OAAO,SAAS,GAAG,aAAa,MAAM,KAAK,CAAC;AACnG,qBAAe;AAAA,IACjB,WAAW,GAAG,aAAa,aAAa,GAAG;AACzC,yBAAmB,EAAE;AAAA,IACvB;AACA,UAAM,kBAAkB;AAAA,MACtB,SAAS;AAAA,IACX;AACA,qBAAiB,IAAI,SAAS,CAAC,UAAU,OAAO,KAAK,GAAG,eAAe;AACvE,qBAAiB,IAAI,SAAS,CAAC,UAAU,OAAO,KAAK,GAAG,eAAe;AACvE,qBAAiB,IAAI,QAAQ,MAAM;AACjC,SAAG,aAAa,eAAe,MAAM;AACrC,eAAS,EAAE;AACX,yBAAmB,EAAE;AAAA,IACvB,GAAG,eAAe;AAClB,QAAI;AACF,WAAKA,UAAS,KAAK,YAAY,EAAE;AACnC,QAAI,CAAC;AACH,yBAAmB,EAAE;AAAA,EACzB,CAAC;AACD,QAAM,OAAO,CAAC,oBAAoB,SAAS;AACzC,QAAI,CAAC;AACH,iBAAW,WAAW,iBAAiB;AACzC,WAAO;AAAA,EACT;AACA,QAAM,SAAS,MAAM;AACnB,QAAI,CAACA;AACH;AACF,eAAW;AACX,QAAI,UAAU;AACZ,gBAAU,QAAQ;AACpB,UAAM,KAAKA,UAAS,cAAc,eAAe,QAAQ,GAAG,CAAC,IAAI;AACjE,QAAI;AACF,MAAAA,UAAS,KAAK,YAAY,EAAE;AAAA,EAChC;AACA,MAAI,aAAa,CAAC;AAChB,iBAAa,IAAI;AACnB,MAAI,CAAC;AACH,mBAAe,MAAM;AACvB,SAAO,EAAE,WAAW,MAAM,OAAO;AACnC;AAEA,SAAS,oBAAoB,KAAK;AAChC,QAAM,QAAQ,OAAO,iBAAiB,GAAG;AACzC,MAAI,MAAM,cAAc,YAAY,MAAM,cAAc,YAAY,MAAM,cAAc,UAAU,IAAI,cAAc,IAAI,eAAe,MAAM,cAAc,UAAU,IAAI,eAAe,IAAI,cAAc;AACxM,WAAO;AAAA,EACT,OAAO;AACL,UAAM,SAAS,IAAI;AACnB,QAAI,CAAC,UAAU,OAAO,YAAY;AAChC,aAAO;AACT,WAAO,oBAAoB,MAAM;AAAA,EACnC;AACF;AACA,SAAS,eAAe,UAAU;AAChC,QAAM,IAAI,YAAY,OAAO;AAC7B,QAAM,UAAU,EAAE;AAClB,MAAI,oBAAoB,OAAO;AAC7B,WAAO;AACT,MAAI,EAAE,QAAQ,SAAS;AACrB,WAAO;AACT,MAAI,EAAE;AACJ,MAAE,eAAe;AACnB,SAAO;AACT;AACA,IAAM,oBAAoC,oBAAI,QAAQ;AACtD,SAAS,cAAc,SAAS,eAAe,OAAO;AACpD,QAAM,WAAW,WAAW,YAAY;AACxC,MAAI,wBAAwB;AAC5B,MAAI,kBAAkB;AACtB,QAAMO,OAAM,OAAO,GAAG,CAAC,OAAO;AAC5B,UAAM,SAAS,eAAe,QAAQ,EAAE,CAAC;AACzC,QAAI,QAAQ;AACV,YAAM,MAAM;AACZ,UAAI,CAAC,kBAAkB,IAAI,GAAG;AAC5B,0BAAkB,IAAI,KAAK,IAAI,MAAM,QAAQ;AAC/C,UAAI,IAAI,MAAM,aAAa;AACzB,0BAAkB,IAAI,MAAM;AAC9B,UAAI,IAAI,MAAM,aAAa;AACzB,eAAO,SAAS,QAAQ;AAC1B,UAAI,SAAS;AACX,eAAO,IAAI,MAAM,WAAW;AAAA,IAChC;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,EACb,CAAC;AACD,QAAM,OAAO,MAAM;AACjB,UAAM,KAAK,eAAe,QAAQ,OAAO,CAAC;AAC1C,QAAI,CAAC,MAAM,SAAS;AAClB;AACF,QAAI,OAAO;AACT,8BAAwB;AAAA,QACtB;AAAA,QACA;AAAA,QACA,CAAC,MAAM;AACL,yBAAe,CAAC;AAAA,QAClB;AAAA,QACA,EAAE,SAAS,MAAM;AAAA,MACnB;AAAA,IACF;AACA,OAAG,MAAM,WAAW;AACpB,aAAS,QAAQ;AAAA,EACnB;AACA,QAAM,SAAS,MAAM;AACnB,UAAM,KAAK,eAAe,QAAQ,OAAO,CAAC;AAC1C,QAAI,CAAC,MAAM,CAAC,SAAS;AACnB;AACF,QAAI;AACF,+BAAyB,OAAO,SAAS,sBAAsB;AACjE,OAAG,MAAM,WAAW;AACpB,sBAAkB,OAAO,EAAE;AAC3B,aAAS,QAAQ;AAAA,EACnB;AACA,oBAAkB,MAAM;AACxB,SAAO,SAAS;AAAA,IACd,MAAM;AACJ,aAAO,SAAS;AAAA,IAClB;AAAA,IACA,IAAI,GAAG;AACL,UAAI;AACF,aAAK;AAAA,UACF,QAAO;AAAA,IACd;AAAA,EACF,CAAC;AACH;AAEA,SAAS,kBAAkB,KAAK,cAAc,UAAU,CAAC,GAAG;AAC1D,QAAM,EAAE,QAAAR,UAAS,cAAc,IAAI;AACnC,SAAO,WAAW,KAAK,cAAcA,WAAU,OAAO,SAASA,QAAO,gBAAgB,OAAO;AAC/F;AAEA,SAAS,SAAS,eAAe,CAAC,GAAG,UAAU,CAAC,GAAG;AACjD,QAAM,EAAE,WAAAG,aAAY,iBAAiB,IAAI;AACzC,QAAM,aAAaA;AACnB,QAAM,cAAc,aAAa,MAAM,cAAc,cAAc,UAAU;AAC7E,QAAM,QAAQ,OAAO,kBAAkB,CAAC,MAAM;AAC5C,QAAI,YAAY,OAAO;AACrB,YAAM,OAAO;AAAA,QACX,GAAG,QAAQ,YAAY;AAAA,QACvB,GAAG,QAAQ,eAAe;AAAA,MAC5B;AACA,UAAI,UAAU;AACd,UAAI,KAAK,SAAS,WAAW;AAC3B,kBAAU,WAAW,SAAS,EAAE,OAAO,KAAK,MAAM,CAAC;AACrD,UAAI;AACF,eAAO,WAAW,MAAM,IAAI;AAAA,IAChC;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,gBAAgB,CAAC,QAAQ,cAAc,OAAO,KAAK,SAAS;AAClE,IAAM,iBAAiB,CAAC,GAAG,MAAM,IAAI;AACrC,SAAS,aAAa,MAAM;AAC1B,MAAI,IAAI,IAAI,IAAI;AAChB,QAAM,CAAC,MAAM,IAAI;AACjB,MAAI,YAAY;AAChB,MAAI,UAAU,CAAC;AACf,MAAI,KAAK,WAAW,GAAG;AACrB,QAAI,OAAO,KAAK,CAAC,MAAM,UAAU;AAC/B,gBAAU,KAAK,CAAC;AAChB,mBAAa,KAAK,QAAQ,cAAc,OAAO,KAAK;AAAA,IACtD,OAAO;AACL,mBAAa,KAAK,KAAK,CAAC,MAAM,OAAO,KAAK;AAAA,IAC5C;AAAA,EACF,WAAW,KAAK,SAAS,GAAG;AAC1B,iBAAa,KAAK,KAAK,CAAC,MAAM,OAAO,KAAK;AAC1C,eAAW,KAAK,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC;AAAA,EAC3C;AACA,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,SAAS;AAAA,EACX,IAAI;AACJ,MAAI,CAAC;AACH,WAAO,SAAS,MAAM,OAAO,CAAC,GAAG,QAAQ,MAAM,CAAC,GAAG,SAAS,CAAC;AAC/D,cAAY,MAAM;AAChB,UAAM,SAAS,OAAO,QAAQ,MAAM,GAAG,SAAS;AAChD,QAAI,MAAM,MAAM;AACd,aAAO,QAAQ;AAAA;AAEf,aAAO,OAAO,GAAG,OAAO,QAAQ,GAAG,MAAM;AAAA,EAC7C,CAAC;AACD,SAAO;AACT;AAEA,SAAS,qBAAqB,UAAU,CAAC,GAAG;AAC1C,QAAM;AAAA,IACJ,iBAAiB;AAAA,IACjB,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,QAAAH,UAAS;AAAA,EACX,IAAI;AACJ,QAAM,OAAOQ,OAAM,QAAQ,QAAQ,OAAO;AAC1C,QAAM,cAAc,WAAW,KAAK;AACpC,QAAM,UAAU,WAAW,KAAK;AAChC,QAAM,SAAS,WAAW,EAAE;AAC5B,QAAM,QAAQ,WAAW,MAAM;AAC/B,MAAI;AACJ,QAAM,QAAQ,MAAM;AAClB,gBAAY,QAAQ;AAAA,EACtB;AACA,QAAM,OAAO,MAAM;AACjB,gBAAY,QAAQ;AAAA,EACtB;AACA,QAAM,SAAS,CAAC,QAAQ,CAAC,YAAY,UAAU;AAC7C,QAAI,OAAO;AACT,YAAM;AAAA,IACR,OAAO;AACL,WAAK;AAAA,IACP;AAAA,EACF;AACA,QAAM,oBAAoBR,YAAWA,QAAO,qBAAqBA,QAAO;AACxE,QAAM,cAAc,aAAa,MAAM,iBAAiB;AACxD,MAAI,YAAY,OAAO;AACrB,kBAAc,IAAI,kBAAkB;AACpC,gBAAY,aAAa;AACzB,gBAAY,iBAAiB;AAC7B,gBAAY,OAAO,QAAQ,IAAI;AAC/B,gBAAY,kBAAkB;AAC9B,gBAAY,UAAU,MAAM;AAC1B,kBAAY,QAAQ;AACpB,cAAQ,QAAQ;AAAA,IAClB;AACA,UAAM,MAAM,CAAC,UAAU;AACrB,UAAI,eAAe,CAAC,YAAY;AAC9B,oBAAY,OAAO;AAAA,IACvB,CAAC;AACD,gBAAY,WAAW,CAAC,UAAU;AAChC,YAAM,gBAAgB,MAAM,QAAQ,MAAM,WAAW;AACrD,YAAM,EAAE,WAAW,IAAI,cAAc,CAAC;AACtC,cAAQ,QAAQ,cAAc;AAC9B,aAAO,QAAQ;AACf,YAAM,QAAQ;AAAA,IAChB;AACA,gBAAY,UAAU,CAAC,UAAU;AAC/B,YAAM,QAAQ;AAAA,IAChB;AACA,gBAAY,QAAQ,MAAM;AACxB,kBAAY,QAAQ;AACpB,kBAAY,OAAO,QAAQ,IAAI;AAAA,IACjC;AACA,UAAM,aAAa,CAAC,UAAU,aAAa;AACzC,UAAI,aAAa;AACf;AACF,UAAI;AACF,oBAAY,MAAM;AAAA;AAElB,oBAAY,KAAK;AAAA,IACrB,CAAC;AAAA,EACH;AACA,oBAAkB,MAAM;AACtB,SAAK;AAAA,EACP,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,mBAAmB,MAAM,UAAU,CAAC,GAAG;AAC9C,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,SAAS;AAAA,IACT,QAAAA,UAAS;AAAA,EACX,IAAI;AACJ,QAAM,QAAQA,WAAUA,QAAO;AAC/B,QAAM,cAAc,aAAa,MAAM,KAAK;AAC5C,QAAM,YAAY,WAAW,KAAK;AAClC,QAAM,SAAS,WAAW,MAAM;AAChC,QAAM,aAAaQ,OAAM,QAAQ,EAAE;AACnC,QAAM,OAAOA,OAAM,QAAQ,QAAQ,OAAO;AAC1C,QAAM,QAAQ,WAAW,MAAM;AAC/B,QAAM,SAAS,CAAC,QAAQ,CAAC,UAAU,UAAU;AAC3C,cAAU,QAAQ;AAAA,EACpB;AACA,QAAM,yBAAyB,CAAC,eAAe;AAC7C,eAAW,OAAO,QAAQ,IAAI;AAC9B,eAAW,QAAQ,QAAQ,QAAQ,KAAK,KAAK;AAC7C,eAAW,QAAQ,QAAQ,KAAK;AAChC,eAAW,OAAO,QAAQ,IAAI;AAC9B,eAAW,SAAS;AACpB,eAAW,UAAU,MAAM;AACzB,gBAAU,QAAQ;AAClB,aAAO,QAAQ;AAAA,IACjB;AACA,eAAW,UAAU,MAAM;AACzB,gBAAU,QAAQ;AAClB,aAAO,QAAQ;AAAA,IACjB;AACA,eAAW,WAAW,MAAM;AAC1B,gBAAU,QAAQ;AAClB,aAAO,QAAQ;AAAA,IACjB;AACA,eAAW,QAAQ,MAAM;AACvB,gBAAU,QAAQ;AAClB,aAAO,QAAQ;AAAA,IACjB;AACA,eAAW,UAAU,CAAC,UAAU;AAC9B,YAAM,QAAQ;AAAA,IAChB;AAAA,EACF;AACA,QAAM,YAAY,SAAS,MAAM;AAC/B,cAAU,QAAQ;AAClB,WAAO,QAAQ;AACf,UAAM,eAAe,IAAI,yBAAyB,WAAW,KAAK;AAClE,2BAAuB,YAAY;AACnC,WAAO;AAAA,EACT,CAAC;AACD,QAAM,QAAQ,MAAM;AAClB,UAAM,OAAO;AACb,QAAI;AACF,YAAM,MAAM,UAAU,KAAK;AAAA,EAC/B;AACA,QAAM,OAAO,MAAM;AACjB,UAAM,OAAO;AACb,cAAU,QAAQ;AAAA,EACpB;AACA,MAAI,YAAY,OAAO;AACrB,2BAAuB,UAAU,KAAK;AACtC,UAAM,MAAM,CAAC,UAAU;AACrB,UAAI,UAAU,SAAS,CAAC,UAAU;AAChC,kBAAU,MAAM,OAAO;AAAA,IAC3B,CAAC;AACD,QAAI,QAAQ,OAAO;AACjB,YAAM,QAAQ,OAAO,MAAM;AACzB,cAAM,OAAO;AAAA,MACf,CAAC;AAAA,IACH;AACA,UAAM,WAAW,MAAM;AACrB,UAAI,UAAU;AACZ,cAAM,OAAO;AAAA;AAEb,cAAM,MAAM;AAAA,IAChB,CAAC;AAAA,EACH;AACA,oBAAkB,MAAM;AACtB,cAAU,QAAQ;AAAA,EACpB,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,WAAW,OAAO,aAAa;AACtC,QAAM,WAAW,IAAI,KAAK;AAC1B,QAAM,YAAY,SAAS,MAAM,MAAM,QAAQ,SAAS,KAAK,IAAI,SAAS,QAAQ,OAAO,KAAK,SAAS,KAAK,CAAC;AAC7G,QAAM,QAAQ,IAAI,UAAU,MAAM,QAAQ,eAAe,OAAO,cAAc,UAAU,MAAM,CAAC,CAAC,CAAC;AACjG,QAAM,UAAU,SAAS,MAAM,GAAG,MAAM,KAAK,CAAC;AAC9C,QAAM,UAAU,SAAS,MAAM,MAAM,UAAU,CAAC;AAChD,QAAM,SAAS,SAAS,MAAM,MAAM,UAAU,UAAU,MAAM,SAAS,CAAC;AACxE,QAAM,OAAO,SAAS,MAAM,UAAU,MAAM,MAAM,QAAQ,CAAC,CAAC;AAC5D,QAAM,WAAW,SAAS,MAAM,UAAU,MAAM,MAAM,QAAQ,CAAC,CAAC;AAChE,WAAS,GAAG,QAAQ;AAClB,QAAI,MAAM,QAAQ,SAAS,KAAK;AAC9B,aAAO,SAAS,MAAM,MAAM;AAC9B,WAAO,SAAS,MAAM,UAAU,MAAM,MAAM,CAAC;AAAA,EAC/C;AACA,WAASO,KAAI,MAAM;AACjB,QAAI,CAAC,UAAU,MAAM,SAAS,IAAI;AAChC;AACF,WAAO,GAAG,UAAU,MAAM,QAAQ,IAAI,CAAC;AAAA,EACzC;AACA,WAAS,KAAK,MAAM;AAClB,QAAI,UAAU,MAAM,SAAS,IAAI;AAC/B,YAAM,QAAQ,UAAU,MAAM,QAAQ,IAAI;AAAA,EAC9C;AACA,WAAS,WAAW;AAClB,QAAI,OAAO;AACT;AACF,UAAM;AAAA,EACR;AACA,WAAS,eAAe;AACtB,QAAI,QAAQ;AACV;AACF,UAAM;AAAA,EACR;AACA,WAAS,SAAS,MAAM;AACtB,QAAI,QAAQ,IAAI;AACd,WAAK,IAAI;AAAA,EACb;AACA,WAAS,OAAO,MAAM;AACpB,WAAO,UAAU,MAAM,QAAQ,IAAI,MAAM,MAAM,QAAQ;AAAA,EACzD;AACA,WAAS,WAAW,MAAM;AACxB,WAAO,UAAU,MAAM,QAAQ,IAAI,MAAM,MAAM,QAAQ;AAAA,EACzD;AACA,WAAS,UAAU,MAAM;AACvB,WAAO,UAAU,MAAM,QAAQ,IAAI,MAAM,MAAM;AAAA,EACjD;AACA,WAAS,SAAS,MAAM;AACtB,WAAO,MAAM,QAAQ,UAAU,MAAM,QAAQ,IAAI;AAAA,EACnD;AACA,WAAS,QAAQ,MAAM;AACrB,WAAO,MAAM,QAAQ,UAAU,MAAM,QAAQ,IAAI;AAAA,EACnD;AACA,SAAO;AAAA,IACL,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAAA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,gBAAgB,KAAK,cAAc,SAAS,UAAU,CAAC,GAAG;AACjE,MAAI;AACJ,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,yBAAyB;AAAA,IACzB,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB;AAAA,IACA,QAAAf,UAAS;AAAA,IACT;AAAA,IACA,UAAU,CAAC,MAAM;AACf,cAAQ,MAAM,CAAC;AAAA,IACjB;AAAA,EACF,IAAI;AACJ,QAAM,UAAU,QAAQ,YAAY;AACpC,QAAM,OAAO,oBAAoB,OAAO;AACxC,QAAM,QAAQ,UAAU,aAAa,KAAK,QAAQ,YAAY,CAAC;AAC/D,QAAM,cAAc,KAAK,QAAQ,eAAe,OAAO,KAAK,mBAAmB,IAAI;AACnF,MAAI,CAAC,SAAS;AACZ,QAAI;AACF,gBAAU,cAAc,0BAA0B,MAAM;AACtD,YAAI;AACJ,gBAAQ,MAAM,kBAAkB,OAAO,SAAS,IAAI;AAAA,MACtD,CAAC,EAAE;AAAA,IACL,SAAS,GAAG;AACV,cAAQ,CAAC;AAAA,IACX;AAAA,EACF;AACA,iBAAe,KAAK,OAAO;AACzB,QAAI,CAAC,WAAW,SAAS,MAAM,QAAQ;AACrC;AACF,QAAI;AACF,YAAM,WAAW,QAAQ,MAAM,WAAW,MAAM,QAAQ,QAAQ,GAAG;AACnE,UAAI,YAAY,MAAM;AACpB,aAAK,QAAQ;AACb,YAAI,iBAAiB,YAAY;AAC/B,gBAAM,QAAQ,QAAQ,KAAK,MAAM,WAAW,MAAM,OAAO,CAAC;AAAA,MAC9D,WAAW,eAAe;AACxB,cAAM,QAAQ,MAAM,WAAW,KAAK,QAAQ;AAC5C,YAAI,OAAO,kBAAkB;AAC3B,eAAK,QAAQ,cAAc,OAAO,OAAO;AAAA,iBAClC,SAAS,YAAY,CAAC,MAAM,QAAQ,KAAK;AAChD,eAAK,QAAQ,EAAE,GAAG,SAAS,GAAG,MAAM;AAAA,YACjC,MAAK,QAAQ;AAAA,MACpB,OAAO;AACL,aAAK,QAAQ,MAAM,WAAW,KAAK,QAAQ;AAAA,MAC7C;AAAA,IACF,SAAS,GAAG;AACV,cAAQ,CAAC;AAAA,IACX;AAAA,EACF;AACA,OAAK;AACL,MAAIA,WAAU;AACZ,qBAAiBA,SAAQ,WAAW,CAAC,MAAM,QAAQ,QAAQ,EAAE,KAAK,MAAM,KAAK,CAAC,CAAC,GAAG,EAAE,SAAS,KAAK,CAAC;AACrG,MAAI,SAAS;AACX;AAAA,MACE;AAAA,MACA,YAAY;AACV,YAAI;AACF,cAAI,KAAK,SAAS;AAChB,kBAAM,QAAQ,WAAW,GAAG;AAAA;AAE5B,kBAAM,QAAQ,QAAQ,KAAK,MAAM,WAAW,MAAM,KAAK,KAAK,CAAC;AAAA,QACjE,SAAS,GAAG;AACV,kBAAQ,CAAC;AAAA,QACX;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAI,MAAM;AACV,SAAS,YAAY,KAAK,UAAU,CAAC,GAAG;AACtC,QAAM,WAAW,WAAW,KAAK;AACjC,QAAM;AAAA,IACJ,UAAAC,YAAW;AAAA,IACX,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,KAAK,mBAAmB,EAAE,GAAG;AAAA,EAC/B,IAAI;AACJ,QAAM,SAAS,WAAW,GAAG;AAC7B,MAAI,OAAO,MAAM;AAAA,EACjB;AACA,QAAM,OAAO,MAAM;AACjB,QAAI,CAACA;AACH;AACF,UAAM,KAAKA,UAAS,eAAe,EAAE,KAAKA,UAAS,cAAc,OAAO;AACxE,QAAI,CAAC,GAAG,aAAa;AACnB,SAAG,KAAK;AACR,UAAI,QAAQ;AACV,WAAG,QAAQ,QAAQ;AACrB,MAAAA,UAAS,KAAK,YAAY,EAAE;AAAA,IAC9B;AACA,QAAI,SAAS;AACX;AACF,WAAO;AAAA,MACL;AAAA,MACA,CAAC,UAAU;AACT,WAAG,cAAc;AAAA,MACnB;AAAA,MACA,EAAE,WAAW,KAAK;AAAA,IACpB;AACA,aAAS,QAAQ;AAAA,EACnB;AACA,QAAM,SAAS,MAAM;AACnB,QAAI,CAACA,aAAY,CAAC,SAAS;AACzB;AACF,SAAK;AACL,IAAAA,UAAS,KAAK,YAAYA,UAAS,eAAe,EAAE,CAAC;AACrD,aAAS,QAAQ;AAAA,EACnB;AACA,MAAI,aAAa,CAAC;AAChB,iBAAa,IAAI;AACnB,MAAI,CAAC;AACH,sBAAkB,MAAM;AAC1B,SAAO;AAAA,IACL;AAAA,IACA,KAAK;AAAA,IACL;AAAA,IACA;AAAA,IACA,UAAU,SAAS,QAAQ;AAAA,EAC7B;AACF;AAEA,SAAS,SAAS,QAAQ,UAAU,CAAC,GAAG;AACtC,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,EACZ,IAAI;AACJ,QAAM,cAAc,SAAS,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;AAC3C,QAAM,YAAY,SAAS,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;AACzC,QAAM,QAAQ,SAAS,MAAM,YAAY,IAAI,UAAU,CAAC;AACxD,QAAM,QAAQ,SAAS,MAAM,YAAY,IAAI,UAAU,CAAC;AACxD,QAAM,EAAE,KAAK,IAAI,IAAI;AACrB,QAAM,sBAAsB,SAAS,MAAM,IAAI,IAAI,MAAM,KAAK,GAAG,IAAI,MAAM,KAAK,CAAC,KAAK,SAAS;AAC/F,QAAM,YAAY,WAAW,KAAK;AAClC,QAAM,YAAY,SAAS,MAAM;AAC/B,QAAI,CAAC,oBAAoB;AACvB,aAAO;AACT,QAAI,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,KAAK,GAAG;AACvC,aAAO,MAAM,QAAQ,IAAI,SAAS;AAAA,IACpC,OAAO;AACL,aAAO,MAAM,QAAQ,IAAI,OAAO;AAAA,IAClC;AAAA,EACF,CAAC;AACD,QAAM,sBAAsB,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE,OAAO;AAC9E,QAAM,oBAAoB,CAAC,GAAG,MAAM;AAClC,gBAAY,IAAI;AAChB,gBAAY,IAAI;AAAA,EAClB;AACA,QAAM,kBAAkB,CAAC,GAAG,MAAM;AAChC,cAAU,IAAI;AACd,cAAU,IAAI;AAAA,EAChB;AACA,QAAM,kBAAkB,EAAE,SAAS,SAAS,CAAC,QAAQ;AACrD,QAAM,aAAa,CAAC,MAAM;AACxB,QAAI,UAAU;AACZ,oBAAc,OAAO,SAAS,WAAW,GAAG,UAAU,KAAK;AAC7D,cAAU,QAAQ;AAAA,EACpB;AACA,QAAM,QAAQ;AAAA,IACZ,iBAAiB,QAAQ,cAAc,CAAC,MAAM;AAC5C,UAAI,EAAE,QAAQ,WAAW;AACvB;AACF,YAAM,CAAC,GAAG,CAAC,IAAI,oBAAoB,CAAC;AACpC,wBAAkB,GAAG,CAAC;AACtB,sBAAgB,GAAG,CAAC;AACpB,sBAAgB,OAAO,SAAS,aAAa,CAAC;AAAA,IAChD,GAAG,eAAe;AAAA,IAClB,iBAAiB,QAAQ,aAAa,CAAC,MAAM;AAC3C,UAAI,EAAE,QAAQ,WAAW;AACvB;AACF,YAAM,CAAC,GAAG,CAAC,IAAI,oBAAoB,CAAC;AACpC,sBAAgB,GAAG,CAAC;AACpB,UAAI,gBAAgB,WAAW,CAAC,gBAAgB,WAAW,KAAK,IAAI,MAAM,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK;AACrG,UAAE,eAAe;AACnB,UAAI,CAAC,UAAU,SAAS,oBAAoB;AAC1C,kBAAU,QAAQ;AACpB,UAAI,UAAU;AACZ,mBAAW,OAAO,SAAS,QAAQ,CAAC;AAAA,IACxC,GAAG,eAAe;AAAA,IAClB,iBAAiB,QAAQ,CAAC,YAAY,aAAa,GAAG,YAAY,eAAe;AAAA,EACnF;AACA,QAAM,OAAO,MAAM,MAAM,QAAQ,CAAC,MAAM,EAAE,CAAC;AAC3C,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT,SAAS;AAAA,IACT;AAAA;AAAA,IAEA,yBAAyB;AAAA,EAC3B;AACF;AAEA,SAAS,sBAAsB;AAC7B,QAAM,OAAO,IAAI,CAAC,CAAC;AACnB,OAAK,MAAM,MAAM,CAAC,OAAO;AACvB,QAAI;AACF,WAAK,MAAM,KAAK,EAAE;AAAA,EACtB;AACA,iBAAe,MAAM;AACnB,SAAK,MAAM,SAAS;AAAA,EACtB,CAAC;AACD,SAAO;AACT;AAEA,SAAS,iBAAiB,UAAU,CAAC,GAAG;AACtC,QAAM;AAAA,IACJ,UAAAA,YAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU;AAAA,IACV,eAAe;AAAA,EACjB,IAAI;AACJ,WAASI,YAAW;AAClB,QAAI,IAAI;AACR,YAAQ,MAAM,KAAKJ,aAAY,OAAO,SAASA,UAAS,cAAc,QAAQ,MAAM,OAAO,SAAS,GAAG,aAAa,KAAK,MAAM,OAAO,KAAK;AAAA,EAC7I;AACA,QAAM,MAAM,IAAII,UAAS,CAAC;AAC1B,eAAa,MAAM,IAAI,QAAQA,UAAS,CAAC;AACzC,MAAI,WAAWJ,WAAU;AACvB;AAAA,MACEA,UAAS,cAAc,QAAQ;AAAA,MAC/B,MAAM,IAAI,QAAQI,UAAS;AAAA,MAC3B,EAAE,YAAY,KAAK;AAAA,IACrB;AAAA,EACF;AACA,SAAO,SAAS;AAAA,IACd,MAAM;AACJ,aAAO,IAAI;AAAA,IACb;AAAA,IACA,IAAI,GAAG;AACL,UAAI,IAAI;AACR,UAAI,QAAQ;AACZ,UAAI,CAACJ;AACH;AACF,UAAI,IAAI;AACN,SAAC,KAAKA,UAAS,cAAc,QAAQ,MAAM,OAAO,SAAS,GAAG,aAAa,OAAO,IAAI,KAAK;AAAA;AAE3F,SAAC,KAAKA,UAAS,cAAc,QAAQ,MAAM,OAAO,SAAS,GAAG,gBAAgB,KAAK;AAAA,IACvF;AAAA,EACF,CAAC;AACH;AAEA,SAAS,uBAAuB,WAAW;AACzC,MAAI;AACJ,QAAM,cAAc,KAAK,UAAU,eAAe,OAAO,KAAK;AAC9D,SAAO,MAAM,KAAK,EAAE,QAAQ,WAAW,GAAG,CAAC,GAAG,MAAM,UAAU,WAAW,CAAC,CAAC;AAC7E;AACA,SAAS,iBAAiB,UAAU,CAAC,GAAG;AACtC,QAAM;AAAA,IACJ,QAAAD,UAAS;AAAA,EACX,IAAI;AACJ,QAAM,YAAY,IAAI,IAAI;AAC1B,QAAM,OAAO,SAAS,MAAM;AAC1B,QAAI,IAAI;AACR,YAAQ,MAAM,KAAK,UAAU,UAAU,OAAO,SAAS,GAAG,SAAS,MAAM,OAAO,KAAK;AAAA,EACvF,CAAC;AACD,QAAM,SAAS,SAAS,MAAM,UAAU,QAAQ,uBAAuB,UAAU,KAAK,IAAI,CAAC,CAAC;AAC5F,QAAM,QAAQ,SAAS,MAAM,OAAO,MAAM,IAAI,CAAC,UAAU,MAAM,sBAAsB,CAAC,CAAC;AACvF,WAAS,oBAAoB;AAC3B,cAAU,QAAQ;AAClB,QAAIA;AACF,gBAAU,QAAQA,QAAO,aAAa;AAAA,EAC1C;AACA,MAAIA;AACF,qBAAiBA,QAAO,UAAU,mBAAmB,mBAAmB,EAAE,SAAS,KAAK,CAAC;AAC3F,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,yBAAyBA,UAAS,eAAe,IAAI;AAC5D,MAAIA,WAAU,OAAOA,QAAO,0BAA0B,YAAY;AAChE,IAAAA,QAAO,sBAAsB,EAAE;AAAA,EACjC,OAAO;AACL,OAAG;AAAA,EACL;AACF;AACA,SAAS,oBAAoB,UAAU,CAAC,GAAG;AACzC,MAAI,IAAI;AACR,QAAM,EAAE,QAAAA,UAAS,cAAc,IAAI;AACnC,QAAM,WAAWQ,OAAM,WAAW,OAAO,SAAS,QAAQ,OAAO;AACjE,QAAM,QAAQA,QAAO,KAAK,WAAW,OAAO,SAAS,QAAQ,UAAU,OAAO,KAAK,EAAE;AACrF,QAAM,aAAa,KAAK,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,KAAK;AACrF,QAAM,uBAAuB,WAAW,CAAC;AACzC,QAAM,mBAAmB,WAAW,CAAC;AACrC,WAAS,gBAAgB;AACvB,QAAI;AACJ,QAAI,CAAC,SAAS;AACZ;AACF,QAAI,SAAS;AACb,aAAS,MAAM,MAAM,SAAS,IAAI;AAClC,yBAAqB,SAAS,MAAM,SAAS,UAAU,OAAO,SAAS,IAAI;AAC3E,UAAM,eAAe,QAAQ,WAAW,OAAO,SAAS,QAAQ,WAAW;AAC3E,QAAI;AACF,mBAAa,MAAM,SAAS,IAAI,GAAG,qBAAqB,KAAK;AAAA;AAE7D,eAAS,GAAG,qBAAqB,KAAK;AACxC,aAAS,MAAM,MAAM,SAAS,IAAI;AAAA,EACpC;AACA,QAAM,CAAC,OAAO,QAAQ,GAAG,MAAM,SAAS,aAAa,GAAG,EAAE,WAAW,KAAK,CAAC;AAC3E,QAAM,sBAAsB,MAAM;AAChC,QAAI;AACJ,YAAQ,MAAM,WAAW,OAAO,SAAS,QAAQ,aAAa,OAAO,SAAS,IAAI,KAAK,OAAO;AAAA,EAChG,CAAC;AACD,oBAAkB,UAAU,CAAC,CAAC,EAAE,YAAY,CAAC,MAAM;AACjD,QAAI,iBAAiB,UAAU,YAAY;AACzC;AACF,6BAAyBR,SAAQ,MAAM;AACrC,uBAAiB,QAAQ,YAAY;AACrC,oBAAc;AAAA,IAChB,CAAC;AAAA,EACH,CAAC;AACD,MAAI,WAAW,OAAO,SAAS,QAAQ;AACrC,UAAM,QAAQ,OAAO,eAAe,EAAE,WAAW,MAAM,MAAM,KAAK,CAAC;AACrE,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,uBAAuB,QAAQ,UAAU,CAAC,GAAG;AACpD,QAAM,EAAE,WAAW,KAAK,WAAW,KAAK,IAAI;AAC5C,QAAM,SAAS,eAAe,UAAU,QAAQ;AAChD,QAAM,UAAU,cAAc,QAAQ,EAAE,GAAG,SAAS,aAAa,OAAO,CAAC;AACzE,SAAO;AAAA,IACL,GAAG;AAAA,EACL;AACF;AAEA,IAAM,gBAAgB;AAAA,EACpB,EAAE,KAAK,KAAK,OAAO,KAAK,MAAM,SAAS;AAAA,EACvC,EAAE,KAAK,OAAO,OAAO,KAAK,MAAM,SAAS;AAAA,EACzC,EAAE,KAAK,MAAM,OAAO,MAAM,MAAM,OAAO;AAAA,EACvC,EAAE,KAAK,QAAQ,OAAO,OAAO,MAAM,MAAM;AAAA,EACzC,EAAE,KAAK,SAAS,OAAO,QAAQ,MAAM,OAAO;AAAA,EAC5C,EAAE,KAAK,SAAS,OAAO,QAAQ,MAAM,QAAQ;AAAA,EAC7C,EAAE,KAAK,OAAO,mBAAmB,OAAO,SAAS,MAAM,OAAO;AAChE;AACA,IAAM,mBAAmB;AAAA,EACvB,SAAS;AAAA,EACT,MAAM,CAAC,MAAM,EAAE,MAAM,IAAI,IAAI,GAAG,CAAC,SAAS;AAAA,EAC1C,QAAQ,CAAC,MAAM,EAAE,MAAM,IAAI,IAAI,MAAM,CAAC,KAAK;AAAA,EAC3C,OAAO,CAAC,GAAG,SAAS,MAAM,IAAI,OAAO,eAAe,eAAe,GAAG,CAAC,SAAS,IAAI,IAAI,MAAM,EAAE;AAAA,EAChG,MAAM,CAAC,GAAG,SAAS,MAAM,IAAI,OAAO,cAAc,cAAc,GAAG,CAAC,QAAQ,IAAI,IAAI,MAAM,EAAE;AAAA,EAC5F,KAAK,CAAC,GAAG,SAAS,MAAM,IAAI,OAAO,cAAc,aAAa,GAAG,CAAC,OAAO,IAAI,IAAI,MAAM,EAAE;AAAA,EACzF,MAAM,CAAC,GAAG,SAAS,MAAM,IAAI,OAAO,cAAc,cAAc,GAAG,CAAC,QAAQ,IAAI,IAAI,MAAM,EAAE;AAAA,EAC5F,MAAM,CAAC,MAAM,GAAG,CAAC,QAAQ,IAAI,IAAI,MAAM,EAAE;AAAA,EACzC,QAAQ,CAAC,MAAM,GAAG,CAAC,UAAU,IAAI,IAAI,MAAM,EAAE;AAAA,EAC7C,QAAQ,CAAC,MAAM,GAAG,CAAC,UAAU,IAAI,IAAI,MAAM,EAAE;AAAA,EAC7C,SAAS;AACX;AACA,SAAS,kBAAkB,MAAM;AAC/B,SAAO,KAAK,YAAY,EAAE,MAAM,GAAG,EAAE;AACvC;AACA,SAAS,WAAW,MAAM,UAAU,CAAC,GAAG;AACtC,QAAM;AAAA,IACJ,UAAU,iBAAiB;AAAA,IAC3B,iBAAiB;AAAA,EACnB,IAAI;AACJ,QAAM,EAAE,KAAAa,MAAK,GAAG,SAAS,IAAI,OAAO,EAAE,UAAU,gBAAgB,UAAU,KAAK,CAAC;AAChF,QAAM,UAAU,SAAS,MAAM,cAAc,IAAI,KAAK,QAAQ,IAAI,CAAC,GAAG,SAAS,QAAQA,IAAG,CAAC,CAAC;AAC5F,MAAI,gBAAgB;AAClB,WAAO;AAAA,MACL;AAAA,MACA,GAAG;AAAA,IACL;AAAA,EACF,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,SAAS,cAAc,MAAM,UAAU,CAAC,GAAGA,OAAM,KAAK,IAAI,GAAG;AAC3D,MAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA,WAAW;AAAA,IACX,oBAAoB;AAAA,IACpB,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,WAAW;AAAA,EACb,IAAI;AACJ,QAAM,UAAU,OAAO,aAAa,WAAW,CAAC,MAAM,CAAC,EAAE,QAAQ,QAAQ,IAAI,KAAK,QAAQ;AAC1F,QAAM,OAAO,CAACA,OAAM,CAAC;AACrB,QAAM,UAAU,KAAK,IAAI,IAAI;AAC7B,WAASR,UAAS,OAAO,MAAM;AAC7B,WAAO,QAAQ,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK;AAAA,EAC7C;AACA,WAAS,OAAO,OAAO,MAAM;AAC3B,UAAM,MAAMA,UAAS,OAAO,IAAI;AAChC,UAAM,OAAO,QAAQ;AACrB,UAAM,MAAM,YAAY,KAAK,MAAM,KAAK,IAAI;AAC5C,WAAO,YAAY,OAAO,SAAS,UAAU,KAAK,IAAI;AAAA,EACxD;AACA,WAAS,YAAY,MAAM,KAAK,QAAQ;AACtC,UAAM,YAAY,SAAS,IAAI;AAC/B,QAAI,OAAO,cAAc;AACvB,aAAO,UAAU,KAAK,MAAM;AAC9B,WAAO,UAAU,QAAQ,OAAO,IAAI,SAAS,CAAC;AAAA,EAChD;AACA,MAAI,UAAU,OAAO,CAAC;AACpB,WAAO,SAAS;AAClB,MAAI,OAAO,QAAQ,YAAY,UAAU;AACvC,WAAO,kBAAkB,IAAI,KAAK,IAAI,CAAC;AACzC,MAAI,OAAO,QAAQ,UAAU;AAC3B,UAAM,WAAW,KAAK,MAAM,KAAK,CAAC,MAAM,EAAE,SAAS,GAAG,MAAM,OAAO,SAAS,GAAG;AAC/E,QAAI,WAAW,UAAU;AACvB,aAAO,kBAAkB,IAAI,KAAK,IAAI,CAAC;AAAA,EAC3C;AACA,aAAW,CAAC,KAAK,IAAI,KAAK,MAAM,QAAQ,GAAG;AACzC,UAAM,MAAMA,UAAS,MAAM,IAAI;AAC/B,QAAI,OAAO,KAAK,MAAM,MAAM,CAAC;AAC3B,aAAO,OAAO,MAAM,MAAM,MAAM,CAAC,CAAC;AACpC,QAAI,UAAU,KAAK;AACjB,aAAO,OAAO,MAAM,IAAI;AAAA,EAC5B;AACA,SAAO,SAAS;AAClB;AAEA,SAAS,eAAe,IAAI,UAAU,UAAU,CAAC,GAAG;AAClD,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,oBAAoB;AAAA,EACtB,IAAI;AACJ,QAAM,EAAE,MAAM,IAAI,aAAa,MAAM,UAAU,EAAE,UAAU,CAAC;AAC5D,QAAM,WAAW,WAAW,KAAK;AACjC,iBAAe,OAAO;AACpB,QAAI,CAAC,SAAS;AACZ;AACF,UAAM,GAAG;AACT,UAAM;AAAA,EACR;AACA,WAAS,SAAS;AAChB,QAAI,CAAC,SAAS,OAAO;AACnB,eAAS,QAAQ;AACjB,UAAI;AACF,WAAG;AACL,YAAM;AAAA,IACR;AAAA,EACF;AACA,WAAS,QAAQ;AACf,aAAS,QAAQ;AAAA,EACnB;AACA,MAAI,aAAa;AACf,WAAO;AACT,oBAAkB,KAAK;AACvB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,aAAa,UAAU,CAAC,GAAG;AAClC,QAAM;AAAA,IACJ,UAAU,iBAAiB;AAAA,IAC3B,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,WAAW;AAAA,IACX;AAAA,EACF,IAAI;AACJ,QAAM,KAAK,WAAW,UAAU,IAAI,MAAM;AAC1C,QAAM,SAAS,MAAM,GAAG,QAAQ,UAAU,IAAI;AAC9C,QAAM,KAAK,WAAW,MAAM;AAC1B,WAAO;AACP,aAAS,GAAG,KAAK;AAAA,EACnB,IAAI;AACJ,QAAM,WAAW,aAAa,0BAA0B,SAAS,IAAI,EAAE,UAAU,CAAC,IAAI,cAAc,IAAI,UAAU,EAAE,UAAU,CAAC;AAC/H,MAAI,gBAAgB;AAClB,WAAO;AAAA,MACL,WAAW;AAAA,MACX,GAAG;AAAA,IACL;AAAA,EACF,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,SAAS,SAAS,WAAW,MAAM,UAAU,CAAC,GAAG;AAC/C,MAAI,IAAI,IAAI;AACZ,QAAM;AAAA,IACJ,UAAAJ,YAAW;AAAA,IACX,mBAAmB,CAAC,MAAM;AAAA,EAC5B,IAAI;AACJ,QAAM,iBAAiB,KAAKA,aAAY,OAAO,SAASA,UAAS,UAAU,OAAO,KAAK;AACvF,QAAM,QAAQO,QAAO,KAAK,YAAY,OAAO,WAAWP,aAAY,OAAO,SAASA,UAAS,UAAU,OAAO,KAAK,IAAI;AACvH,QAAMe,cAAa,CAAC,EAAE,YAAY,OAAO,aAAa;AACtD,WAAS,OAAO,GAAG;AACjB,QAAI,EAAE,mBAAmB;AACvB,aAAO;AACT,UAAM,WAAW,QAAQ,iBAAiB;AAC1C,WAAO,OAAO,aAAa,aAAa,SAAS,CAAC,IAAI,QAAQ,QAAQ,EAAE,QAAQ,OAAO,CAAC;AAAA,EAC1F;AACA;AAAA,IACE;AAAA,IACA,CAAC,UAAU,aAAa;AACtB,UAAI,aAAa,YAAYf;AAC3B,QAAAA,UAAS,QAAQ,OAAO,YAAY,OAAO,WAAW,EAAE;AAAA,IAC5D;AAAA,IACA,EAAE,WAAW,KAAK;AAAA,EACpB;AACA,MAAI,QAAQ,WAAW,CAAC,QAAQ,iBAAiBA,aAAY,CAACe,aAAY;AACxE;AAAA,OACG,KAAKf,UAAS,SAAS,OAAO,SAAS,GAAG,cAAc,OAAO;AAAA,MAChE,MAAM;AACJ,YAAIA,aAAYA,UAAS,UAAU,MAAM;AACvC,gBAAM,QAAQ,OAAOA,UAAS,KAAK;AAAA,MACvC;AAAA,MACA,EAAE,WAAW,KAAK;AAAA,IACpB;AAAA,EACF;AACA,oBAAkB,MAAM;AACtB,QAAI,kBAAkB;AACpB,YAAM,gBAAgB,iBAAiB,eAAe,MAAM,SAAS,EAAE;AACvE,UAAI,iBAAiB,QAAQA;AAC3B,QAAAA,UAAS,QAAQ;AAAA,IACrB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,IAAM,qBAAqB;AAAA,EACzB,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC7B,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC9B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAChC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;AAAA,EAC5B,aAAa,CAAC,KAAK,GAAG,MAAM,CAAC;AAAA,EAC7B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAChC,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC9B,cAAc,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC/B,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EACjC,aAAa,CAAC,KAAK,GAAG,MAAM,CAAC;AAAA,EAC7B,cAAc,CAAC,MAAM,GAAG,KAAK,CAAC;AAAA,EAC9B,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EACjC,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC9B,cAAc,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC/B,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EACjC,YAAY,CAAC,KAAK,GAAG,MAAM,CAAC;AAAA,EAC5B,aAAa,CAAC,MAAM,GAAG,KAAK,CAAC;AAAA,EAC7B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAChC,YAAY,CAAC,MAAM,GAAG,GAAG,IAAI;AAAA,EAC7B,aAAa,CAAC,GAAG,MAAM,MAAM,CAAC;AAAA,EAC9B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAChC,YAAY,CAAC,MAAM,GAAG,MAAM,KAAK;AAAA,EACjC,aAAa,CAAC,MAAM,MAAM,MAAM,CAAC;AAAA,EACjC,eAAe,CAAC,MAAM,MAAM,MAAM,GAAG;AACvC;AACA,IAAM,oBAAoC,OAAO,OAAO,CAAC,GAAG,EAAE,QAAQ,SAAS,GAAG,kBAAkB;AACpG,SAAS,qBAAqB,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG;AAC9C,QAAM,IAAI,CAAC,IAAI,OAAO,IAAI,IAAI,KAAK,IAAI;AACvC,QAAM,IAAI,CAAC,IAAI,OAAO,IAAI,KAAK,IAAI;AACnC,QAAM,IAAI,CAAC,OAAO,IAAI;AACtB,QAAM,aAAa,CAAC,GAAG,IAAI,SAAS,EAAE,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,EAAE,KAAK,IAAI,EAAE,EAAE,KAAK;AAC9E,QAAM,WAAW,CAAC,GAAG,IAAI,OAAO,IAAI,EAAE,IAAI,EAAE,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,EAAE,IAAI,IAAI,EAAE,EAAE;AAChF,QAAM,WAAW,CAAC,MAAM;AACtB,QAAI,UAAU;AACd,aAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,YAAM,eAAe,SAAS,SAAS,IAAI,EAAE;AAC7C,UAAI,iBAAiB;AACnB,eAAO;AACT,YAAM,WAAW,WAAW,SAAS,IAAI,EAAE,IAAI;AAC/C,iBAAW,WAAW;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AACA,SAAO,CAAC,MAAM,OAAO,MAAM,OAAO,KAAK,IAAI,WAAW,SAAS,CAAC,GAAG,IAAI,EAAE;AAC3E;AACA,SAAS,KAAK,GAAG,GAAG,OAAO;AACzB,SAAO,IAAI,SAAS,IAAI;AAC1B;AACA,SAAS,MAAM,GAAG;AAChB,UAAQ,OAAO,MAAM,WAAW,CAAC,CAAC,IAAI,MAAM,CAAC;AAC/C;AACA,SAAS,kBAAkB,QAAQ,MAAM,IAAI,UAAU,CAAC,GAAG;AACzD,MAAI,IAAI;AACR,QAAM,UAAU,QAAQ,IAAI;AAC5B,QAAM,QAAQ,QAAQ,EAAE;AACxB,QAAM,KAAK,MAAM,OAAO;AACxB,QAAM,KAAK,MAAM,KAAK;AACtB,QAAM,YAAY,KAAK,QAAQ,QAAQ,QAAQ,MAAM,OAAO,KAAK;AACjE,QAAM,YAAY,KAAK,IAAI;AAC3B,QAAM,QAAQ,KAAK,IAAI,IAAI;AAC3B,QAAM,QAAQ,OAAO,QAAQ,eAAe,aAAa,QAAQ,cAAc,KAAK,QAAQ,QAAQ,UAAU,MAAM,OAAO,KAAK;AAChI,QAAM,OAAO,OAAO,UAAU,aAAa,QAAQ,qBAAqB,KAAK;AAC7E,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,WAAO,QAAQ;AACf,UAAM,OAAO,MAAM;AACjB,UAAI;AACJ,WAAK,MAAM,QAAQ,UAAU,OAAO,SAAS,IAAI,KAAK,OAAO,GAAG;AAC9D,gBAAQ;AACR;AAAA,MACF;AACA,YAAMY,OAAM,KAAK,IAAI;AACrB,YAAM,QAAQ,MAAMA,OAAM,aAAa,QAAQ;AAC/C,YAAM,MAAM,MAAM,OAAO,KAAK,EAAE,IAAI,CAAC,GAAG,MAAM,KAAK,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC;AACvE,UAAI,MAAM,QAAQ,OAAO,KAAK;AAC5B,eAAO,QAAQ,IAAI,IAAI,CAAC,GAAG,MAAM;AAC/B,cAAI,KAAK;AACT,iBAAO,MAAM,MAAM,GAAG,CAAC,MAAM,OAAO,MAAM,IAAI,MAAM,GAAG,CAAC,MAAM,OAAO,MAAM,GAAG,KAAK;AAAA,QACrF,CAAC;AAAA,eACM,OAAO,OAAO,UAAU;AAC/B,eAAO,QAAQ,IAAI,CAAC;AACtB,UAAIA,OAAM,OAAO;AACf,8BAAsB,IAAI;AAAA,MAC5B,OAAO;AACL,eAAO,QAAQ;AACf,gBAAQ;AAAA,MACV;AAAA,IACF;AACA,SAAK;AAAA,EACP,CAAC;AACH;AACA,SAAS,cAAc,QAAQ,UAAU,CAAC,GAAG;AAC3C,MAAI,YAAY;AAChB,QAAM,YAAY,MAAM;AACtB,UAAM,IAAI,QAAQ,MAAM;AACxB,WAAO,OAAO,MAAM,WAAW,IAAI,EAAE,IAAI,OAAO;AAAA,EAClD;AACA,QAAM,YAAY,IAAI,UAAU,CAAC;AACjC,QAAM,WAAW,OAAO,OAAO;AAC7B,QAAI,IAAI;AACR,QAAI,QAAQ,QAAQ,QAAQ;AAC1B;AACF,UAAM,KAAK,EAAE;AACb,QAAI,QAAQ;AACV,YAAM,eAAe,QAAQ,QAAQ,KAAK,CAAC;AAC7C,QAAI,OAAO;AACT;AACF,UAAM,QAAQ,MAAM,QAAQ,EAAE,IAAI,GAAG,IAAI,OAAO,IAAI,QAAQ,EAAE;AAC9D,KAAC,KAAK,QAAQ,cAAc,OAAO,SAAS,GAAG,KAAK,OAAO;AAC3D,UAAM,kBAAkB,WAAW,UAAU,OAAO,OAAO;AAAA,MACzD,GAAG;AAAA,MACH,OAAO,MAAM;AACX,YAAI;AACJ,eAAO,OAAO,eAAe,MAAM,QAAQ,UAAU,OAAO,SAAS,IAAI,KAAK,OAAO;AAAA,MACvF;AAAA,IACF,CAAC;AACD,KAAC,KAAK,QAAQ,eAAe,OAAO,SAAS,GAAG,KAAK,OAAO;AAAA,EAC9D,GAAG,EAAE,MAAM,KAAK,CAAC;AACjB,QAAM,MAAM,QAAQ,QAAQ,QAAQ,GAAG,CAAC,aAAa;AACnD,QAAI,UAAU;AACZ;AACA,gBAAU,QAAQ,UAAU;AAAA,IAC9B;AAAA,EACF,CAAC;AACD,oBAAkB,MAAM;AACtB;AAAA,EACF,CAAC;AACD,SAAO,SAAS,MAAM,QAAQ,QAAQ,QAAQ,IAAI,UAAU,IAAI,UAAU,KAAK;AACjF;AAEA,SAAS,mBAAmB,OAAO,WAAW,UAAU,CAAC,GAAG;AAC1D,QAAM;AAAA,IACJ,eAAe,CAAC;AAAA,IAChB,sBAAsB;AAAA,IACtB,oBAAoB;AAAA,IACpB,OAAO,cAAc;AAAA,IACrB,YAAY;AAAA,IACZ,QAAAb,UAAS;AAAA,EACX,IAAI;AACJ,MAAI,CAACA;AACH,WAAO,SAAS,YAAY;AAC9B,QAAM,QAAQ,SAAS,CAAC,CAAC;AACzB,WAAS,eAAe;AACtB,QAAI,SAAS,WAAW;AACtB,aAAOA,QAAO,SAAS,UAAU;AAAA,IACnC,WAAW,SAAS,QAAQ;AAC1B,YAAM,OAAOA,QAAO,SAAS,QAAQ;AACrC,YAAM,QAAQ,KAAK,QAAQ,GAAG;AAC9B,aAAO,QAAQ,IAAI,KAAK,MAAM,KAAK,IAAI;AAAA,IACzC,OAAO;AACL,cAAQA,QAAO,SAAS,QAAQ,IAAI,QAAQ,MAAM,EAAE;AAAA,IACtD;AAAA,EACF;AACA,WAAS,eAAe,QAAQ;AAC9B,UAAM,cAAc,OAAO,SAAS;AACpC,QAAI,SAAS;AACX,aAAO,GAAG,cAAc,IAAI,WAAW,KAAK,EAAE,GAAGA,QAAO,SAAS,QAAQ,EAAE;AAC7E,QAAI,SAAS;AACX,aAAO,GAAGA,QAAO,SAAS,UAAU,EAAE,GAAG,cAAc,IAAI,WAAW,KAAK,EAAE;AAC/E,UAAM,OAAOA,QAAO,SAAS,QAAQ;AACrC,UAAM,QAAQ,KAAK,QAAQ,GAAG;AAC9B,QAAI,QAAQ;AACV,aAAO,GAAGA,QAAO,SAAS,UAAU,EAAE,GAAG,KAAK,MAAM,GAAG,KAAK,CAAC,GAAG,cAAc,IAAI,WAAW,KAAK,EAAE;AACtG,WAAO,GAAGA,QAAO,SAAS,UAAU,EAAE,GAAG,IAAI,GAAG,cAAc,IAAI,WAAW,KAAK,EAAE;AAAA,EACtF;AACA,WAAS,OAAO;AACd,WAAO,IAAI,gBAAgB,aAAa,CAAC;AAAA,EAC3C;AACA,WAAS,YAAY,QAAQ;AAC3B,UAAM,aAAa,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC;AAC7C,eAAW,OAAO,OAAO,KAAK,GAAG;AAC/B,YAAM,eAAe,OAAO,OAAO,GAAG;AACtC,YAAM,GAAG,IAAI,aAAa,SAAS,IAAI,eAAe,OAAO,IAAI,GAAG,KAAK;AACzE,iBAAW,OAAO,GAAG;AAAA,IACvB;AACA,UAAM,KAAK,UAAU,EAAE,QAAQ,CAAC,QAAQ,OAAO,MAAM,GAAG,CAAC;AAAA,EAC3D;AACA,QAAM,EAAE,OAAO,OAAO,IAAI;AAAA,IACxB;AAAA,IACA,MAAM;AACJ,YAAM,SAAS,IAAI,gBAAgB,EAAE;AACrC,aAAO,KAAK,KAAK,EAAE,QAAQ,CAAC,QAAQ;AAClC,cAAM,WAAW,MAAM,GAAG;AAC1B,YAAI,MAAM,QAAQ,QAAQ;AACxB,mBAAS,QAAQ,CAAC,UAAU,OAAO,OAAO,KAAK,KAAK,CAAC;AAAA,iBAC9C,uBAAuB,YAAY;AAC1C,iBAAO,OAAO,GAAG;AAAA,iBACV,qBAAqB,CAAC;AAC7B,iBAAO,OAAO,GAAG;AAAA;AAEjB,iBAAO,IAAI,KAAK,QAAQ;AAAA,MAC5B,CAAC;AACD,YAAM,QAAQ,KAAK;AAAA,IACrB;AAAA,IACA,EAAE,MAAM,KAAK;AAAA,EACf;AACA,WAAS,MAAM,QAAQ,cAAc;AACnC,UAAM;AACN,QAAI;AACF,kBAAY,MAAM;AACpB,QAAI,cAAc,WAAW;AAC3B,MAAAA,QAAO,QAAQ;AAAA,QACbA,QAAO,QAAQ;AAAA,QACfA,QAAO,SAAS;AAAA,QAChBA,QAAO,SAAS,WAAW,eAAe,MAAM;AAAA,MAClD;AAAA,IACF,OAAO;AACL,MAAAA,QAAO,QAAQ;AAAA,QACbA,QAAO,QAAQ;AAAA,QACfA,QAAO,SAAS;AAAA,QAChBA,QAAO,SAAS,WAAW,eAAe,MAAM;AAAA,MAClD;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,WAAS,YAAY;AACnB,QAAI,CAAC;AACH;AACF,UAAM,KAAK,GAAG,IAAI;AAAA,EACpB;AACA,QAAM,kBAAkB,EAAE,SAAS,KAAK;AACxC,mBAAiBA,SAAQ,YAAY,WAAW,eAAe;AAC/D,MAAI,SAAS;AACX,qBAAiBA,SAAQ,cAAc,WAAW,eAAe;AACnE,QAAM,UAAU,KAAK;AACrB,MAAI,QAAQ,KAAK,EAAE,KAAK,EAAE;AACxB,gBAAY,OAAO;AAAA;AAEnB,WAAO,OAAO,OAAO,YAAY;AACnC,SAAO;AACT;AAEA,SAAS,aAAa,UAAU,CAAC,GAAG;AAClC,MAAI,IAAI;AACR,QAAM,UAAU,YAAY,KAAK,QAAQ,YAAY,OAAO,KAAK,KAAK;AACtE,QAAM,aAAa,YAAY,KAAK,QAAQ,eAAe,OAAO,KAAK,IAAI;AAC3E,QAAM,cAAc,IAAI,QAAQ,WAAW;AAC3C,QAAM,EAAE,WAAAG,aAAY,iBAAiB,IAAI;AACzC,QAAM,cAAc,aAAa,MAAM;AACrC,QAAI;AACJ,YAAQ,MAAMA,cAAa,OAAO,SAASA,WAAU,iBAAiB,OAAO,SAAS,IAAI;AAAA,EAC5F,CAAC;AACD,QAAM,SAAS,WAAW;AAC1B,WAAS,iBAAiB,MAAM;AAC9B,YAAQ,MAAM;AAAA,MACZ,KAAK,SAAS;AACZ,YAAI,YAAY;AACd,iBAAO,YAAY,MAAM,SAAS;AACpC;AAAA,MACF;AAAA,MACA,KAAK,SAAS;AACZ,YAAI,YAAY;AACd,iBAAO,YAAY,MAAM,SAAS;AACpC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,iBAAe,SAAS;AACtB,QAAI,CAAC,YAAY,SAAS,OAAO;AAC/B;AACF,WAAO,QAAQ,MAAMA,WAAU,aAAa,aAAa;AAAA,MACvD,OAAO,iBAAiB,OAAO;AAAA,MAC/B,OAAO,iBAAiB,OAAO;AAAA,IACjC,CAAC;AACD,WAAO,OAAO;AAAA,EAChB;AACA,WAAS,QAAQ;AACf,QAAI;AACJ,KAAC,MAAM,OAAO,UAAU,OAAO,SAAS,IAAI,UAAU,EAAE,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC;AAC/E,WAAO,QAAQ;AAAA,EACjB;AACA,WAAS,OAAO;AACd,UAAM;AACN,YAAQ,QAAQ;AAAA,EAClB;AACA,iBAAe,QAAQ;AACrB,UAAM,OAAO;AACb,QAAI,OAAO;AACT,cAAQ,QAAQ;AAClB,WAAO,OAAO;AAAA,EAChB;AACA,iBAAe,UAAU;AACvB,UAAM;AACN,WAAO,MAAM,MAAM;AAAA,EACrB;AACA;AAAA,IACE;AAAA,IACA,CAAC,MAAM;AACL,UAAI;AACF,eAAO;AAAA,UACJ,OAAM;AAAA,IACb;AAAA,IACA,EAAE,WAAW,KAAK;AAAA,EACpB;AACA;AAAA,IACE;AAAA,IACA,MAAM;AACJ,UAAI,WAAW,SAAS,OAAO;AAC7B,gBAAQ;AAAA,IACZ;AAAA,IACA,EAAE,WAAW,KAAK;AAAA,EACpB;AACA,oBAAkB,MAAM;AACtB,SAAK;AAAA,EACP,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,UAAU,OAAO,KAAK,MAAM,UAAU,CAAC,GAAG;AACjD,MAAI,IAAI,IAAI;AACZ,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,UAAU;AAAA,IACV;AAAA,IACA,OAAO;AAAA,IACP;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,KAAK,mBAAmB;AAC9B,QAAM,QAAQ,SAAS,MAAM,OAAO,SAAS,GAAG,WAAW,KAAK,MAAM,OAAO,SAAS,GAAG,UAAU,OAAO,SAAS,GAAG,KAAK,EAAE,QAAQ,MAAM,KAAK,MAAM,OAAO,SAAS,GAAG,UAAU,OAAO,SAAS,GAAG,UAAU,OAAO,SAAS,GAAG,KAAK,MAAM,OAAO,SAAS,GAAG,KAAK;AACtQ,MAAI,QAAQ;AACZ,MAAI,CAAC,KAAK;AACR,UAAM;AAAA,EACR;AACA,UAAQ,SAAS,UAAU,IAAI,SAAS,CAAC;AACzC,QAAM,UAAU,CAAC,QAAQ,CAAC,QAAQ,MAAM,OAAO,UAAU,aAAa,MAAM,GAAG,IAAI,YAAY,GAAG;AAClG,QAAME,YAAW,MAAM,MAAM,MAAM,GAAG,CAAC,IAAI,QAAQ,MAAM,GAAG,CAAC,IAAI;AACjE,QAAM,cAAc,CAAC,UAAU;AAC7B,QAAI,YAAY;AACd,UAAI,WAAW,KAAK;AAClB,cAAM,OAAO,KAAK;AAAA,IACtB,OAAO;AACL,YAAM,OAAO,KAAK;AAAA,IACpB;AAAA,EACF;AACA,MAAI,SAAS;AACX,UAAM,eAAeA,UAAS;AAC9B,UAAM,QAAQ,IAAI,YAAY;AAC9B,QAAI,aAAa;AACjB;AAAA,MACE,MAAM,MAAM,GAAG;AAAA,MACf,CAAC,MAAM;AACL,YAAI,CAAC,YAAY;AACf,uBAAa;AACb,gBAAM,QAAQ,QAAQ,CAAC;AACvB,mBAAS,MAAM,aAAa,KAAK;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AACA;AAAA,MACE;AAAA,MACA,CAAC,MAAM;AACL,YAAI,CAAC,eAAe,MAAM,MAAM,GAAG,KAAK;AACtC,sBAAY,CAAC;AAAA,MACjB;AAAA,MACA,EAAE,KAAK;AAAA,IACT;AACA,WAAO;AAAA,EACT,OAAO;AACL,WAAO,SAAS;AAAA,MACd,MAAM;AACJ,eAAOA,UAAS;AAAA,MAClB;AAAA,MACA,IAAI,OAAO;AACT,oBAAY,KAAK;AAAA,MACnB;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEA,SAAS,WAAW,OAAO,MAAM,UAAU,CAAC,GAAG;AAC7C,QAAM,MAAM,CAAC;AACb,aAAW,OAAO,OAAO;AACvB,QAAI,GAAG,IAAI;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,WAAW,SAAS;AAC3B,QAAM;AAAA,IACJ,UAAU,CAAC;AAAA,IACX,WAAW;AAAA,IACX,WAAAF,aAAY;AAAA,EACd,IAAI,WAAW,CAAC;AAChB,QAAM,cAAc,aAAa,MAAM,OAAOA,eAAc,eAAe,aAAaA,UAAS;AACjG,QAAM,aAAaK,OAAM,OAAO;AAChC,MAAI;AACJ,QAAM,UAAU,CAAC,WAAW,WAAW,UAAU;AAC/C,QAAI,YAAY;AACd,MAAAL,WAAU,QAAQ,QAAQ;AAAA,EAC9B;AACA,QAAM,OAAO,MAAM;AACjB,QAAI,YAAY;AACd,MAAAA,WAAU,QAAQ,CAAC;AACrB,wBAAoB,OAAO,SAAS,iBAAiB,MAAM;AAAA,EAC7D;AACA,MAAI,WAAW,GAAG;AAChB,uBAAmB;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,QACE,WAAW;AAAA,QACX,mBAAmB;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,eAAe,MAAM,SAAS;AACrC,QAAM,EAAE,gBAAgB,cAAc,UAAU,gBAAgB,aAAa,aAAa,IAAI,gBAAgB,UAAU,uBAAuB,SAAS,IAAI,IAAI,yBAAyB,SAAS,IAAI;AACtM,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,gBAAgB;AAAA,MACd,KAAK;AAAA,MACL,UAAU,MAAM;AACd,uBAAe;AAAA,MACjB;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,wBAAwB,MAAM;AACrC,QAAM,eAAe,WAAW,IAAI;AACpC,QAAM,OAAO,eAAe,YAAY;AACxC,QAAM,cAAc,IAAI,CAAC,CAAC;AAC1B,QAAM,SAAS,WAAW,IAAI;AAC9B,QAAM,QAAQ,IAAI,EAAE,OAAO,GAAG,KAAK,GAAG,CAAC;AACvC,SAAO,EAAE,OAAO,QAAQ,aAAa,MAAM,aAAa;AAC1D;AACA,SAAS,sBAAsB,OAAO,QAAQ,UAAU;AACtD,SAAO,CAAC,kBAAkB;AACxB,QAAI,OAAO,aAAa;AACtB,aAAO,KAAK,KAAK,gBAAgB,QAAQ;AAC3C,UAAM,EAAE,QAAQ,EAAE,IAAI,MAAM;AAC5B,QAAI,MAAM;AACV,QAAI,WAAW;AACf,aAAS,IAAI,OAAO,IAAI,OAAO,MAAM,QAAQ,KAAK;AAChD,YAAM,OAAO,SAAS,CAAC;AACvB,aAAO;AACP,iBAAW;AACX,UAAI,MAAM;AACR;AAAA,IACJ;AACA,WAAO,WAAW;AAAA,EACpB;AACF;AACA,SAAS,gBAAgB,QAAQ,UAAU;AACzC,SAAO,CAAC,oBAAoB;AAC1B,QAAI,OAAO,aAAa;AACtB,aAAO,KAAK,MAAM,kBAAkB,QAAQ,IAAI;AAClD,QAAI,MAAM;AACV,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,OAAO,MAAM,QAAQ,KAAK;AAC5C,YAAM,OAAO,SAAS,CAAC;AACvB,aAAO;AACP,UAAI,OAAO,iBAAiB;AAC1B,iBAAS;AACT;AAAA,MACF;AAAA,IACF;AACA,WAAO,SAAS;AAAA,EAClB;AACF;AACA,SAAS,qBAAqB,MAAM,UAAU,WAAW,iBAAiB,EAAE,cAAc,OAAO,aAAa,OAAO,GAAG;AACtH,SAAO,MAAM;AACX,UAAM,UAAU,aAAa;AAC7B,QAAI,SAAS;AACX,YAAM,SAAS,UAAU,SAAS,aAAa,QAAQ,YAAY,QAAQ,UAAU;AACrF,YAAM,eAAe,gBAAgB,SAAS,aAAa,QAAQ,eAAe,QAAQ,WAAW;AACrG,YAAM,OAAO,SAAS;AACtB,YAAM,KAAK,SAAS,eAAe;AACnC,YAAM,QAAQ;AAAA,QACZ,OAAO,OAAO,IAAI,IAAI;AAAA,QACtB,KAAK,KAAK,OAAO,MAAM,SAAS,OAAO,MAAM,SAAS;AAAA,MACxD;AACA,kBAAY,QAAQ,OAAO,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,GAAG,EAAE,IAAI,CAAC,KAAK,WAAW;AAAA,QAC9F,MAAM;AAAA,QACN,OAAO,QAAQ,MAAM,MAAM;AAAA,MAC7B,EAAE;AAAA,IACJ;AAAA,EACF;AACF;AACA,SAAS,kBAAkB,UAAU,QAAQ;AAC3C,SAAO,CAAC,UAAU;AAChB,QAAI,OAAO,aAAa,UAAU;AAChC,YAAM,QAAQ,QAAQ;AACtB,aAAO;AAAA,IACT;AACA,UAAM,OAAO,OAAO,MAAM,MAAM,GAAG,KAAK,EAAE,OAAO,CAAC,KAAK,GAAG,MAAM,MAAM,SAAS,CAAC,GAAG,CAAC;AACpF,WAAO;AAAA,EACT;AACF;AACA,SAAS,iBAAiB,MAAM,MAAM,cAAc,gBAAgB;AAClE,QAAM,CAAC,KAAK,OAAO,KAAK,QAAQ,MAAM,YAAY,GAAG,MAAM;AACzD,mBAAe;AAAA,EACjB,CAAC;AACH;AACA,SAAS,wBAAwB,UAAU,QAAQ;AACjD,SAAO,SAAS,MAAM;AACpB,QAAI,OAAO,aAAa;AACtB,aAAO,OAAO,MAAM,SAAS;AAC/B,WAAO,OAAO,MAAM,OAAO,CAAC,KAAK,GAAG,UAAU,MAAM,SAAS,KAAK,GAAG,CAAC;AAAA,EACxE,CAAC;AACH;AACA,IAAM,wCAAwC;AAAA,EAC5C,YAAY;AAAA,EACZ,UAAU;AACZ;AACA,SAAS,eAAe,MAAM,gBAAgB,aAAa,cAAc;AACvE,SAAO,CAAC,UAAU;AAChB,QAAI,aAAa,OAAO;AACtB,mBAAa,MAAM,sCAAsC,IAAI,CAAC,IAAI,YAAY,KAAK;AACnF,qBAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,SAAS,yBAAyB,SAAS,MAAM;AAC/C,QAAM,YAAY,wBAAwB,IAAI;AAC9C,QAAM,EAAE,OAAO,QAAQ,aAAa,MAAM,aAAa,IAAI;AAC3D,QAAM,iBAAiB,EAAE,WAAW,OAAO;AAC3C,QAAM,EAAE,WAAW,WAAW,EAAE,IAAI;AACpC,QAAM,kBAAkB,sBAAsB,OAAO,QAAQ,SAAS;AACtE,QAAM,YAAY,gBAAgB,QAAQ,SAAS;AACnD,QAAM,iBAAiB,qBAAqB,cAAc,UAAU,WAAW,iBAAiB,SAAS;AACzG,QAAM,kBAAkB,kBAAkB,WAAW,MAAM;AAC3D,QAAM,aAAa,SAAS,MAAM,gBAAgB,MAAM,MAAM,KAAK,CAAC;AACpE,QAAM,aAAa,wBAAwB,WAAW,MAAM;AAC5D,mBAAiB,MAAM,MAAM,cAAc,cAAc;AACzD,QAAM,WAAW,eAAe,cAAc,gBAAgB,iBAAiB,YAAY;AAC3F,QAAM,eAAe,SAAS,MAAM;AAClC,WAAO;AAAA,MACL,OAAO;AAAA,QACL,QAAQ;AAAA,QACR,OAAO,GAAG,WAAW,QAAQ,WAAW,KAAK;AAAA,QAC7C,YAAY,GAAG,WAAW,KAAK;AAAA,QAC/B,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,uBAAuB,SAAS,MAAM;AAC7C,QAAM,YAAY,wBAAwB,IAAI;AAC9C,QAAM,EAAE,OAAO,QAAQ,aAAa,MAAM,aAAa,IAAI;AAC3D,QAAM,iBAAiB,EAAE,WAAW,OAAO;AAC3C,QAAM,EAAE,YAAY,WAAW,EAAE,IAAI;AACrC,QAAM,kBAAkB,sBAAsB,OAAO,QAAQ,UAAU;AACvE,QAAM,YAAY,gBAAgB,QAAQ,UAAU;AACpD,QAAM,iBAAiB,qBAAqB,YAAY,UAAU,WAAW,iBAAiB,SAAS;AACvG,QAAM,iBAAiB,kBAAkB,YAAY,MAAM;AAC3D,QAAM,YAAY,SAAS,MAAM,eAAe,MAAM,MAAM,KAAK,CAAC;AAClE,QAAM,cAAc,wBAAwB,YAAY,MAAM;AAC9D,mBAAiB,MAAM,MAAM,cAAc,cAAc;AACzD,QAAM,WAAW,eAAe,YAAY,gBAAgB,gBAAgB,YAAY;AACxF,QAAM,eAAe,SAAS,MAAM;AAClC,WAAO;AAAA,MACL,OAAO;AAAA,QACL,OAAO;AAAA,QACP,QAAQ,GAAG,YAAY,QAAQ,UAAU,KAAK;AAAA,QAC9C,WAAW,GAAG,UAAU,KAAK;AAAA,MAC/B;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,YAAY,UAAU,CAAC,GAAG;AACjC,QAAM;AAAA,IACJ,WAAAA,aAAY;AAAA,IACZ,UAAAF,YAAW;AAAA,EACb,IAAI;AACJ,QAAM,gBAAgB,WAAW,KAAK;AACtC,QAAM,WAAW,WAAW,IAAI;AAChC,QAAM,qBAAqB,sBAAsB,EAAE,UAAAA,UAAS,CAAC;AAC7D,QAAM,cAAc,aAAa,MAAME,cAAa,cAAcA,UAAS;AAC3E,QAAM,WAAW,SAAS,MAAM,CAAC,CAAC,SAAS,SAAS,mBAAmB,UAAU,SAAS;AAC1F,MAAI,YAAY,OAAO;AACrB,qBAAiB,UAAU,WAAW,MAAM;AAC1C,UAAI,IAAI;AACR,oBAAc,SAAS,MAAM,KAAK,SAAS,UAAU,OAAO,SAAS,GAAG,SAAS,OAAO,KAAK;AAAA,IAC/F,GAAG,EAAE,SAAS,KAAK,CAAC;AACpB;AAAA,MACE,MAAM,mBAAmB,UAAU,cAAcF,aAAY,OAAO,SAASA,UAAS,qBAAqB,aAAa,cAAc;AAAA,MACtI,CAAC,SAAS;AACR,sBAAc,QAAQ;AACtB,qBAAa,IAAI;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AACA,iBAAe,aAAa,MAAM;AAChC,QAAI;AACJ,YAAQ,KAAK,SAAS,UAAU,OAAO,SAAS,GAAG,QAAQ;AAC3D,aAAS,QAAQ,YAAY,QAAQ,MAAME,WAAU,SAAS,QAAQ,IAAI,IAAI;AAAA,EAChF;AACA,iBAAe,QAAQ,MAAM;AAC3B,QAAI,mBAAmB,UAAU;AAC/B,YAAM,aAAa,IAAI;AAAA;AAEvB,oBAAc,QAAQ;AAAA,EAC1B;AACA,iBAAe,UAAU;AACvB,kBAAc,QAAQ;AACtB,UAAM,IAAI,SAAS;AACnB,aAAS,QAAQ;AACjB,WAAO,KAAK,OAAO,SAAS,EAAE,QAAQ;AAAA,EACxC;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,mBAAmB,UAAU,CAAC,GAAG;AACxC,QAAM;AAAA,IACJ,QAAAH,UAAS;AAAA,IACT,oBAAoB,yBAAyB;AAAA,EAC/C,IAAI;AACJ,QAAM,gCAAgC;AACtC,QAAM,cAAc,aAAa,MAAM;AACrC,QAAI,CAACA,WAAU,EAAE,kBAAkBA;AACjC,aAAO;AACT,QAAI,aAAa,eAAe;AAC9B,aAAO;AACT,QAAI;AACF,YAAM,gBAAgB,IAAI,aAAa,EAAE;AACzC,oBAAc,SAAS,MAAM;AAC3B,sBAAc,MAAM;AAAA,MACtB;AAAA,IACF,SAAS,GAAG;AACV,UAAI,EAAE,SAAS;AACb,eAAO;AAAA,IACX;AACA,WAAO;AAAA,EACT,CAAC;AACD,QAAM,oBAAoB,WAAW,YAAY,SAAS,gBAAgB,gBAAgB,aAAa,eAAe,SAAS;AAC/H,QAAM,eAAe,IAAI,IAAI;AAC7B,QAAM,oBAAoB,YAAY;AACpC,QAAI,CAAC,YAAY;AACf;AACF,QAAI,CAAC,kBAAkB,SAAS,aAAa,eAAe,UAAU;AACpE,YAAM,SAAS,MAAM,aAAa,kBAAkB;AACpD,UAAI,WAAW;AACb,0BAAkB,QAAQ;AAAA,IAC9B;AACA,WAAO,kBAAkB;AAAA,EAC3B;AACA,QAAM,EAAE,IAAI,SAAS,SAAS,aAAa,IAAI,gBAAgB;AAC/D,QAAM,EAAE,IAAI,QAAQ,SAAS,YAAY,IAAI,gBAAgB;AAC7D,QAAM,EAAE,IAAI,SAAS,SAAS,aAAa,IAAI,gBAAgB;AAC/D,QAAM,EAAE,IAAI,SAAS,SAAS,aAAa,IAAI,gBAAgB;AAC/D,QAAM,OAAO,OAAO,cAAc;AAChC,QAAI,CAAC,YAAY,SAAS,CAAC,kBAAkB;AAC3C;AACF,UAAM,WAAW,OAAO,OAAO,CAAC,GAAG,+BAA+B,SAAS;AAC3E,iBAAa,QAAQ,IAAI,aAAa,SAAS,SAAS,IAAI,QAAQ;AACpE,iBAAa,MAAM,UAAU;AAC7B,iBAAa,MAAM,SAAS;AAC5B,iBAAa,MAAM,UAAU;AAC7B,iBAAa,MAAM,UAAU;AAC7B,WAAO,aAAa;AAAA,EACtB;AACA,QAAM,QAAQ,MAAM;AAClB,QAAI,aAAa;AACf,mBAAa,MAAM,MAAM;AAC3B,iBAAa,QAAQ;AAAA,EACvB;AACA,MAAI;AACF,iBAAa,iBAAiB;AAChC,oBAAkB,KAAK;AACvB,MAAI,YAAY,SAASA,SAAQ;AAC/B,UAAMC,YAAWD,QAAO;AACxB,qBAAiBC,WAAU,oBAAoB,CAAC,MAAM;AACpD,QAAE,eAAe;AACjB,UAAIA,UAAS,oBAAoB,WAAW;AAC1C,cAAM;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,uBAAuB;AAC7B,SAAS,qBAAqB,SAAS;AACrC,MAAI,YAAY;AACd,WAAO,CAAC;AACV,SAAO;AACT;AACA,SAAS,aAAa,KAAK,UAAU,CAAC,GAAG;AACvC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,YAAY,CAAC;AAAA,EACf,IAAI;AACJ,QAAM,OAAO,IAAI,IAAI;AACrB,QAAM,SAAS,WAAW,QAAQ;AAClC,QAAM,QAAQ,IAAI;AAClB,QAAM,SAASO,OAAM,GAAG;AACxB,MAAI;AACJ,MAAI;AACJ,MAAI,mBAAmB;AACvB,MAAI,UAAU;AACd,MAAI,eAAe,CAAC;AACpB,MAAI;AACJ,MAAI;AACJ,QAAM,cAAc,MAAM;AACxB,QAAI,aAAa,UAAU,MAAM,SAAS,OAAO,UAAU,QAAQ;AACjE,iBAAW,UAAU;AACnB,cAAM,MAAM,KAAK,MAAM;AACzB,qBAAe,CAAC;AAAA,IAClB;AAAA,EACF;AACA,QAAM,aAAa,MAAM;AACvB,QAAI,gBAAgB,MAAM;AACxB,mBAAa,YAAY;AACzB,qBAAe;AAAA,IACjB;AAAA,EACF;AACA,QAAM,iBAAiB,MAAM;AAC3B,iBAAa,eAAe;AAC5B,sBAAkB;AAAA,EACpB;AACA,QAAM,QAAQ,CAAC,OAAO,KAAK,WAAW;AACpC,eAAW;AACX,QAAI,CAAC,YAAY,CAAC,YAAY,CAAC,MAAM;AACnC;AACF,uBAAmB;AACnB,mBAAe;AACf,sBAAkB,OAAO,SAAS,eAAe;AACjD,UAAM,MAAM,MAAM,MAAM,MAAM;AAC9B,UAAM,QAAQ;AAAA,EAChB;AACA,QAAM,OAAO,CAAC,OAAO,YAAY,SAAS;AACxC,QAAI,CAAC,MAAM,SAAS,OAAO,UAAU,QAAQ;AAC3C,UAAI;AACF,qBAAa,KAAK,KAAK;AACzB,aAAO;AAAA,IACT;AACA,gBAAY;AACZ,UAAM,MAAM,KAAK,KAAK;AACtB,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,MAAM;AAClB,QAAI,oBAAoB,OAAO,OAAO,UAAU;AAC9C;AACF,UAAM,KAAK,IAAI,UAAU,OAAO,OAAO,SAAS;AAChD,UAAM,QAAQ;AACd,WAAO,QAAQ;AACf,OAAG,SAAS,MAAM;AAChB,aAAO,QAAQ;AACf,gBAAU;AACV,qBAAe,OAAO,SAAS,YAAY,EAAE;AAC7C,yBAAmB,OAAO,SAAS,gBAAgB;AACnD,kBAAY;AAAA,IACd;AACA,OAAG,UAAU,CAAC,OAAO;AACnB,aAAO,QAAQ;AACf,qBAAe;AACf,wBAAkB,OAAO,SAAS,eAAe;AACjD,wBAAkB,OAAO,SAAS,eAAe,IAAI,EAAE;AACvD,UAAI,CAAC,oBAAoB,QAAQ,kBAAkB,MAAM,SAAS,QAAQ,OAAO,MAAM,QAAQ;AAC7F,cAAM;AAAA,UACJ,UAAU;AAAA,UACV,QAAQ;AAAA,UACR;AAAA,QACF,IAAI,qBAAqB,QAAQ,aAAa;AAC9C,cAAM,eAAe,OAAO,YAAY,aAAa,UAAU,MAAM,OAAO,YAAY,aAAa,UAAU,KAAK,UAAU;AAC9H,YAAI,aAAa,OAAO,GAAG;AACzB,qBAAW;AACX,yBAAe,WAAW,OAAO,KAAK;AAAA,QACxC,OAAO;AACL,sBAAY,OAAO,SAAS,SAAS;AAAA,QACvC;AAAA,MACF;AAAA,IACF;AACA,OAAG,UAAU,CAAC,MAAM;AAClB,iBAAW,OAAO,SAAS,QAAQ,IAAI,CAAC;AAAA,IAC1C;AACA,OAAG,YAAY,CAAC,MAAM;AACpB,UAAI,QAAQ,WAAW;AACrB,uBAAe;AACf,cAAM;AAAA,UACJ,UAAU;AAAA,UACV,kBAAkB;AAAA,QACpB,IAAI,qBAAqB,QAAQ,SAAS;AAC1C,YAAI,EAAE,SAAS,QAAQ,eAAe;AACpC;AAAA,MACJ;AACA,WAAK,QAAQ,EAAE;AACf,mBAAa,OAAO,SAAS,UAAU,IAAI,CAAC;AAAA,IAC9C;AAAA,EACF;AACA,MAAI,QAAQ,WAAW;AACrB,UAAM;AAAA,MACJ,UAAU;AAAA,MACV,WAAW;AAAA,MACX,cAAc;AAAA,IAChB,IAAI,qBAAqB,QAAQ,SAAS;AAC1C,UAAM,EAAE,OAAO,OAAO,IAAI;AAAA,MACxB,MAAM;AACJ,aAAK,QAAQ,OAAO,GAAG,KAAK;AAC5B,YAAI,mBAAmB;AACrB;AACF,0BAAkB,WAAW,MAAM;AACjC,gBAAM;AACN,6BAAmB;AAAA,QACrB,GAAG,WAAW;AAAA,MAChB;AAAA,MACA;AAAA,MACA,EAAE,WAAW,MAAM;AAAA,IACrB;AACA,qBAAiB;AACjB,sBAAkB;AAAA,EACpB;AACA,MAAI,WAAW;AACb,QAAI;AACF,uBAAiB,gBAAgB,MAAM,MAAM,GAAG,EAAE,SAAS,KAAK,CAAC;AACnE,sBAAkB,KAAK;AAAA,EACzB;AACA,QAAM,OAAO,MAAM;AACjB,QAAI,CAAC,YAAY,CAAC;AAChB;AACF,UAAM;AACN,uBAAmB;AACnB,cAAU;AACV,UAAM;AAAA,EACR;AACA,MAAI;AACF,SAAK;AACP,MAAI;AACF,UAAM,QAAQ,IAAI;AACpB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,IAAI;AAAA,EACN;AACF;AAEA,SAAS,aAAa,MAAM,eAAe,SAAS;AAClD,QAAM;AAAA,IACJ,QAAAR,UAAS;AAAA,EACX,IAAI,WAAW,OAAO,UAAU,CAAC;AACjC,QAAM,OAAO,IAAI,IAAI;AACrB,QAAM,SAAS,WAAW;AAC1B,QAAM,OAAO,IAAI,SAAS;AACxB,QAAI,CAAC,OAAO;AACV;AACF,WAAO,MAAM,YAAY,GAAG,IAAI;AAAA,EAClC;AACA,QAAM,YAAY,SAAS,aAAa;AACtC,QAAI,CAAC,OAAO;AACV;AACF,WAAO,MAAM,UAAU;AAAA,EACzB;AACA,MAAIA,SAAQ;AACV,QAAI,OAAO,SAAS;AAClB,aAAO,QAAQ,IAAI,OAAO,MAAM,aAAa;AAAA,aACtC,OAAO,SAAS;AACvB,aAAO,QAAQ,KAAK;AAAA;AAEpB,aAAO,QAAQ;AACjB,WAAO,MAAM,YAAY,CAAC,MAAM;AAC9B,WAAK,QAAQ,EAAE;AAAA,IACjB;AACA,sBAAkB,MAAM;AACtB,UAAI,OAAO;AACT,eAAO,MAAM,UAAU;AAAA,IAC3B,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,WAAW,MAAM,WAAW;AACnC,MAAI,KAAK,WAAW,KAAK,UAAU,WAAW;AAC5C,WAAO;AACT,QAAM,aAAa,KAAK,IAAI,CAAC,QAAQ,IAAI,GAAG,GAAG,EAAE,SAAS;AAC1D,QAAM,qBAAqB,UAAU,OAAO,CAAC,QAAQ,OAAO,QAAQ,UAAU,EAAE,IAAI,CAAC,OAAO;AAC1F,UAAM,MAAM,GAAG,SAAS;AACxB,QAAI,IAAI,KAAK,EAAE,WAAW,UAAU,GAAG;AACrC,aAAO;AAAA,IACT,OAAO;AACL,YAAM,OAAO,GAAG;AAChB,aAAO,SAAS,IAAI,MAAM,GAAG;AAAA,IAC/B;AAAA,EACF,CAAC,EAAE,KAAK,GAAG;AACX,QAAM,eAAe,iBAAiB,UAAU;AAChD,SAAO,GAAG,WAAW,KAAK,MAAM,KAAK,KAAK,YAAY,IAAI,kBAAkB;AAC9E;AAEA,SAAS,UAAU,UAAU;AAC3B,SAAO,CAAC,MAAM;AACZ,UAAM,eAAe,EAAE,KAAK,CAAC;AAC7B,WAAO,QAAQ,QAAQ,SAAS,MAAM,QAAQ,YAAY,CAAC,EAAE,KAAK,CAAC,WAAW;AAC5E,kBAAY,CAAC,WAAW,MAAM,CAAC;AAAA,IACjC,CAAC,EAAE,MAAM,CAAC,UAAU;AAClB,kBAAY,CAAC,SAAS,KAAK,CAAC;AAAA,IAC9B,CAAC;AAAA,EACH;AACF;AAEA,SAAS,oBAAoB,IAAI,MAAM,WAAW;AAChD,QAAM,WAAW,GAAG,WAAW,MAAM,SAAS,CAAC,gBAAgB,SAAS,KAAK,EAAE;AAC/E,QAAM,OAAO,IAAI,KAAK,CAAC,QAAQ,GAAG,EAAE,MAAM,kBAAkB,CAAC;AAC7D,QAAM,MAAM,IAAI,gBAAgB,IAAI;AACpC,SAAO;AACT;AAEA,SAAS,eAAe,IAAI,UAAU,CAAC,GAAG;AACxC,QAAM;AAAA,IACJ,eAAe,CAAC;AAAA,IAChB,oBAAoB,CAAC;AAAA,IACrB;AAAA,IACA,QAAAA,UAAS;AAAA,EACX,IAAI;AACJ,QAAM,SAAS,IAAI;AACnB,QAAM,eAAe,WAAW,SAAS;AACzC,QAAM,UAAU,IAAI,CAAC,CAAC;AACtB,QAAM,YAAY,WAAW;AAC7B,QAAM,kBAAkB,CAAC,SAAS,cAAc;AAC9C,QAAI,OAAO,SAAS,OAAO,MAAM,QAAQA,SAAQ;AAC/C,aAAO,MAAM,UAAU;AACvB,UAAI,gBAAgB,OAAO,MAAM,IAAI;AACrC,cAAQ,QAAQ,CAAC;AACjB,aAAO,QAAQ;AACf,MAAAA,QAAO,aAAa,UAAU,KAAK;AACnC,mBAAa,QAAQ;AAAA,IACvB;AAAA,EACF;AACA,kBAAgB;AAChB,oBAAkB,eAAe;AACjC,QAAM,iBAAiB,MAAM;AAC3B,UAAM,UAAU,oBAAoB,IAAI,cAAc,iBAAiB;AACvE,UAAM,YAAY,IAAI,OAAO,OAAO;AACpC,cAAU,OAAO;AACjB,cAAU,YAAY,CAAC,MAAM;AAC3B,YAAM,EAAE,UAAU,MAAM;AAAA,MACxB,GAAG,SAAS,MAAM;AAAA,MAClB,EAAE,IAAI,QAAQ;AACd,YAAM,CAAC,QAAQ,MAAM,IAAI,EAAE;AAC3B,cAAQ,QAAQ;AAAA,QACd,KAAK;AACH,kBAAQ,MAAM;AACd,0BAAgB,MAAM;AACtB;AAAA,QACF;AACE,iBAAO,MAAM;AACb,0BAAgB,OAAO;AACvB;AAAA,MACJ;AAAA,IACF;AACA,cAAU,UAAU,CAAC,MAAM;AACzB,YAAM,EAAE,SAAS,MAAM;AAAA,MACvB,EAAE,IAAI,QAAQ;AACd,QAAE,eAAe;AACjB,aAAO,CAAC;AACR,sBAAgB,OAAO;AAAA,IACzB;AACA,QAAI,SAAS;AACX,gBAAU,QAAQ;AAAA,QAChB,MAAM,gBAAgB,iBAAiB;AAAA,QACvC;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,aAAa,IAAI,WAAW,IAAI,QAAQ,CAAC,SAAS,WAAW;AACjE,QAAI;AACJ,YAAQ,QAAQ;AAAA,MACd;AAAA,MACA;AAAA,IACF;AACA,KAAC,KAAK,OAAO,UAAU,OAAO,SAAS,GAAG,YAAY,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;AACnE,iBAAa,QAAQ;AAAA,EACvB,CAAC;AACD,QAAM,WAAW,IAAI,WAAW;AAC9B,QAAI,aAAa,UAAU,WAAW;AACpC,cAAQ;AAAA,QACN;AAAA,MACF;AACA,aAAO,QAAQ,OAAO;AAAA,IACxB;AACA,WAAO,QAAQ,eAAe;AAC9B,WAAO,WAAW,GAAG,MAAM;AAAA,EAC7B;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,eAAe,UAAU,CAAC,GAAG;AACpC,QAAM,EAAE,QAAAA,UAAS,cAAc,IAAI;AACnC,MAAI,CAACA;AACH,WAAO,WAAW,KAAK;AACzB,QAAM,UAAU,WAAWA,QAAO,SAAS,SAAS,CAAC;AACrD,QAAM,kBAAkB,EAAE,SAAS,KAAK;AACxC,mBAAiBA,SAAQ,QAAQ,MAAM;AACrC,YAAQ,QAAQ;AAAA,EAClB,GAAG,eAAe;AAClB,mBAAiBA,SAAQ,SAAS,MAAM;AACtC,YAAQ,QAAQ;AAAA,EAClB,GAAG,eAAe;AAClB,SAAO;AACT;AAEA,SAAS,gBAAgB,UAAU,CAAC,GAAG;AACrC,QAAM,EAAE,QAAAA,UAAS,eAAe,GAAG,KAAK,IAAI;AAC5C,SAAO,UAAUA,SAAQ,IAAI;AAC/B;AAEA,SAAS,cAAc,UAAU,CAAC,GAAG;AACnC,QAAM;AAAA,IACJ,QAAAA,UAAS;AAAA,IACT,eAAe,OAAO;AAAA,IACtB,gBAAgB,OAAO;AAAA,IACvB,oBAAoB;AAAA,IACpB,mBAAmB;AAAA,IACnB,OAAO;AAAA,EACT,IAAI;AACJ,QAAM,QAAQ,WAAW,YAAY;AACrC,QAAM,SAAS,WAAW,aAAa;AACvC,QAAM,SAAS,MAAM;AACnB,QAAIA,SAAQ;AACV,UAAI,SAAS,SAAS;AACpB,cAAM,QAAQA,QAAO;AACrB,eAAO,QAAQA,QAAO;AAAA,MACxB,WAAW,SAAS,YAAYA,QAAO,gBAAgB;AACrD,cAAM,EAAE,OAAO,qBAAqB,QAAQ,sBAAsB,MAAM,IAAIA,QAAO;AACnF,cAAM,QAAQ,KAAK,MAAM,sBAAsB,KAAK;AACpD,eAAO,QAAQ,KAAK,MAAM,uBAAuB,KAAK;AAAA,MACxD,WAAW,kBAAkB;AAC3B,cAAM,QAAQA,QAAO;AACrB,eAAO,QAAQA,QAAO;AAAA,MACxB,OAAO;AACL,cAAM,QAAQA,QAAO,SAAS,gBAAgB;AAC9C,eAAO,QAAQA,QAAO,SAAS,gBAAgB;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACP,eAAa,MAAM;AACnB,QAAM,kBAAkB,EAAE,SAAS,KAAK;AACxC,mBAAiB,UAAU,QAAQ,eAAe;AAClD,MAAIA,WAAU,SAAS,YAAYA,QAAO,gBAAgB;AACxD,qBAAiBA,QAAO,gBAAgB,UAAU,QAAQ,eAAe;AAAA,EAC3E;AACA,MAAI,mBAAmB;AACrB,UAAM,UAAU,cAAc,yBAAyB;AACvD,UAAM,SAAS,MAAM,OAAO,CAAC;AAAA,EAC/B;AACA,SAAO,EAAE,OAAO,OAAO;AACzB;", "names": ["get", "set", "ref", "keys", "invoke", "toRef", "toRefs", "toValue", "window", "document", "timestamp", "navigator", "events", "getValue", "ref", "defaults", "toRef", "set", "onUpdated", "preventDefault", "toRefs", "now", "keys", "get", "is<PERSON><PERSON><PERSON>ly"]}